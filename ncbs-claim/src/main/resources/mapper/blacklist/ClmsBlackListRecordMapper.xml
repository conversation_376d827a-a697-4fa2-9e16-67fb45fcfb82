<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.blacklist.ClmsBlackListRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.blacklist.ClmsBlackListRecord">
        <id column="id" property="id" />
        <result column="black_list_id" property="blackListId" />
        <result column="report_no" property="reportNo" />
        <result column="case_times" property="caseTimes" />
        <result column="operate_type" property="operateType" />
        <result column="party_type" property="partyType" />
        <result column="entity_type" property="entityType" />
        <result column="party_name" property="partyName" />
        <result column="id_type" property="idType" />
        <result column="id_num" property="idNum" />
        <result column="risk_type" property="riskType" />
        <result column="phone_num" property="phoneNum" />
        <result column="valid_flag" property="validFlag" />
        <result column="black_source" property="blackSource" />
        <result column="audit_status" property="auditStatus" />
        <result column="remark" property="remark" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
        <result column="related_report_no" property="relatedReportNo" />
    </resultMap>

    <!--根据black_list_id查询 -->
    <select id="getByBlackListId" resultMap="BaseResultMap">
        SELECT id,
        black_list_id,
        report_no,
        case_times,
        operate_type,
        party_type,
        entity_type,
        party_name,
        id_type,
        id_num,
        risk_type,
        phone_num,
        valid_flag,
        black_source,
        audit_status,
        remark,
        created_by,
        sys_ctime,
        updated_by,
        sys_utime,
        related_report_no
        FROM clms_black_list_record
        WHERE black_list_id = #{blackListId}
    </select>

    <!--新增数据 -->
    <insert id="saveClmsBlackListRecord" parameterType="com.paic.ncbs.claim.dao.entity.blacklist.ClmsBlackListRecord">
        INSERT INTO clms_black_list_record (
        id,
        black_list_id,
        report_no,
        case_times,
        operate_type,
        party_type,
        entity_type,
        party_name,
        id_type,
        id_num,
        risk_type,
        phone_num,
        valid_flag,
        black_source,
        audit_status,
        remark,
        related_report_no,
        created_by,
        sys_ctime,
        updated_by,
        sys_utime
        ) VALUES (
        replace(UUID(), '-', ''),
        #{blackListId},
        #{reportNo},
        #{caseTimes},
        #{operateType},
        #{partyType},
        #{entityType},
        #{partyName},
        #{idType},
        #{idNum},
        #{riskType},
        #{phoneNum},
        #{validFlag},
        #{blackSource},
        #{auditStatus},
        #{remark},
        #{relatedReportNo},
        #{createdBy},
        NOW(),
        #{updatedBy},
        NOW()
        )
    </insert>

    <!--根据black_list_id物理删除 -->
    <delete id="deleteByBlackListId">
        DELETE FROM clms_black_list_record
        WHERE black_list_id = #{blackListId}
    </delete>

    <select id="countByNameAndCertNo" resultType="int">
        SELECT COUNT(1)
        FROM clms_black_list_record
        WHERE party_name = #{partyName}
        <choose>
            <when test="idNum != null and idNum != ''">
                AND id_num = #{idNum}
            </when>
            <otherwise>
                AND (id_num IS NULL OR id_num = '')
            </otherwise>
        </choose>
    </select>

</mapper>
