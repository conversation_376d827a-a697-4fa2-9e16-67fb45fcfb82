<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.estimate.EstimateIntermediateDataMapper">


    <insert id="insert"  parameterType="com.paic.ncbs.claim.model.dto.estimate.EstimateIntermediateData">
      insert into clms_estimate_intermediate_data (id,policy_no, report_no, case_no,
      case_times, plan_code, duty_code ,estimate_flag,estimate_amount,estimate_amount_change,estimate_date,
        created_by, created_date, updated_by, updated_date)
      values (#{id},#{policyNo}, #{reportNo}, #{caseNo},
        #{caseTimes}, #{planCode}, #{dutyCode}, #{estimateFlag},#{estimateAmount},#{estimateAmountChange},#{estimateDate},
        #{createdBy}, now(), #{updatedBy},now())
    </insert>

    <insert id="batchInsert"  parameterType="java.util.List" >
        insert into clms_estimate_intermediate_data (id,policy_no, report_no, case_no,
        case_times, plan_code, duty_code ,estimate_flag,estimate_amount,estimate_amount_change,estimate_date,
        created_by, created_date, updated_by, updated_date)
        values
        <foreach collection="estimateIntermediateDataList" item="data" separator=",">
            (#{data.id},#{data.policyNo}, #{data.reportNo}, #{data.caseNo},
            #{data.caseTimes}, #{data.planCode}, #{data.dutyCode},
            #{data.estimateFlag},#{data.estimateAmount},#{data.estimateAmountChange},#{data.estimateDate},
            #{data.createdBy}, now(), #{data.updatedBy}, now())
        </foreach>
    </insert>

    <select id="queryListByCondition"
            resultType="com.paic.ncbs.claim.model.dto.estimate.EstimateIntermediateData">
      select policy_no,
        report_no,
        case_no,
        case_times,
        plan_code,
        duty_code ,
        estimate_flag ,
        estimate_amount,
        estimate_amount_change,
        estimate_date,
        updated_date
      from clms_estimate_intermediate_data
      where report_no = #{reportNo}
    </select>

</mapper>