<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.estimate.EstimatePlanMapper">

	<resultMap type="com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO" id="estimatePlanDTO">
		<id column="ID_AHCS_ESTIMATE_PLAN" property="idAhcsEstimatePlan"/>
		<result column="ID_AHCS_ESTIMATE_POLICY" property="idAhcsEstimatePolicy"/>
		<result column="PLAN_CODE" property="planCode"/>
		<result column="PLAN_NAME" property="planName"/>
		<result column="ORG_PLAN_CODE" property="orgPlanCode"/>
		<result column="ORG_PLAN_NAME" property="orgPlanName"/>
		<result column="ESTIMATE_AMOUNT" property="estimateAmount"/>
		<result column="ARBITRAGE_FEE" property="arbitrageFee"/>
		<result column="LAWSUIT_FEE" property="lawsuitFee"/>
		<result column="COMMON_ESTIMATE_FEE" property="commonEstimateFee"/>
		<result column="LAWYER_FEE" property="lawyerFee"/>
		<result column="EXECUTE_FEE" property="executeFee"/>
		<result column="VERIFY_APPRAISE_FEE" property="verifyAppraiseFee"/>
		<result column="INQUIRE_FEE" property="inquireFee"/>
		<result column="OTHER_FEE" property="otherFee"/>
		<result column="SPECIAL_SURVEY_FEE" property="specialSurveyFee"/>
		<result column="CASE_NO" property="caseNo"/>
		<result column="CASE_TIMES" property="caseTimes"/>
		<result column="POLICY_NO" property="policyNo"/>
		<result column="IS_MAIN" property="isMain"/>
		<result column="GROUP_CODE" property="groupCode"/>
		<result column="ID_PLY_RISK_PROPERTY" property="idPlyRiskProperty"/>
		<result column="risk_group_no" property="riskGroupNo"/>
		<result column="risk_group_name" property="riskGroupName"/>

		<!-- 关联预估的责任信息-->
		<collection property="estimateDutyRecordList" column="{planCode=PLAN_CODE,caseNo=CASE_NO,caseTimes=CASE_TIMES}" javaType="ArrayList"
					ofType="com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO"
					select="com.paic.ncbs.claim.dao.mapper.estimate.EstimateDutyRecordMapper.getEstimateDutyRecordList"/>
	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO" id="estimatePlanForRestartDTO">
		<id column="ID_AHCS_ESTIMATE_PLAN" property="idAhcsEstimatePlan"/>
		<result column="ID_AHCS_ESTIMATE_POLICY" property="idAhcsEstimatePolicy"/>
		<result column="PLAN_CODE" property="planCode"/>
		<result column="PLAN_NAME" property="planName"/>
		<result column="ORG_PLAN_CODE" property="orgPlanCode"/>
		<result column="ORG_PLAN_NAME" property="orgPlanName"/>
		<result column="ESTIMATE_AMOUNT" property="estimateAmount"/>
		<result column="ARBITRAGE_FEE" property="arbitrageFee"/>
		<result column="LAWSUIT_FEE" property="lawsuitFee"/>
		<result column="COMMON_ESTIMATE_FEE" property="commonEstimateFee"/>
		<result column="LAWYER_FEE" property="lawyerFee"/>
		<result column="EXECUTE_FEE" property="executeFee"/>
		<result column="VERIFY_APPRAISE_FEE" property="verifyAppraiseFee"/>
		<result column="INQUIRE_FEE" property="inquireFee"/>
		<result column="OTHER_FEE" property="otherFee"/>
		<result column="SPECIAL_SURVEY_FEE" property="specialSurveyFee"/>
		<result column="CASE_NO" property="caseNo"/>
		<result column="CASE_TIMES" property="caseTimes"/>
		<result column="POLICY_NO" property="policyNo"/>
		<result column="IS_MAIN" property="isMain"/>
		<result column="GROUP_CODE" property="groupCode"/>

		<!-- 关联预估的责任信息-->
		<collection property="estimateDutyRecordList" column="{planCode=PLAN_CODE,caseNo=CASE_NO,caseTimes=CASE_TIMES}" javaType="ArrayList"
					ofType="com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO"
					select="com.paic.ncbs.claim.dao.mapper.estimate.EstimateDutyRecordMapper.getEstimateDutyRecordForRestartList"/>
	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO" id="getEstimateDutyListMap">
		<id column="ID_AHCS_ESTIMATE_PLAN" property="idAhcsEstimatePlan"/>
		<result column="ID_AHCS_ESTIMATE_POLICY" property="idAhcsEstimatePolicy"/>
		<result column="PLAN_CODE" property="planCode"/>
		<result column="PLAN_NAME" property="planName"/>
		<result column="ORG_PLAN_CODE" property="orgPlanCode"/>
		<result column="ORG_PLAN_NAME" property="orgPlanName"/>
		<result column="ESTIMATE_AMOUNT" property="estimateAmount"/>
		<result column="ARBITRAGE_FEE" property="arbitrageFee"/>
		<result column="LAWSUIT_FEE" property="lawsuitFee"/>
		<result column="COMMON_ESTIMATE_FEE" property="commonEstimateFee"/>
		<result column="LAWYER_FEE" property="lawyerFee"/>
		<result column="EXECUTE_FEE" property="executeFee"/>
		<result column="VERIFY_APPRAISE_FEE" property="verifyAppraiseFee"/>
		<result column="INQUIRE_FEE" property="inquireFee"/>
		<result column="OTHER_FEE" property="otherFee"/>
		<result column="SPECIAL_SURVEY_FEE" property="specialSurveyFee"/>
		<result column="CASE_NO" property="caseNo"/>
		<result column="CASE_TIMES" property="caseTimes"/>
		<result column="POLICY_NO" property="policyNo"/>
		<result column="GROUP_CODE" property="groupCode"/>
		<result column="id_ply_risk_property" property="idPlyRiskProperty"/>

		<!-- 关联责任信息-->
		<collection property="estimateDutyList" column="ID_AHCS_ESTIMATE_PLAN" javaType="ArrayList"
					ofType="com.paic.ncbs.claim.model.dto.estimate.EstimateDutyDTO"
					select="com.paic.ncbs.claim.dao.mapper.estimate.EstimateDutyMapper.getByPlanID"/>
	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO" id="estimateDutyListMap">
		<id column="ID_AHCS_ESTIMATE_PLAN" property="idAhcsEstimatePlan"/>
		<result column="ID_AHCS_ESTIMATE_POLICY" property="idAhcsEstimatePolicy"/>
		<result column="PLAN_CODE" property="planCode"/>
		<result column="PLAN_NAME" property="planName"/>
		<result column="ESTIMATE_AMOUNT" property="estimateAmount"/>
		<result column="ARBITRAGE_FEE" property="arbitrageFee"/>
		<result column="LAWSUIT_FEE" property="lawsuitFee"/>
		<result column="COMMON_ESTIMATE_FEE" property="commonEstimateFee"/>
		<result column="LAWYER_FEE" property="lawyerFee"/>
		<result column="EXECUTE_FEE" property="executeFee"/>
		<result column="VERIFY_APPRAISE_FEE" property="verifyAppraiseFee"/>
		<result column="INQUIRE_FEE" property="inquireFee"/>
		<result column="OTHER_FEE" property="otherFee"/>
		<result column="SPECIAL_SURVEY_FEE" property="specialSurveyFee"/>
		<result column="CASE_NO" property="caseNo"/>
		<result column="CASE_TIMES" property="caseTimes"/>
		<result column="POLICY_NO" property="policyNo"/>
		<result column="GROUP_CODE" property="groupCode"/>

		<!-- 关联责任信息-->
		<collection property="estimateDutyHistoryList" column="{planCode=PLAN_CODE,caseNo=CASE_NO,caseTimes=CASE_TIMES}" javaType="ArrayList"
					ofType="com.paic.ncbs.claim.model.dto.estimate.EstimateDutyHistoryDTO"
					select="com.paic.ncbs.claim.dao.mapper.estimate.EstimateDutyHistoryMapper.getEstimateDutyHistoryList"/>
	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO" id="estimatePlanDTOFromCopy">
		<id column="ID_AHCS_ESTIMATE_PLAN" property="idAhcsEstimatePlan"/>
		<result column="ID_AHCS_ESTIMATE_POLICY" property="idAhcsEstimatePolicy"/>
		<result column="PLAN_CODE" property="planCode"/>
		<result column="PLAN_NAME" property="planName"/>
		<result column="ORG_PLAN_CODE" property="orgPlanCode"/>
		<result column="ORG_PLAN_NAME" property="orgPlanName"/>
		<result column="IS_MAIN" property="isMain"/>
		<result column="GROUP_CODE" property="groupCode"/>
		<result column="ID_PLY_RISK_GROUP" property="idPlyRiskGroup"/>
		<result column="risk_group_no" property="riskGroupNo"/>
		<result column="risk_group_name" property="riskGroupName"/>

	<!-- 关联责任信息-->
	<collection property="estimateDutyList" column="ID_AHCS_ESTIMATE_PLAN" javaType="ArrayList"
				ofType="com.paic.ncbs.claim.model.dto.estimate.EstimateDutyDTO"
				select="com.paic.ncbs.claim.dao.mapper.estimate.EstimateDutyMapper.getDutyFormCopy"/>
	</resultMap>
 
    <!-- 获取预估险种金额表 -->
    <select id="getByPolicyID" parameterType="java.lang.String" resultMap="estimatePlanDTO">
          SELECT
	            p.ID_AHCS_ESTIMATE_PLAN   ,
				p.ID_AHCS_ESTIMATE_POLICY ,
				p.PLAN_CODE               ,
				p.PLAN_NAME               ,
				(select t1.org_plan_code from CLMS_policy_info t, CLMS_policy_plan t1
					where t.id_ahcs_policy_info=t1.id_ahcs_policy_info 
					and t.case_no=p.CASE_NO and t1.PLAN_CODE=p.PLAN_CODE limit 1)as ORG_PLAN_CODE,
				(select t1.org_plan_name from CLMS_policy_info t, CLMS_policy_plan t1
					where t.id_ahcs_policy_info=t1.id_ahcs_policy_info 
					and t.case_no=p.CASE_NO and t1.PLAN_CODE=p.PLAN_CODE limit 1)as ORG_PLAN_NAME,
				p.ESTIMATE_AMOUNT ,
				p.ARBITRAGE_FEE ,
				p.LAWSUIT_FEE ,
				p.COMMON_ESTIMATE_FEE ,
				p.LAWYER_FEE ,
				p.EXECUTE_FEE ,
				p.VERIFY_APPRAISE_FEE ,
				p.INQUIRE_FEE ,
                p.OTHER_FEE,
                p.SPECIAL_SURVEY_FEE,
				p.CASE_NO ,
				p.CASE_TIMES ,
				p.POLICY_NO ,
				p.IS_MAIN ,
				p.GROUP_CODE,
				p.id_ply_risk_property,
				p.risk_group_no,
				p.risk_group_name
          from CLMS_ESTIMATE_PLAN p
          where p.ID_AHCS_ESTIMATE_POLICY=#{idAhcsEstimatePolicy,jdbcType=VARCHAR}
          ORDER BY p.PLAN_CODE ,p.GROUP_CODE
    </select>
    
    <!-- 获取预估险种金额表 -->
    <select id="getPlanByPolicyID" parameterType="java.lang.String" resultMap="getEstimateDutyListMap">
          SELECT
				p.ID_AHCS_ESTIMATE_PLAN   ,
				p.ID_AHCS_ESTIMATE_POLICY ,
				p.PLAN_CODE   ,
				p.PLAN_NAME   ,
				(select t1.org_plan_code from CLMS_policy_info t, CLMS_policy_plan t1
					where t.id_ahcs_policy_info=t1.id_ahcs_policy_info 
					and t.case_no=p.CASE_NO and t1.PLAN_CODE=p.PLAN_CODE and t.policy_no=p.policy_NO )as ORG_PLAN_CODE,
				(select t1.org_plan_name from CLMS_policy_info t, CLMS_policy_plan t1
					where t.id_ahcs_policy_info=t1.id_ahcs_policy_info 
					and t.case_no=p.CASE_NO and t1.PLAN_CODE=p.PLAN_CODE and t.policy_no=p.policy_NO)as ORG_PLAN_NAME,
				p.ESTIMATE_AMOUNT   ,
				p.ARBITRAGE_FEE  ,
				p.LAWSUIT_FEE  ,
				p.COMMON_ESTIMATE_FEE  ,
				p.LAWYER_FEE ,
				p.EXECUTE_FEE  ,
				p.VERIFY_APPRAISE_FEE  ,
				p.INQUIRE_FEE ,
                p.OTHER_FEE,
                p.SPECIAL_SURVEY_FEE,
				p.CASE_NO ,
				p.CASE_TIMES  ,
				p.POLICY_NO ,
				p.GROUP_CODE,
				p.id_ply_risk_property
          from CLMS_ESTIMATE_PLAN p
          where p.ID_AHCS_ESTIMATE_POLICY=#{idAhcsEstimatePolicy,jdbcType=VARCHAR}
          ORDER BY p.GROUP_CODE
    </select>
    
     <!-- 获取预估险种金额表 -->
    <select id="getPlanListByPolicyID" parameterType="java.lang.String" resultMap="estimateDutyListMap">
          SELECT
	            p.ID_AHCS_ESTIMATE_PLAN   ,
				p.ID_AHCS_ESTIMATE_POLICY ,
				p.PLAN_CODE ,
				p.PLAN_NAME ,
				p.ESTIMATE_AMOUNT ,
				p.ARBITRAGE_FEE ,
				p.LAWSUIT_FEE ,
				p.COMMON_ESTIMATE_FEE ,
				p.LAWYER_FEE ,
				p.EXECUTE_FEE ,
				p.VERIFY_APPRAISE_FEE  ,
				p.INQUIRE_FEE ,
				p.OTHER_FEE,
				p.SPECIAL_SURVEY_FEE,
				p.CASE_NO ,
				p.CASE_TIMES ,
				p.POLICY_NO ,
                p.GROUP_CODE
        from CLMS_ESTIMATE_PLAN p
          where p.ID_AHCS_ESTIMATE_POLICY=#{idAhcsEstimatePolicy,jdbcType=VARCHAR}
    </select>
    
    <select id="getPlanFormCopy" resultMap="estimatePlanDTOFromCopy">
		 select pp.id_ahcs_policy_info ID_AHCS_ESTIMATE_POLICY,
			 	pp.plan_code ,
		        pp.plan_name ,
		        pp.org_plan_code,
		        pp.org_plan_name,
		        pp.id_ahcs_policy_plan ID_AHCS_ESTIMATE_PLAN,
		        pp.IS_MAIN,
		        pp.GROUP_CODE,
		        pp.id_ply_risk_group,
		        pp.risk_group_no,
		        pp.risk_group_name
	    from CLMS_policy_plan pp
	    where pp.id_ahcs_policy_info = #{idAhcsEstimatePolicy, jdbcType = VARCHAR}
	</select>
    
    <!-- 循环新增EstimatePlan -->
    <insert id="addBatchEstimatePlan" parameterType="java.util.List">
	    	  insert into CLMS_estimate_plan
		          (
		          CREATED_BY,           
		          CREATED_DATE,         
		          UPDATED_BY,           
		          UPDATED_DATE,          
		          ID_AHCS_ESTIMATE_PLAN, 
		          ID_AHCS_ESTIMATE_POLICY,
		          PLAN_CODE,          
		          PLAN_NAME,
		          ESTIMATE_AMOUNT,
				  VERIFY_APPRAISE_FEE  ,
				  INQUIRE_FEE ,
				  OTHER_FEE,
				  SPECIAL_SURVEY_FEE,
				  CASE_NO,
				  CASE_TIMES,
				  POLICY_NO,
				  IS_MAIN,
				  GROUP_CODE,
				  ARCHIVE_TIME,
				  id_ply_risk_property,
			      risk_group_no,
				  risk_group_name
				 )
		<foreach collection="paramList" index="index" item="item" open="(" separator="union all" close=")">
				select
		          #{item.createdBy,jdbcType=VARCHAR},	
		          sysdate(),
		          #{item.updatedBy,jdbcType=VARCHAR},
		          sysdate(),
		          #{item.idAhcsEstimatePlan, jdbcType=VARCHAR},
		          #{item.idAhcsEstimatePolicy,jdbcType=VARCHAR},
		          #{item.planCode,jdbcType=VARCHAR},
		          #{item.planName,jdbcType=VARCHAR},
		          #{item.estimateAmount,jdbcType=DECIMAL},
					#{item.verifyAppraiseFee,jdbcType=NUMERIC},
					#{item.inquireFee,jdbcType=NUMERIC},
					#{item.otherFee,jdbcType=NUMERIC},
					#{item.specialSurveyFee,jdbcType=NUMERIC},
		          #{item.caseNo,jdbcType=VARCHAR},
		          #{item.caseTimes,jdbcType=INTEGER},
		          #{item.policyNo,jdbcType=VARCHAR},
		          #{item.isMain,jdbcType=VARCHAR},
		          #{item.groupCode,jdbcType=VARCHAR},
			       sysdate(),
				  #{item.idPlyRiskProperty,jdbcType=VARCHAR},
				  #{item.riskGroupNo},
				  #{item.riskGroupName}
				FROM DUAL
        </foreach>
    </insert>
    
     <!-- 更新预估险种金额表相关费用金额 -->
     <update id="modifyBatchEstimatePlan" parameterType="java.util.List">
	      <foreach collection="paramList" index="index" item="item" separator=";" >
	     	update CLMS_estimate_plan d
			   set d.UPDATED_BY = #{item.updatedBy,jdbcType=VARCHAR},
			   	   d.ESTIMATE_AMOUNT = #{item.estimateAmount,jdbcType=NUMERIC},
				   d.ARBITRAGE_FEE = #{item.arbitrageFee,jdbcType=NUMERIC},
				   d.LAWSUIT_FEE = #{item.lawsuitFee,jdbcType=NUMERIC},
			       d.COMMON_ESTIMATE_FEE = #{item.commonEstimateFee,jdbcType=NUMERIC},
				   d.LAWYER_FEE = #{item.lawyerFee,jdbcType=NUMERIC},
			       d.EXECUTE_FEE = #{item.executeFee,jdbcType=NUMERIC},
			       d.VERIFY_APPRAISE_FEE = #{item.verifyAppraiseFee,jdbcType=NUMERIC},
			       d.INQUIRE_FEE = #{item.inquireFee,jdbcType=NUMERIC},
			       d.OTHER_FEE = #{item.otherFee,jdbcType=NUMERIC},
			       d.SPECIAL_SURVEY_FEE = #{item.specialSurveyFee,jdbcType=NUMERIC},
		           d.UPDATED_DATE = now()
		     where d.ID_AHCS_ESTIMATE_PLAN = #{item.idAhcsEstimatePlan,jdbcType=VARCHAR}
	       </foreach>   
     </update>
     
     <!-- 根据保单主键List查找出一个List<EstimatePlanDTO> -->
     <select id="getListByPolicyId" parameterType="java.util.List" resultMap="estimatePlanDTO">
    		SELECT
		            p.ID_AHCS_ESTIMATE_PLAN   ,
					p.ID_AHCS_ESTIMATE_POLICY ,
				    p.CASE_NO                 ,  
				    p.CASE_TIMES              ,
				    p.POLICY_NO				  ,
					p.PLAN_CODE               ,
					p.PLAN_NAME               ,
					p.ESTIMATE_AMOUNT         ,
					p.ARBITRAGE_FEE      	  ,
					p.LAWSUIT_FEE         	  ,
					p.COMMON_ESTIMATE_FEE  	  ,
					p.LAWYER_FEE       		  ,
					p.EXECUTE_FEE      		  ,
					p.VERIFY_APPRAISE_FEE  ,
					p.INQUIRE_FEE ,
					p.OTHER_FEE,
					p.SPECIAL_SURVEY_FEE,
					p.IS_MAIN				  ,
					p.GROUP_CODE               
	          from CLMS_ESTIMATE_PLAN p
	          where p.ID_AHCS_ESTIMATE_POLICY in
		 <foreach collection="paramList" item="item" open="(" separator="," close=")">
			 #{item,jdbcType = VARCHAR}
		 </foreach>
     </select>

	<!-- 根据赔案号和赔付次数和险种编码批量更新未决险种数据的金额 -->
	 <update id="modifyPlanAmount" parameterType="java.util.List">
	 	<foreach collection="planList" index="index" item="item" separator=";" >
	     	update CLMS_estimate_plan d
			   set d.UPDATED_BY = #{item.updatedBy,jdbcType=VARCHAR},
			   	   d.ESTIMATE_AMOUNT = #{item.estimateAmount,jdbcType=NUMERIC},
				   d.ARBITRAGE_FEE = #{item.arbitrageFee,jdbcType=NUMERIC},
				   d.LAWSUIT_FEE = #{item.lawsuitFee,jdbcType=NUMERIC},
			       d.COMMON_ESTIMATE_FEE = #{item.commonEstimateFee,jdbcType=NUMERIC},
				   d.LAWYER_FEE = #{item.lawyerFee,jdbcType=NUMERIC},
			       d.EXECUTE_FEE = #{item.executeFee,jdbcType=NUMERIC},
				   d.VERIFY_APPRAISE_FEE = #{item.verifyAppraiseFee,jdbcType=NUMERIC},
				   d.INQUIRE_FEE = #{item.inquireFee,jdbcType=NUMERIC},
				   d.OTHER_FEE = #{item.otherFee,jdbcType=NUMERIC},
				   d.SPECIAL_SURVEY_FEE = #{item.specialSurveyFee,jdbcType=NUMERIC},
				   d.UPDATED_DATE = now()
		     where d.CASE_NO= #{item.caseNo, jdbcType=VARCHAR}
		       and d.CASE_TIMES = #{item.caseTimes, jdbcType=INTEGER}
		       and d.PLAN_CODE = #{item.planCode, jdbcType=VARCHAR}
	       </foreach>   
	 </update>

	<!--删除立案数据 紧小微案件-->
	<delete id="delEstimatePlanDataByCaseNo">
		DELETE FROM CLMS_ESTIMATE_PLAN T
		WHERE T.CASE_NO=#{caseNo, jdbcType=VARCHAR} 
		AND T.CASE_TIMES=#{caseTimes, jdbcType=INTEGER}
	</delete>

	<select id="getByPolicyIDForRestart" parameterType="java.lang.String" resultMap="estimatePlanForRestartDTO">
		SELECT
		p.ID_AHCS_ESTIMATE_PLAN   ,
		p.ID_AHCS_ESTIMATE_POLICY ,
		p.PLAN_CODE               ,
		p.PLAN_NAME               ,
		(select t1.org_plan_code from CLMS_policy_info t, CLMS_policy_plan t1
		where t.id_ahcs_policy_info=t1.id_ahcs_policy_info
		and t.case_no=p.CASE_NO and t1.PLAN_CODE=p.PLAN_CODE limit 1)as ORG_PLAN_CODE,
		(select t1.org_plan_name from CLMS_policy_info t, CLMS_policy_plan t1
		where t.id_ahcs_policy_info=t1.id_ahcs_policy_info
		and t.case_no=p.CASE_NO and t1.PLAN_CODE=p.PLAN_CODE limit 1)as ORG_PLAN_NAME,
		p.ESTIMATE_AMOUNT ,
		p.ARBITRAGE_FEE ,
		p.LAWSUIT_FEE ,
		p.COMMON_ESTIMATE_FEE ,
		p.LAWYER_FEE ,
		p.EXECUTE_FEE ,
		p.VERIFY_APPRAISE_FEE ,
		p.INQUIRE_FEE ,
		p.OTHER_FEE,
		p.SPECIAL_SURVEY_FEE,
		p.CASE_NO ,
		p.CASE_TIMES ,
		p.POLICY_NO ,
		p.IS_MAIN ,
		p.GROUP_CODE
		from CLMS_ESTIMATE_PLAN p
		where p.ID_AHCS_ESTIMATE_POLICY=#{idAhcsEstimatePolicy,jdbcType=VARCHAR}
		ORDER BY p.PLAN_CODE ,p.GROUP_CODE
	</select>

	<insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO">
		INSERT INTO CLMS_ESTIMATE_PLAN (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_ESTIMATE_PLAN,
			ID_AHCS_ESTIMATE_POLICY,
			CASE_NO,
			CASE_TIMES,
			POLICY_NO,
			PLAN_CODE,
			PLAN_NAME,
			ESTIMATE_AMOUNT,
			ARBITRAGE_FEE,
			LAWSUIT_FEE,
			COMMON_ESTIMATE_FEE,
			LAWYER_FEE,
			EXECUTE_FEE,
			VERIFY_APPRAISE_FEE,
			IS_MAIN,
			GROUP_CODE,
			ARCHIVE_TIME,
			INQUIRE_FEE,
			OTHER_FEE,
			SPECIAL_SURVEY_FEE
		)
		<foreach collection="paramList" item="item" open="(" separator="union all" close=")">
			SELECT
				#{item.reopenUserId},
				NOW(),
				#{item.reopenUserId},
				NOW(),
				#{item.reopenEstimatePlanId},
				#{item.idAhcsEstimatePolicy},
				CASE_NO,
				#{item.reopenCaseTimes},
				POLICY_NO,
				PLAN_CODE,
				PLAN_NAME,
				ESTIMATE_AMOUNT,
				ARBITRAGE_FEE,
				LAWSUIT_FEE,
				COMMON_ESTIMATE_FEE,
				LAWYER_FEE,
				EXECUTE_FEE,
				VERIFY_APPRAISE_FEE,
				IS_MAIN,
				GROUP_CODE,
				NOW(),
				INQUIRE_FEE,
				OTHER_FEE,
				SPECIAL_SURVEY_FEE
				FROM CLMS_ESTIMATE_PLAN
			WHERE ID_AHCS_ESTIMATE_PLAN = #{item.idAhcsEstimatePlan}
		</foreach>
	</insert>

	<delete id="delEstimatePlanByCaseNo">
		delete from CLMS_ESTIMATE_PLAN
		WHERE CASE_NO in (
		<foreach collection="caseNoList" item="item" separator=",">
			#{item}
		</foreach>
		)
	    AND CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
	</delete>
</mapper>