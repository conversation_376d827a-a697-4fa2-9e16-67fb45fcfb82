<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.estimate.PackageInfoEntityMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.estimate.PackageInfoEntity">
        <id column="ID_PACKAGE_INFO" property="idPackageInfo" jdbcType="VARCHAR"/>
        <result column="ID_MARKETPRODUCT_INFO" property="idMarketproductInfo" jdbcType="VARCHAR"/>
        <result column="PACKAGE_CODE" property="packageCode" jdbcType="VARCHAR"/>
        <result column="PACKAGE_NAME" property="packageName" jdbcType="VARCHAR"/>
        <result column="INVALIDATE_DATE" property="invalidateDate" jdbcType="TIMESTAMP"/>
        <result column="EFFECTIVE_DATE" property="effectiveDate" jdbcType="TIMESTAMP"/>
        <result column="STATUS" property="status" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="TARGET_TYPE" property="targetType" jdbcType="VARCHAR"/>
        <result column="TOTAL_COMPENSATION_MAX_AMOUNT" property="totalCompensationMaxAmount" jdbcType="DECIMAL"/>
        <result column="COMPENSATION_MAX_AMOUNT" property="compensationMaxAmount" jdbcType="DECIMAL"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID_PACKAGE_INFO, ID_MARKETPRODUCT_INFO, PACKAGE_CODE, PACKAGE_NAME, INVALIDATE_DATE,
        EFFECTIVE_DATE, STATUS, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, TARGET_TYPE,
        TOTAL_COMPENSATION_MAX_AMOUNT, COMPENSATION_MAX_AMOUNT
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from BASE_PACKAGE_INFO
        where ID_PACKAGE_INFO = #{idPackageInfo}
    </select>
    <select id="getPackageInfoByCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from BASE_PACKAGE_INFO
        where PACKAGE_CODE = #{packageCode}
        and ID_MARKETPRODUCT_INFO =#{productId}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from BASE_PACKAGE_INFO
        where ID_PACKAGE_INFO = #{idPackageInfo}
    </delete>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.estimate.PackageInfoEntity">
        insert into BASE_PACKAGE_INFO (ID_PACKAGE_INFO, ID_MARKETPRODUCT_INFO,
        PACKAGE_CODE, PACKAGE_NAME, INVALIDATE_DATE,
        EFFECTIVE_DATE, STATUS, CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        TARGET_TYPE, TOTAL_COMPENSATION_MAX_AMOUNT,
        COMPENSATION_MAX_AMOUNT)
        values (#{idPackageInfo}, #{idMarketproductInfo},
        #{packageCode}, #{packageName}, #{invalidateDate},
        #{effectiveDate}, #{status}, #{createdBy},
        #{createdDate}, #{updatedBy}, #{updatedDate},
        #{targetType}, #{totalCompensationMaxAmount},
        #{compensationMaxAmount})
    </insert>
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.estimate.PackageInfoEntity">
        insert into BASE_PACKAGE_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idPackageInfo != null">
                ID_PACKAGE_INFO,
            </if>
            <if test="idMarketproductInfo != null">
                ID_MARKETPRODUCT_INFO,
            </if>
            <if test="packageCode != null">
                PACKAGE_CODE,
            </if>
            <if test="packageName != null">
                PACKAGE_NAME,
            </if>
            <if test="invalidateDate != null">
                INVALIDATE_DATE,
            </if>
            <if test="effectiveDate != null">
                EFFECTIVE_DATE,
            </if>
            <if test="status != null">
                STATUS,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="targetType != null">
                TARGET_TYPE,
            </if>
            <if test="totalCompensationMaxAmount != null">
                TOTAL_COMPENSATION_MAX_AMOUNT,
            </if>
            <if test="compensationMaxAmount != null">
                COMPENSATION_MAX_AMOUNT,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idPackageInfo != null">
                #{idPackageInfo},
            </if>
            <if test="idMarketproductInfo != null">
                #{idMarketproductInfo},
            </if>
            <if test="packageCode != null">
                #{packageCode},
            </if>
            <if test="packageName != null">
                #{packageName},
            </if>
            <if test="invalidateDate != null">
                #{invalidateDate},
            </if>
            <if test="effectiveDate != null">
                #{effectiveDate},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="createdBy != null">
                #{createdBy},
            </if>
            <if test="createdDate != null">
                #{createdDate},
            </if>
            <if test="updatedBy != null">
                #{updatedBy},
            </if>
            <if test="updatedDate != null">
                #{updatedDate},
            </if>
            <if test="targetType != null">
                #{targetType},
            </if>
            <if test="totalCompensationMaxAmount != null">
                #{totalCompensationMaxAmount},
            </if>
            <if test="compensationMaxAmount != null">
                #{compensationMaxAmount},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.estimate.PackageInfoEntity">
        update BASE_PACKAGE_INFO
        <set>
            <if test="idMarketproductInfo != null">
                ID_MARKETPRODUCT_INFO = #{idMarketproductInfo},
            </if>
            <if test="packageCode != null">
                PACKAGE_CODE = #{packageCode},
            </if>
            <if test="packageName != null">
                PACKAGE_NAME = #{packageName},
            </if>
            <if test="invalidateDate != null">
                INVALIDATE_DATE = #{invalidateDate},
            </if>
            <if test="effectiveDate != null">
                EFFECTIVE_DATE = #{effectiveDate},
            </if>
            <if test="status != null">
                STATUS = #{status},
            </if>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate},
            </if>
            <if test="targetType != null">
                TARGET_TYPE = #{targetType},
            </if>
            <if test="totalCompensationMaxAmount != null">
                TOTAL_COMPENSATION_MAX_AMOUNT = #{totalCompensationMaxAmount},
            </if>
            <if test="compensationMaxAmount != null">
                COMPENSATION_MAX_AMOUNT = #{compensationMaxAmount},
            </if>
        </set>
        where ID_PACKAGE_INFO = #{idPackageInfo}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.estimate.PackageInfoEntity">
        update BASE_PACKAGE_INFO
        set ID_MARKETPRODUCT_INFO = #{idMarketproductInfo},
        PACKAGE_CODE = #{packageCode},
        PACKAGE_NAME = #{packageName},
        INVALIDATE_DATE = #{invalidateDate},
        EFFECTIVE_DATE = #{effectiveDate},
        STATUS = #{status},
        CREATED_BY = #{createdBy},
        CREATED_DATE = #{createdDate},
        UPDATED_BY = #{updatedBy},
        UPDATED_DATE = #{updatedDate},
        TARGET_TYPE = #{targetType},
        TOTAL_COMPENSATION_MAX_AMOUNT = #{totalCompensationMaxAmount},
        COMPENSATION_MAX_AMOUNT = #{compensationMaxAmount}
        where ID_PACKAGE_INFO = #{idPackageInfo}
    </update>
</mapper>