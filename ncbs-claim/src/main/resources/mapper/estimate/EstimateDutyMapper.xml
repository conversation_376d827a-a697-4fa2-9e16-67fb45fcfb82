<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.estimate.EstimateDutyMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.estimate.EstimateDutyDTO" id="estimateDutyDTO">
        <id column="ID_AHCS_ESTIMATE_DUTY" property="idAhcsEstimateDuty"/>
        <result column="CREATED_BY" property="createdBy"/>
        <result column="CREATED_DATE" property="createdDate"/>
        <result column="UPDATED_BY" property="updatedBy"/>
        <result column="UPDATED_DATE" property="updatedDate"/>
        <result column="ID_AHCS_ESTIMATE_PLAN" property="idAhcsEstimatePlan"/>
        <result column="DUTY_CODE" property="dutyCode"/>
        <result column="DUTY_NAME" property="dutyName"/>
        <result column="ORG_DUTY_CODE" property="orgDutyCode"/>
        <result column="ORG_DUTY_NAME" property="orgDutyName"/>
        <result column="BASE_AMOUNT_PAY" property="baseAmountPay"/>
        <result column="ESTIMATE_AMOUNT" property="estimateAmount"/>
        <result column="ARBITRAGE_FEE" property="arbitrageFee"/>
        <result column="LAWSUIT_FEE" property="lawsuitFee"/>
        <result column="COMMON_ESTIMATE_FEE" property="commonEstimateFee"/>
        <result column="LAWYER_FEE" property="lawyerFee"/>
        <result column="EXECUTE_FEE" property="executeFee"/>
        <result column="VERIFY_APPRAISE_FEE" property="verifyAppraiseFee"/>
        <result column="INQUIRE_FEE" property="inquireFee"/>
        <result column="OTHER_FEE" property="otherFee"/>
        <result column="SPECIAL_SURVEY_FEE" property="specialSurveyFee"/>
        <result column="PLAN_CODE" property="planCode"/>
        <result column="CASE_NO" property="caseNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="POLICY_NO" property="policyNo"/>
        <result column="ESTIMATE_TYPE" property="estimateType"/>
        <result column="ID_PLY_RISK_PROPERTY" property="idPlyRiskProperty"/>
        <result column="risk_group_no" property="riskGroupNo"/>
        <result column="risk_group_name" property="riskGroupName"/>
    </resultMap>

    <select id="getDutyFormCopy" resultMap="estimateDutyDTO">
        SELECT
        PD.ID_AHCS_POLICY_DUTY ID_AHCS_ESTIMATE_DUTY,
        PD.DUTY_CODE,
        PD.DUTY_NAME,
        PD.ORG_DUTY_CODE,
        PD.ORG_DUTY_NAME,
        PD.DUTY_AMOUNT BASE_AMOUNT_PAY,
        PD.ID_AHCS_POLICY_PLAN ID_AHCS_ESTIMATE_PLAN
        FROM CLMS_POLICY_DUTY PD
        WHERE PD.ID_AHCS_POLICY_PLAN = #{idAhcsEstimatePlan,jdbcType = VARCHAR}
    </select>

    <!-- 获取预估责任金额表 -->
    <select id="getByPlanID" parameterType="java.lang.String" resultMap="estimateDutyDTO">
        select
        d.CREATED_BY ,
        d.UPDATED_BY ,
        d.ID_AHCS_ESTIMATE_DUTY ,
        d.ID_AHCS_ESTIMATE_PLAN ,
        d.DUTY_CODE ,
        d.DUTY_NAME ,
        (select t2.org_duty_code from CLMS_policy_info t, CLMS_policy_plan t1, CLMS_policy_duty t2
        where t.id_ahcs_policy_info=t1.id_ahcs_policy_info and t1.id_ahcs_policy_plan=t2.id_ahcs_policy_plan
        and t.case_no=d.CASE_NO and t2.duty_code=d.DUTY_CODE and t1.PLAN_CODE=d.PLAN_CODE) as ORG_DUTY_CODE,
        (select t2.org_duty_name from CLMS_policy_info t, CLMS_policy_plan t1, CLMS_policy_duty t2
        where t.id_ahcs_policy_info=t1.id_ahcs_policy_info and t1.id_ahcs_policy_plan=t2.id_ahcs_policy_plan
        and t.case_no=d.CASE_NO and t2.duty_code=d.DUTY_CODE and t1.PLAN_CODE=d.PLAN_CODE) as ORG_DUTY_NAME,
        d.BASE_AMOUNT_PAY ,
        d.ESTIMATE_AMOUNT ,
        d.ARBITRAGE_FEE ,
        d.LAWSUIT_FEE ,
        d.COMMON_ESTIMATE_FEE ,
        d.LAWYER_FEE ,
        d.EXECUTE_FEE ,
        d.VERIFY_APPRAISE_FEE ,
        d.INQUIRE_FEE ,
        d.OTHER_FEE,
        d.SPECIAL_SURVEY_FEE,
        d.PLAN_CODE ,
        d.CASE_NO ,
        d.CASE_TIMES ,
        d.POLICY_NO ,
        d.ESTIMATE_TYPE
        from CLMS_estimate_duty d
        where d.id_ahcs_estimate_plan=#{idAhcsEstimatePlan,jdbcType = VARCHAR}
    </select>

    <!-- 获取预估责任金额表 -->
    <select id="getDutyListByIdPlanList" parameterType="java.util.List" resultMap="estimateDutyDTO">
        select
        d.CREATED_BY ,
        d.UPDATED_BY ,
        d.ID_AHCS_ESTIMATE_DUTY ,
        d.ID_AHCS_ESTIMATE_PLAN ,
        d.DUTY_CODE ,
        d.DUTY_NAME ,
        d.BASE_AMOUNT_PAY ,
        d.ESTIMATE_AMOUNT ,
        d.ARBITRAGE_FEE ,
        d.LAWSUIT_FEE ,
        d.COMMON_ESTIMATE_FEE ,
        d.LAWYER_FEE ,
        d.EXECUTE_FEE ,
        d.VERIFY_APPRAISE_FEE ,
        d.INQUIRE_FEE ,
        d.OTHER_FEE,
        d.SPECIAL_SURVEY_FEE,
        d.PLAN_CODE ,
        d.CASE_NO ,
        d.CASE_TIMES ,
        d.POLICY_NO ,
        d.ESTIMATE_TYPE
        from CLMS_estimate_duty d
        where d.id_ahcs_estimate_plan in
        <foreach collection="paramList" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType = VARCHAR}
        </foreach>
    </select>

    <!-- 循环新增EstimateDuty -->
    <insert id="addBatchEstimateDuty" parameterType="java.util.List">
            insert into CLMS_estimate_duty
            (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_AHCS_ESTIMATE_DUTY,
            ID_AHCS_ESTIMATE_PLAN,
            DUTY_CODE,
            DUTY_NAME,
            ESTIMATE_AMOUNT,
            ARBITRAGE_FEE,
            LAWSUIT_FEE,
            COMMON_ESTIMATE_FEE,
            LAWYER_FEE,
            EXECUTE_FEE,
            VERIFY_APPRAISE_FEE ,
            INQUIRE_FEE ,
            OTHER_FEE,
            SPECIAL_SURVEY_FEE,
            BASE_AMOUNT_PAY,
            PLAN_CODE,
            CASE_NO,
            CASE_TIMES,
            POLICY_NO,
            ESTIMATE_TYPE,
            ARCHIVE_TIME,
            id_ply_risk_property,
            risk_group_no,
            risk_group_name
            )
        <foreach collection="paramList" index="index" item="item" open="(" separator="union all" close=")">
        select
            #{item.createdBy,jdbcType=VARCHAR},
            sysdate(),
            #{item.updatedBy,jdbcType=VARCHAR},
            sysdate(),
            #{item.idAhcsEstimateDuty,jdbcType=VARCHAR},
            #{item.idAhcsEstimatePlan,jdbcType=VARCHAR},
            #{item.dutyCode,jdbcType=VARCHAR},
            #{item.dutyName,jdbcType=VARCHAR},
            #{item.estimateAmount,jdbcType=NUMERIC},
            #{item.arbitrageFee,jdbcType=NUMERIC},
            #{item.lawsuitFee,jdbcType=NUMERIC},
            #{item.commonEstimateFee,jdbcType=NUMERIC},
            #{item.lawyerFee,jdbcType=NUMERIC},
            #{item.executeFee,jdbcType=NUMERIC},
            #{item.verifyAppraiseFee,jdbcType=NUMERIC},
            #{item.inquireFee,jdbcType=NUMERIC},
            #{item.otherFee,jdbcType=NUMERIC},
            #{item.specialSurveyFee,jdbcType=NUMERIC},
            #{item.baseAmountPay,jdbcType=NUMERIC},
            #{item.planCode,jdbcType=VARCHAR},
            #{item.caseNo,jdbcType=VARCHAR},
            #{item.caseTimes,jdbcType=INTEGER},
            #{item.policyNo,jdbcType=VARCHAR},
            #{item.estimateType,jdbcType=VARCHAR},
            sysdate(),
            #{item.idPlyRiskProperty,jdbcType=VARCHAR},
            #{item.riskGroupNo},
            #{item.riskGroupName}
            FROM DUAL
        </foreach>
    </insert>

    <!-- 更新预估责任金额表相关费用金额 -->
    <update id="modifyBatchEstimateDuty" parameterType="java.util.List">
        <foreach collection="paramList" index="index" item="item" open="begin" separator=";" close=";end;">
            update CLMS_estimate_duty d
            set d.UPDATED_BY = #{item.updatedBy,jdbcType=VARCHAR},
            d.ESTIMATE_AMOUNT = #{item.estimateAmount,jdbcType=NUMERIC},
            d.ARBITRAGE_FEE = #{item.arbitrageFee,jdbcType=NUMERIC},
            d.LAWSUIT_FEE = #{item.lawsuitFee,jdbcType=NUMERIC},
            d.COMMON_ESTIMATE_FEE = #{item.commonEstimateFee,jdbcType=NUMERIC},
            d.LAWYER_FEE = #{item.lawyerFee,jdbcType=NUMERIC},
            d.EXECUTE_FEE = #{item.executeFee,jdbcType=NUMERIC},
            d.VERIFY_APPRAISE_FEE = #{item.verifyAppraiseFee,jdbcType=NUMERIC},
            d.INQUIRE_FEE = #{item.inquireFee,jdbcType=NUMERIC},
            d.OTHER_FEE = #{item.otherFee,jdbcType=NUMERIC},
            d.SPECIAL_SURVEY_FEE = #{item.specialSurveyFee,jdbcType=NUMERIC},
            d.ESTIMATE_TYPE = #{item.estimateType,jdbcType=VARCHAR},
            d.UPDATED_DATE = #{item.updatedDate, jdbcType=TIMESTAMP}
            where d.DUTY_CODE = #{item.dutyCode}
            AND d.PLAN_CODE = #{item.planCode}
            AND d.CASE_NO = #{item.caseNo}
            AND d.CASE_TIMES = #{item.caseTimes}
        </foreach>
    </update>

    <!-- 只更新未决类型为立案未决,因为有重开案件，所以要跟赔付次数关联 -->
    <update id="modifyBatchEstimateType">
        <foreach collection="caseNoList" item="item" open="begin" separator=";" close=";end;">
            update CLMS_estimate_duty d
            set d.estimate_type='02'
            where d.case_no = #{item,jdbcType=VARCHAR}
            and d.case_times = #{caseTimes, jdbcType=INTEGER}
        </foreach>
    </update>

    <!-- 由赔案号查询已更改的记录 ,由于重开案件也走预估，所以要关联赔付次数-->
    <select id="getEstimateDutyDTOList" resultMap="estimateDutyDTO">
        select d.CREATED_BY ,
        d.CREATED_DATE ,
        d.UPDATED_BY ,
        d.UPDATED_DATE ,
        d.ID_AHCS_ESTIMATE_DUTY ,
        d.ID_AHCS_ESTIMATE_PLAN ,
        d.DUTY_CODE ,
        d.DUTY_NAME ,
        d.BASE_AMOUNT_PAY ,
        d.ESTIMATE_AMOUNT ,
        d.ARBITRAGE_FEE ,
        d.LAWSUIT_FEE ,
        d.COMMON_ESTIMATE_FEE ,
        d.LAWYER_FEE ,
        d.EXECUTE_FEE ,
        d.VERIFY_APPRAISE_FEE ,
        d.INQUIRE_FEE ,
        d.OTHER_FEE,
        d.SPECIAL_SURVEY_FEE,
        d.PLAN_CODE ,
        d.CASE_NO ,
        d.CASE_TIMES ,
        d.POLICY_NO ,
        d.ESTIMATE_TYPE,
        d.id_ply_risk_property,
        d.risk_group_no,
        d.risk_group_name
        from CLMS_estimate_duty d
        where d.case_times = #{caseTimes, jdbcType=INTEGER}
        and d.case_no in
        <foreach collection="caseNoList" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!--删除立案数据 紧小微案件-->
    <delete id="delEstimateDutyDataByCaseNo">
        DELETE FROM CLMS_ESTIMATE_DUTY T
        WHERE T.CASE_NO=#{caseNo, jdbcType=VARCHAR}
        AND T.CASE_TIMES=#{caseTimes, jdbcType=INTEGER}
    </delete>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO">
        INSERT INTO CLMS_ESTIMATE_DUTY (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_AHCS_ESTIMATE_DUTY,
            ID_AHCS_ESTIMATE_PLAN,
            CASE_NO,
            CASE_TIMES,
            POLICY_NO,
            PLAN_CODE,
            DUTY_CODE,
            DUTY_NAME,
            BASE_AMOUNT_PAY,
            ESTIMATE_AMOUNT,
            ARBITRAGE_FEE,
            LAWSUIT_FEE,
            COMMON_ESTIMATE_FEE,
            LAWYER_FEE,
            EXECUTE_FEE,
            VERIFY_APPRAISE_FEE,
            ESTIMATE_TYPE,
            ARCHIVE_TIME,
            INQUIRE_FEE,
            OTHER_FEE,
            SPECIAL_SURVEY_FEE
        )
        <foreach collection="paramList" item="item" open="(" separator="union all" close=")">
            SELECT
                #{item.reopenUserId},
                NOW(),
                #{item.reopenUserId},
                NOW(),
                #{item.reopenEstimateDutyId},
                #{item.idAhcsEstimatePlan},
                CASE_NO,
                #{item.reopenCaseTimes},
                POLICY_NO,
                PLAN_CODE,
                DUTY_CODE,
                DUTY_NAME,
                BASE_AMOUNT_PAY,
                ESTIMATE_AMOUNT,
                ARBITRAGE_FEE,
                LAWSUIT_FEE,
                COMMON_ESTIMATE_FEE,
                LAWYER_FEE,
                EXECUTE_FEE,
                VERIFY_APPRAISE_FEE,
                ESTIMATE_TYPE,
                NOW(),
                INQUIRE_FEE,
                OTHER_FEE,
                SPECIAL_SURVEY_FEE
            FROM CLMS_ESTIMATE_DUTY
            WHERE ID_AHCS_ESTIMATE_DUTY = #{item.idAhcsEstimateDuty}
        </foreach>
    </insert>

    <delete id="delEstimateDutyByCaseNo">
        DELETE FROM CLMS_ESTIMATE_DUTY T
        WHERE T.CASE_NO in (
        <foreach collection="caseNoList" item="item" separator=",">
            #{item}
        </foreach>
        )
          AND T.CASE_TIMES=#{caseTimes, jdbcType=INTEGER}
    </delete>
</mapper>