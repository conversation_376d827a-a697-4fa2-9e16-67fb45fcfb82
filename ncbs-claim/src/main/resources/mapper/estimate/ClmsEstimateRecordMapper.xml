<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.estimate.ClmsEstimateRecordMapper">
  <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.model.dto.estimate.ClmsEstimateRecord">
    <id column="ID_CLMS_ESTIMATE_RECORD" jdbcType="VARCHAR" property="idClmsEstimateRecord" />
    <result column="REPORT_NO" jdbcType="VARCHAR" property="reportNo" />
    <result column="CASE_TIMES" jdbcType="DECIMAL" property="caseTimes" />
    <result column="ESTIMATE_TYPE" jdbcType="VARCHAR" property="estimateType" />
    <result column="ESTIMATE_AMOUNT" jdbcType="DECIMAL" property="estimateAmount" />
    <result column="EFFECTIVE_TIME" jdbcType="TIMESTAMP" property="effectiveTime" />
    <result column="RECORD_USER_ID" jdbcType="VARCHAR" property="recordUserId" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATED_DATE" jdbcType="DATE" property="createdDate" />
    <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
    <result column="UPDATED_DATE" jdbcType="DATE" property="updatedDate" />
  </resultMap>
  <sql id="Base_Column_List">
    ID_CLMS_ESTIMATE_RECORD, REPORT_NO, CASE_TIMES, ESTIMATE_TYPE, ESTIMATE_AMOUNT, EFFECTIVE_TIME, 
    RECORD_USER_ID, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from clms_estimate_record
    where ID_CLMS_ESTIMATE_RECORD = #{idClmsEstimateRecord,jdbcType=VARCHAR}
  </select>

  <select id="selectByReportNoAndType" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from clms_estimate_record
    where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    and CASE_TIMES = #{caseTimes,jdbcType=VARCHAR}
    <if test="estimateType != null">
      and ESTIMATE_TYPE = #{estimateType,jdbcType=VARCHAR}
    </if>
    order by  EFFECTIVE_TIME desc
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from clms_estimate_record
    where ID_CLMS_ESTIMATE_RECORD = #{idClmsEstimateRecord,jdbcType=VARCHAR}
  </delete>

  <insert id="insert" keyColumn="ID_CLMS_ESTIMATE_RECORD" keyProperty="idClmsEstimateRecord"
          parameterType="com.paic.ncbs.claim.model.dto.estimate.ClmsEstimateRecord" useGeneratedKeys="true">
    insert into clms_estimate_record (ID_CLMS_ESTIMATE_RECORD, REPORT_NO, CASE_TIMES, ESTIMATE_TYPE,
                                      ESTIMATE_AMOUNT, EFFECTIVE_TIME, RECORD_USER_ID,
                                      CREATED_BY, CREATED_DATE, UPDATED_BY,
                                      UPDATED_DATE)
    values (#{idClmsEstimateRecord,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, #{caseTimes,jdbcType=DECIMAL},
            #{estimateType,jdbcType=VARCHAR},
            #{estimateAmount,jdbcType=DECIMAL},
            ifnull(#{effectiveTime}, NOW()),
            #{recordUserId,jdbcType=VARCHAR},
            #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=DATE}, #{updatedBy,jdbcType=VARCHAR},
            #{updatedDate,jdbcType=DATE})
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.model.dto.estimate.ClmsEstimateRecord">
    update clms_estimate_record
    <set>
      <if test="reportNo != null">
        REPORT_NO = #{reportNo,jdbcType=VARCHAR},
      </if>
      <if test="caseTimes != null">
        CASE_TIMES = #{caseTimes,jdbcType=DECIMAL},
      </if>
      <if test="estimateType != null">
        ESTIMATE_TYPE = #{estimateType,jdbcType=VARCHAR},
      </if>
      <if test="estimateAmount != null">
        ESTIMATE_AMOUNT = #{estimateAmount,jdbcType=DECIMAL},
      </if>
      <if test="effectiveTime != null">
        EFFECTIVE_TIME = #{effectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recordUserId != null">
        RECORD_USER_ID = #{recordUserId,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null">
        CREATED_DATE = #{createdDate,jdbcType=DATE},
      </if>
      <if test="updatedBy != null">
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedDate != null">
        UPDATED_DATE = #{updatedDate,jdbcType=DATE},
      </if>
    </set>
    where ID_CLMS_ESTIMATE_RECORD = #{idClmsEstimateRecord,jdbcType=VARCHAR}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.model.dto.estimate.ClmsEstimateRecord">
    update clms_estimate_record
    set REPORT_NO = #{reportNo,jdbcType=VARCHAR},
      CASE_TIMES = #{caseTimes,jdbcType=DECIMAL},
      ESTIMATE_TYPE = #{estimateType,jdbcType=VARCHAR},
      ESTIMATE_AMOUNT = #{estimateAmount,jdbcType=DECIMAL},
      EFFECTIVE_TIME = #{effectiveTime,jdbcType=TIMESTAMP},
      RECORD_USER_ID = #{recordUserId,jdbcType=VARCHAR},
      CREATED_BY = #{createdBy,jdbcType=VARCHAR},
      CREATED_DATE = #{createdDate,jdbcType=DATE},
      UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
      UPDATED_DATE = #{updatedDate,jdbcType=DATE}
    where ID_CLMS_ESTIMATE_RECORD = #{idClmsEstimateRecord,jdbcType=VARCHAR}
  </update>
  
  <select id="getEstimateRecordAmount" resultType="java.math.BigDecimal">
    select  a.estimate_amount
    from clms_estimate_record a
    where a.report_no = #{reportNo,jdbcType=VARCHAR}
    and a.case_times = #{caseTimes,jdbcType=DECIMAL}
    and a.estimate_type = '02'
    and exists (select 1 from clms_estimate_record b where b.report_no = a.report_no and b.case_times = a.case_times
    and b.estimate_type = '01' and b.estimate_amount != a.estimate_amount)
  </select>

  <select id="getLastEstimateRecordAmount" resultType="java.math.BigDecimal">
    select c.ESTIMATE_AMOUNT
    from clms_estimate_record c
    where c.report_no=#{reportNo,jdbcType=VARCHAR}
      and c.case_times = #{caseTimes,jdbcType=DECIMAL}
    order by c.EFFECTIVE_TIME desc
    limit 1
  </select>

  <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
    INSERT INTO CLMS_ESTIMATE_RECORD (
      CREATED_BY,
      CREATED_DATE,
      UPDATED_BY,
      UPDATED_DATE,
      ID_CLMS_ESTIMATE_RECORD,
      REPORT_NO,
      CASE_TIMES,
      ESTIMATE_TYPE,
      ESTIMATE_AMOUNT,
      EFFECTIVE_TIME,
      RECORD_USER_ID
    )
    SELECT
      #{userId},
      NOW(),
      #{userId},
      NOW(),
      MD5(UUID()),
      REPORT_NO,
      #{reopenCaseTimes},
      ESTIMATE_TYPE,
      #{restartAmount},
      NOW(),
      #{userId}
    FROM CLMS_ESTIMATE_RECORD
    WHERE REPORT_NO=#{reportNo}
    AND CASE_TIMES=#{caseTimes}
  </insert>

  <update id="updateEstimateRecordInfo" parameterType="com.paic.ncbs.claim.model.dto.estimate.ClmsEstimateRecord">
    update clms_estimate_record
    <set>
      <if test="estimateAmount != null">
        ESTIMATE_AMOUNT = #{estimateAmount,jdbcType=DECIMAL},
      </if>
      <if test="effectiveTime != null">
        EFFECTIVE_TIME = #{effectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recordUserId != null">
        RECORD_USER_ID = #{recordUserId,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
      </if>
      UPDATED_DATE = now()
    </set>
    where REPORT_NO=#{reportNo}
    AND CASE_TIMES=#{caseTimes}
    and estimate_type = '02'
  </update>
</mapper>