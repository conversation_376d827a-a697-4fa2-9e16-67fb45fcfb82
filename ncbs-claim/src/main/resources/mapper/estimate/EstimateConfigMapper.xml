<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.estimate.EstimateConfigMapper">

    <!-- 根据机构码和责任码准确查询未决金额(给自动预估用),取失效时间最新的-->
    <select id="getEstimateAmount" resultType="java.math.BigDecimal">
        select ifnull(a.ESTIMATE_AMOUNT,0)
        from (select ESTIMATE_AMOUNT,
        INVALID_DATE
        from CLMS_estimate_config aec
        where aec.DEPARTMENT_CODE = #{departmentCode, jdbcType=VARCHAR}
        and aec.DUTY_CODE = #{dutyCode, jdbcType=VARCHAR}
        order by aec.INVALID_DATE desc
        ) a limit 1
    </select>

</mapper>