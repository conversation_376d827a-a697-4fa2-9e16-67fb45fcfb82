<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.estimate.CaseInfoMapper">

 <resultMap type="com.paic.ncbs.claim.model.vo.estimate.CaseInfoVO" id="caseInfoVO">
        <result property="policyNo" column="POLICY_NO"/>
        <result property="holderName" column="HOLDER_NAME"/>
        <result property="insuranceName" column="INSURANCE_NAME"/>
        <result property="departmentName" column="DEPARTMENT_ABBR_NAME"/>
        <result property="insuranceBeginTime" column="INSURANCE_BEGIN_TIME"/>
        <result property="insuranceEndTime" column="INSURANCE_END_TIME"/>
        <result property="estimateAmount" column="ESTIMATE_AMOUNT"/>
        <result property="policyCerNo" column="policy_cer_no"/>
    </resultMap>
	
  <!-- 获取保单与承保机构 -->
	<select id="getCaseInfoList"  resultMap="caseInfoVO">
	select b.policy_no,
	       b.policy_cer_no,
	       c.name HOLDER_NAME,
	       d.name INSURANCE_NAME,
	       e.DEPARTMENT_ABBR_NAME,
	       b.insurance_begin_time,
	       b.insurance_end_time,
	       f.estimate_amount
		  from clm_case_base       a,
		       CLMS_policy_info    b,
		       CLMS_policy_holder  c,
		       CLMS_insured_person d,
		       department_define   e,
		       CLMS_estimate_policy f,
		       clms_task_info t
		 where a.case_no=b.case_no
		   and a.report_no = #{reportNo,jdbcType=VARCHAR}
		   and a.case_times = #{caseTimes, jdbcType=INTEGER}
		   and b.policy_status='B5'
		   and b.id_ahcs_policy_info = c.id_ahcs_policy_info
		   and b.id_ahcs_policy_info = d.id_ahcs_policy_info
		   and t.report_no = #{reportNo,jdbcType=VARCHAR}
		   and t.case_times = #{caseTimes, jdbcType=INTEGER}
		   and t.status = '0'
		   and t.department_code = e.department_code
		   and a.case_no = f.case_no
		   and a.case_times = f.case_times
		   order by b.insurance_begin_time
			limit 1
    </select>


</mapper>