<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.estimate.EstimateChangeMapper">

  <select id="getAllEstimateChangeList" resultType="com.paic.ncbs.claim.model.dto.estimate.EstimateChangeDTO">
    select
      t.REPORT_NO        reportNo,
      t.CASE_TIMES       caseTimes,
      t.POLICY_NO        policyNo,
      t.CHANGE_AMOUNT    changeAmount,
      t.CHANGE_DATE      changeDate,
      t.USER_ID          userId,
      t.REASON           reason,
      t.id_clms_estimate_change   idclmsEstimateChange,
      t.ID_ESTIMATE_CHANGE_APPLY   idEstimateChangeApply,
      t.sum_duty_pay_amount sumDutyPayAmount,
      t.sum_duty_fee_amount sumDutyFeeAmount,
      t.id_flag_history_change   idFlagHistoryChange
    from clms_estimate_change t
    where t.report_no = #{reportNo,jdbcType=VARCHAR}
    order by t.created_date desc
  </select>

  <select id="getLastEstimateChangeList" resultType="com.paic.ncbs.claim.model.dto.estimate.EstimateChangeDTO">
    select
      t.REPORT_NO        reportNo,
      t.CASE_TIMES       caseTimes,
      t.POLICY_NO        policyNo,
      t.CHANGE_AMOUNT    changeAmount,
      t.CHANGE_DATE      changeDate,
      t.USER_ID          userId,
      t.REASON           reason,
      t.id_flag_history_change   idFlagHistoryChange
    from clms_estimate_change t
    where t.report_no = #{reportNo,jdbcType=VARCHAR}
    and t.case_times = #{caseTimes,jdbcType=NUMERIC}
    and t.is_effective = 'Y'
  </select>

  <insert id="addEstimateChangeList" parameterType="java.util.List">
    insert into clms_estimate_change (
      created_by ,
      created_date ,
      updated_by ,
      updated_date ,
      id_clms_estimate_change ,
      report_no ,
      case_times ,
      policy_no ,
      change_amount ,
      change_date ,
      user_id ,
      reason ,
      remark ,
      is_effective,
    sum_duty_pay_amount,
    sum_duty_fee_amount,
    id_flag_history_change,
    ID_ESTIMATE_CHANGE_APPLY
    )
    values
    <foreach collection="estimateChangeList" separator="," index="index" item="item">
      (#{item.createdBy,jdbcType=VARCHAR},
      now(),
      #{item.updatedBy,jdbcType=VARCHAR},
      now(),
      #{item.idclmsEstimateChange, jdbcType=VARCHAR},
      #{item.reportNo,jdbcType=VARCHAR},
      #{item.caseTimes,jdbcType=NUMERIC},
      #{item.policyNo,jdbcType=VARCHAR},
      #{item.changeAmount,jdbcType=DECIMAL},
      #{item.changeDate,jdbcType=TIMESTAMP},
      #{item.userId,jdbcType=VARCHAR},
      #{item.reason,jdbcType=VARCHAR},
      #{item.remark,jdbcType=VARCHAR},
      'Y',
      #{item.sumDutyPayAmount},
      #{item.sumDutyFeeAmount},
      #{item.idFlagHistoryChange},
      #{item.idEstimateChangeApply}
      )
    </foreach>
  </insert>

  <update id="deleteEstimateChange" parameterType="com.paic.ncbs.claim.model.dto.estimate.EstimateChangeDTO">
    update clms_estimate_change
    set updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_date = now(),
      is_effective = 'N'
    where report_no = #{reportNo ,jdbcType=VARCHAR}
    and case_times = #{caseTimes,jdbcType=NUMERIC}
    and is_effective = 'Y'
  </update>

  <select id="getPolicyRegisterAmount" resultType="com.paic.ncbs.claim.model.dto.estimate.EstimateChangeDTO">
    select
      t.report_no         reportNo,
      t.case_times        caseTimes,
      t.policy_no         policyNo,
      (select ifnull(sum(d.estimate_amount), 0) + ifnull(sum(d.VERIFY_APPRAISE_FEE), 0) +
      ifnull(sum(d.arbitrage_fee), 0) + ifnull(sum(d.lawsuit_fee), 0) +
      ifnull(sum(d.common_estimate_fee), 0) + ifnull(sum(d.lawyer_fee), 0) +
      ifnull(sum(d.execute_fee), 0) + ifnull(sum(d.INQUIRE_FEE), 0 )
      + ifnull(sum(d.OTHER_FEE), 0 )  + ifnull(sum(d.SPECIAL_SURVEY_FEE), 0 )
      from clms_estimate_duty_record d
      where d.CASE_NO=t.CASE_NO and d.CASE_TIMES=t.CASE_TIMES and d.ESTIMATE_TYPE='02' and d.IS_EFFECTIVE='Y')    registerAmount,
      ((select a.total_insured_amount from clms_policy_info a where a.report_no = t.report_no and a.policy_no = t.policy_no)-
      (select ifnull(sum(b.policy_pay),0) from clm_policy_pay b, (SELECT MAX(case_times) case_times, case_no FROM clm_policy_pay WHERE policy_no = t.policy_no GROUP BY case_no) b2
       where b.policy_no = t.policy_no AND b.case_times = b2.case_times AND b.case_no = b2.case_no AND b.report_no != #{reportNo}
       and exists (select 1 from clm_whole_case_base c where c.report_no = b.report_no and c.case_times = b.case_times and c.whole_case_status = '0' and c.indemnity_conclusion = '1'))
      ) maxPayAmount
    from clms_estimate_policy t
    where t.report_no = #{reportNo,jdbcType=VARCHAR}
    and t.case_times = #{caseTimes,jdbcType=NUMERIC}

  </select>

  <select id="getEstimateChangeAmount" resultType="java.math.BigDecimal">
    select sum(d.CHANGE_AMOUNT)
    from clms_estimate_change d
    where d.report_no = #{reportNo ,jdbcType=VARCHAR}
    and d.CASE_TIMES = #{caseTimes,jdbcType=NUMERIC}
    and d.IS_EFFECTIVE = 'Y'
  </select>

  <select id="getChangeAmount" resultType="java.math.BigDecimal">
    SELECT IFNULL(SUM(CHANGE_AMOUNT), 0)
    FROM CLMS_ESTIMATE_CHANGE
    WHERE REPORT_NO = #{reportNo}
    AND CASE_TIMES = #{caseTimes}
    AND POLICY_NO = #{policyNo}
    AND IS_EFFECTIVE = 'Y'
  </select>

  <select id="selectCount" resultType="int">
    SELECT COUNT(*)
    FROM CLMS_ESTIMATE_CHANGE
    WHERE REPORT_NO = #{reportNo}
    AND CASE_TIMES = #{caseTimes}
    AND POLICY_NO = #{policyNo}
  </select>

  <update id="updateEstimateDateByReportNo">
    UPDATE CLMS_ESTIMATE_CHANGE
    SET UPDATED_DATE = NOW(),
        CHANGE_DATE = NOW()
    WHERE
        REPORT_NO = #{reportNo, jdbcType=VARCHAR}
        and IS_EFFECTIVE = 'Y'
  </update>
  <select id="getEstimateChangeListByIdFlagHistoryChange" resultType="com.paic.ncbs.claim.model.dto.estimate.EstimateChangeDTO">
    select
    t.REPORT_NO        reportNo,
    t.CASE_TIMES       caseTimes,
    t.POLICY_NO        policyNo,
    t.CHANGE_AMOUNT    changeAmount,
    t.CHANGE_DATE      changeDate,
    t.USER_ID          userId,
    t.REASON           reason,
    t.id_clms_estimate_change   idclmsEstimateChange,
    t.ID_ESTIMATE_CHANGE_APPLY   idEstimateChangeApply,
    t.sum_duty_pay_amount sumDutyPayAmount,
    t.sum_duty_fee_amount sumDutyFeeAmount,
    t.id_flag_history_change   idFlagHistoryChange
    from clms_estimate_change t
    where t.report_no = #{reportNo,jdbcType=VARCHAR}
    and t.id_flag_history_change = #{idFlagHistoryChange}
  </select>

  <select id="getChangeApplyTimes" resultType="Integer">
    select IFNULL(max(apply_times),0)
    from clms_estimate_change_apply
    where audit_status = '2'
    and report_no = #{reportNo, jdbcType=VARCHAR}
    and case_times = #{caseTimes,jdbcType=INTEGER}
  </select>

</mapper>