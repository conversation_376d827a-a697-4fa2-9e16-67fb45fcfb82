<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.estimate.ClmsTpaRequestRegisterMapper">

 <resultMap type="com.paic.ncbs.claim.model.dto.openapi.ClmsTpaRequestRegisterDTO" id="registerMap">
        <result property="id" column="id"/>
        <result property="reportNo" column="report_no"/>
        <result property="caseTimes" column="case_times"/>
        <result property="source" column="source"/>
        <result property="register" column="register"/>
        <result property="reportTrack" column="report_track"/>

    </resultMap>

	<insert id="save" parameterType="com.paic.ncbs.claim.model.dto.openapi.ClmsTpaRequestRegisterDTO">
		insert into clms_tpa_request_register_info(id,report_no,case_times,source,register,report_track,created_by,created_date,updated_by,updated_date)
		VALUES(#{id},#{reportNo},#{caseTimes},#{source},#{register},#{reportTrack},#{createdBy},#{createdDate},#{updatedBy},#{updatedDate})
	</insert>
  <!-- 获取保单与承保机构 -->
	<select id="getClmsTpaRequestRegisterDTO"  resultMap="registerMap">
        select id, register,report_track  reportTrack from clms_tpa_request_register_info
        where report_no=#{reportNo}
        and case_times=#{caseTimes}
    </select>
    <update id="updateData" parameterType="com.paic.ncbs.claim.model.dto.openapi.ClmsTpaRequestRegisterDTO">
        update clms_tpa_request_register_info set report_track=#{reportTrack},updated_date=#{updatedDate}
        where id=#{id}
    </update>

</mapper>