<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.estimate.EstimatePolicyMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO" id="estimatePolicyDTO">
        <id column="ID_AHCS_ESTIMATE_POLICY" property="idAhcsEstimatePolicy"/>
        <result column="CREATED_BY" property="createdBy"/>
        <result column="CREATED_DATE" property="createdDate"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_NO" property="caseNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="POLICY_NO" property="policyNo"/>
        <result column="POLICY_CER_NO" property="policyCerNo"/>
        <result column="ESTIMATE_AMOUNT" property="estimateAmount"/>
        <result column="ARBITRAGE_FEE" property="arbitrageFee"/>
        <result column="LAWSUIT_FEE" property="lawsuitFee"/>
        <result column="COMMON_ESTIMATE_FEE" property="commonEstimateFee"/>
        <result column="LAWYER_FEE" property="lawyerFee"/>
        <result column="EXECUTE_FEE" property="executeFee"/>
        <result column="VERIFY_APPRAISE_FEE" property="verifyAppraiseFee"/>

        <result column="INQUIRE_FEE" property="inquireFee"/>
        <result column="OTHER_FEE" property="otherFee"/>
        <result column="SPECIAL_SURVEY_FEE" property="specialSurveyFee"/>
        <result column="CO_SHARE" property="coShare"/>
        <result column="IS_PRINCIPAL_UNDERWRITING" property="isPrincipalUnderwriting"/>
        <result column="insurance_begin_time" property="insuranceBeginTime"/>
        <result column="insurance_end_time" property="insuranceEndTime"/>
        <result column="insured_code" property="insuredCode"/>
        <result column="name" property="name"/>
        <result column="SHARE_INSURED_AMOUNT" property="shareInsuredAmount"/>
        <result column="product_code" property="productCode"/>
        <result column="DEPARTMENT_CODE" property="departmentCode"/>
        <result column="DEPARTMENT_ABBR_NAME" property="departmentName"/>
        <association
                select="com.paic.ncbs.claim.dao.mapper.settle.PolicyClaimCaseMapper.getByCaseNo"
                javaType="com.paic.ncbs.claim.model.dto.settle.PolicyClaimCaseDTO"
                property="policyClaimCaseDTO" column="{caseNo=CASE_NO}"/>

        <!-- 关联险种信息-->
        <collection property="estimatePlanList" column="ID_AHCS_ESTIMATE_POLICY" javaType="ArrayList"
                    ofType="com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO"
                    select="com.paic.ncbs.claim.dao.mapper.estimate.EstimatePlanMapper.getByPolicyID"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO" id="estimatePolicyForRestartDTO">
        <id column="ID_AHCS_ESTIMATE_POLICY" property="idAhcsEstimatePolicy"/>
        <result column="CREATED_BY" property="createdBy"/>
        <result column="CREATED_DATE" property="createdDate"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_NO" property="caseNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="POLICY_NO" property="policyNo"/>
        <result column="POLICY_CER_NO" property="policyCerNo"/>
        <result column="ESTIMATE_AMOUNT" property="estimateAmount"/>
        <result column="ARBITRAGE_FEE" property="arbitrageFee"/>
        <result column="LAWSUIT_FEE" property="lawsuitFee"/>
        <result column="COMMON_ESTIMATE_FEE" property="commonEstimateFee"/>
        <result column="LAWYER_FEE" property="lawyerFee"/>
        <result column="EXECUTE_FEE" property="executeFee"/>
        <result column="VERIFY_APPRAISE_FEE" property="verifyAppraiseFee"/>

        <result column="INQUIRE_FEE" property="inquireFee"/>
        <result column="OTHER_FEE" property="otherFee"/>
        <result column="SPECIAL_SURVEY_FEE" property="specialSurveyFee"/>
        <result column="CO_SHARE" property="coShare"/>
        <result column="IS_PRINCIPAL_UNDERWRITING" property="isPrincipalUnderwriting"/>
        <result column="insurance_begin_time" property="insuranceBeginTime"/>
        <result column="insurance_end_time" property="insuranceEndTime"/>
        <result column="insured_code" property="insuredCode"/>
        <result column="name" property="name"/>
        <result column="SHARE_INSURED_AMOUNT" property="shareInsuredAmount"/>
        <result column="product_code" property="productCode"/>
        <result column="DEPARTMENT_CODE" property="departmentCode"/>
        <result column="DEPARTMENT_ABBR_NAME" property="departmentName"/>
        <association
                select="com.paic.ncbs.claim.dao.mapper.settle.PolicyClaimCaseMapper.getByCaseNo"
                javaType="com.paic.ncbs.claim.model.dto.settle.PolicyClaimCaseDTO"
                property="policyClaimCaseDTO" column="{caseNo=CASE_NO}"/>

        <!-- 关联险种信息-->
        <collection property="estimatePlanList" column="ID_AHCS_ESTIMATE_POLICY" javaType="ArrayList"
                    ofType="com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO"
                    select="com.paic.ncbs.claim.dao.mapper.estimate.EstimatePlanMapper.getByPolicyIDForRestart"/>
    </resultMap>



    <resultMap type="com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO" id="getEstimatePolicyMap">
        <id column="ID_AHCS_ESTIMATE_POLICY" property="idAhcsEstimatePolicy"/>
        <result column="CREATED_BY" property="createdBy"/>
        <result column="CREATED_DATE" property="createdDate"/>
        <result column="UPDATED_BY" property="updatedBy"/>
        <result column="UPDATED_DATE" property="updatedDate"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_NO" property="caseNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="POLICY_NO" property="policyNo"/>
        <result column="POLICY_CER_NO" property="policyCerNo"/>
        <result column="ESTIMATE_AMOUNT" property="estimateAmount"/>
        <result column="ARBITRAGE_FEE" property="arbitrageFee"/>
        <result column="LAWSUIT_FEE" property="lawsuitFee"/>
        <result column="COMMON_ESTIMATE_FEE" property="commonEstimateFee"/>
        <result column="LAWYER_FEE" property="lawyerFee"/>
        <result column="EXECUTE_FEE" property="executeFee"/>
        <result column="VERIFY_APPRAISE_FEE" property="verifyAppraiseFee"/>

        <result column="INQUIRE_FEE" property="inquireFee"/>
        <result column="OTHER_FEE" property="otherFee"/>
        <result column="SPECIAL_SURVEY_FEE" property="specialSurveyFee"/>
        <result column="CO_SHARE" property="coShare"/>
        <result column="SHARE_INSURED_AMOUNT" property="shareInsuredAmount"/>
        <result column="PRODUCT_CODE" property="productCode"/>
        <association
                select="com.paic.ncbs.claim.dao.mapper.settle.PolicyClaimCaseMapper.getByCaseNo"
                javaType="com.paic.ncbs.claim.model.dto.settle.PolicyClaimCaseDTO"
                property="policyClaimCaseDTO" column="{caseNo=CASE_NO}"/>

        <!-- 关联险种信息-->
        <collection property="estimatePlanList" column="ID_AHCS_ESTIMATE_POLICY" javaType="ArrayList"
                    ofType="com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO"
                    select="com.paic.ncbs.claim.dao.mapper.estimate.EstimatePlanMapper.getPlanByPolicyID"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO" id="estimatePolicyMap">
        <id column="ID_AHCS_ESTIMATE_POLICY" property="idAhcsEstimatePolicy"/>
        <result column="CREATED_BY" property="createdBy"/>
        <result column="CREATED_DATE" property="createdDate"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_NO" property="caseNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="POLICY_NO" property="policyNo"/>
        <result column="POLICY_CER_NO" property="policyCerNo"/>
        <result column="ESTIMATE_AMOUNT" property="estimateAmount"/>
        <result column="ARBITRAGE_FEE" property="arbitrageFee"/>
        <result column="LAWSUIT_FEE" property="lawsuitFee"/>
        <result column="COMMON_ESTIMATE_FEE" property="commonEstimateFee"/>
        <result column="LAWYER_FEE" property="lawyerFee"/>
        <result column="EXECUTE_FEE" property="executeFee"/>
        <result column="VERIFY_APPRAISE_FEE" property="verifyAppraiseFee"/>

        <result column="INQUIRE_FEE" property="inquireFee"/>
        <result column="OTHER_FEE" property="otherFee"/>
        <result column="SPECIAL_SURVEY_FEE" property="specialSurveyFee"/>
        <result column="CO_SHARE" property="coShare"/>

        <!-- 关联险种信息-->
        <collection property="estimatePlanList" column="ID_AHCS_ESTIMATE_POLICY" javaType="ArrayList"
                    ofType="com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO"
                    select="com.paic.ncbs.claim.dao.mapper.estimate.EstimatePlanMapper.getPlanListByPolicyID"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO" id="estimatePolicyDTOFromCopy">
        <id column="id_ahcs_policy_info" property="idAhcsEstimatePolicy"/>
        <result column="report_no" property="reportNo"/>
        <result column="case_no" property="caseNo"/>
        <result column="case_times" property="caseTimes"/>
        <result column="policy_no" property="policyNo"/>
        <result column="policy_cer_no" property="policyCerNo"/>
        <result column="reinsure_scale" property="coShare"/>
        <result column="sub_policyno" property="subpolicyNo"/>
        <result column="insured_code" property="insuredCode"/>
        <result column="department_code" property="departmentCode"/>
        <result column="department_chinese_name" property="departmentName"/>
        <result column="client_no" property="partyNo"/>
        <result column="accept_insurance_flag" property="isPrincipalUnderwriting"/>
        <result column="insurance_begin_time" property="insuranceBeginTime"/>
        <result column="insurance_end_time" property="insuranceEndTime"/>
        <result column="product_code" property="productCode"/>
        <result column="product_version" property="productVersion"/>
        <result column="name" property="name"/>
        <result column="coinsurance_type" property="coinsuranceType"/>


        <!-- 关联险种信息-->
        <collection property="estimatePlanList" column="id_ahcs_policy_info" javaType="ArrayList"
                    ofType="com.paic.ncbs.claim.model.dto.estimate.EstimatePlanDTO"
                    select="com.paic.ncbs.claim.dao.mapper.estimate.EstimatePlanMapper.getPlanFormCopy"/>

    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyInfoDTO" id="estimatePolicyInfoDTO">
        <result column="report_no" property="reportNo"/>
        <result column="case_times" property="caseTimes"/>
        <result column="policy_no" property="policyNo"/>
        <result column="case_no" property="caseNo"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.estimate.EstimatePolicySumDTO" id="policySumDto">
        <result column="case_no" property="caseNo"/>
        <result column="case_times" property="caseTimes"/>
        <result column="policy_no" property="policyNo"/>
        <result column="policy_sum_estimate" property="policySumEstimate"/>
    </resultMap>

    <!--  根据报案号从抄单信息中查询 -->
    <select id="getEstimatePolicyFromCopy" resultMap="estimatePolicyDTOFromCopy">
        select p.id_ahcs_policy_info,
        p.policy_cer_no,
        p.report_no,
        p.policy_no,
        p.department_code,
        p.insurance_begin_time,
        p.insurance_end_time,
        p.product_code,
        p.product_version,
        p.department_chinese_name,
        p.case_no,
        p.case_times,
        p.reinsure_scale,
        p.accept_insurance_flag,
        p.coinsurance_type,
        p.client_no,
        p.name,
        (select ip.sub_policyno from CLMS_insured_person ip where p.id_ahcs_policy_info = ip.id_ahcs_policy_info and
        (p.client_no = ip.client_no or p.certificate_no=ip.certificate_no) limit 1) sub_policyno,
        (select ip.CLIENT_NO from CLMS_insured_person ip where p.id_ahcs_policy_info = ip.id_ahcs_policy_info and
        (p.client_no = ip.client_no or p.certificate_no=ip.certificate_no) limit 1) insured_code
        from ( select
        pi.id_ahcs_policy_info,
        pi.generate_flag,
        pi.policy_cer_no,
        pi.report_no,
        pi.policy_no,
        pi.department_code,
        pi.insurance_begin_time,
        pi.insurance_end_time,
        pi.product_code,
        pi.product_version,
        (select dd.department_abbr_name from department_define dd where pi.department_code = dd.department_code)
        department_chinese_name,
        ac.reinsure_scale reinsure_scale,
        ac.accept_insurance_flag accept_insurance_flag,
        ac.coinsurance_type coinsurance_type,
        (select rc.client_no from CLMS_report_customer rc where pi.report_no = rc.report_no limit 1) client_no,
        (select rc.certificate_no from CLMS_report_customer rc where pi.report_no = rc.report_no limit 1)
        certificate_no,
        (select cb.case_no from clm_case_base cb where pi.case_no = cb.case_no limit 1) case_no,
        (select cb.case_times from clm_case_base cb where pi.report_no = cb.report_no and pi.policy_no = cb.policy_no
        and cb.case_times =#{caseTimes, jdbcType = INTEGER} limit 1) case_times,
        (select ph.name from CLMS_policy_holder ph where pi.id_ahcs_policy_info = ph.id_ahcs_policy_info ) name
        from CLMS_policy_info pi left join CLMS_coinsure ac on pi.id_ahcs_policy_info = ac.id_ahcs_policy_info and
        ac.coinsurance_type='0' and ac.accept_insurance_flag='1'
        and (ac.reinsure_company_code= '3005' or exists(select DEPARTMENT_CODE from department_define t where
        t.department_code=ac.reinsure_company_code))) p
        where p.report_no =#{reportNo, jdbcType = VARCHAR} and p.case_times = #{caseTimes, jdbcType = INTEGER}
    </select>

    <!-- 获取预估立案信息 -->
    <select id="getByReportNoAndCaseTimes" resultMap="estimatePolicyDTO">
        select distinct
            po.CREATED_BY ,
            po.ID_AHCS_ESTIMATE_POLICY ,
            po.REPORT_NO ,
            po.CASE_NO ,
            po.CASE_TIMES ,
            po.POLICY_NO ,
            po.ESTIMATE_AMOUNT ,
            po.ARBITRAGE_FEE ,
            po.LAWSUIT_FEE ,
            po.COMMON_ESTIMATE_FEE ,
            po.LAWYER_FEE ,
            po.EXECUTE_FEE ,
            po.VERIFY_APPRAISE_FEE ,
            po.INQUIRE_FEE,
            po.OTHER_FEE,
            po.SPECIAL_SURVEY_FEE,
            po.CO_SHARE ,
            po.IS_PRINCIPAL_UNDERWRITING,
            po.insurance_begin_time,
            po.insurance_end_time,
            po.POLICY_CER_NO,
            pi.product_code product_code,
            (CASE pi.SHARE_INSURED_AMOUNT WHEN '1' then 'Y' else 'N' end ) SHARE_INSURED_AMOUNT,
            pc.INSURED_CODE,
            cp.DEPARTMENT_CODE,
            cp.DEPARTMENT_ABBR_NAME
        from CLMS_estimate_policy po inner join CLMS_POLICY_CLAIM_CASE pc on po.POLICY_NO = pc.POLICY_NO and po.CASE_NO = pc.CASE_NO
                 left join CLMS_policy_info pi on pi.case_no=po.case_no and pi.report_no=po.report_no and pi.POLICY_NO=po.POLICY_NO
                 left join  (select  c.`DEPARTMENT_CODE`,  d.`DEPARTMENT_ABBR_NAME` ,C.`REPORT_NO` from  clms_policy_info c left  join  department_define d on  c.`DEPARTMENT_CODE` =d.`DEPARTMENT_CODE`
        ) cp  on cp.REPORT_NO = po.REPORT_NO
        where po.report_no=#{reportNo,jdbcType=VARCHAR}
        and po.case_times=#{caseTimes,jdbcType=INTEGER}
        order  by po.POLICY_NO
    </select>

    <!-- 获取预估立案信息 -->
    <select id="getEstimatePolicyList" resultMap="getEstimatePolicyMap">
        select
        po.CREATED_BY ,
        po.UPDATED_BY ,
        po.UPDATED_DATE ,
        po.ID_AHCS_ESTIMATE_POLICY ,
        po.REPORT_NO ,
        po.CASE_NO ,
        po.CASE_TIMES ,
        po.POLICY_NO ,
        po.POLICY_CER_NO ,
        po.ESTIMATE_AMOUNT ,
        po.ARBITRAGE_FEE ,
        po.LAWSUIT_FEE ,
        po.COMMON_ESTIMATE_FEE ,
        po.LAWYER_FEE ,
        po.EXECUTE_FEE ,
        po.VERIFY_APPRAISE_FEE ,
        po.INQUIRE_FEE,
        po.OTHER_FEE,
        po.SPECIAL_SURVEY_FEE,
        po.CO_SHARE,
        (select CASE SHARE_INSURED_AMOUNT WHEN '1' then 'Y' else 'N' end from CLMS_policy_info pi where
        pi.case_no=po.case_no and pi.report_no=po.report_no and pi.policy_no = po.policy_no) SHARE_INSURED_AMOUNT,
        (select product_code from CLMS_policy_info pi where pi.policy_no = po.policy_no LIMIT 1) product_code
        from CLMS_estimate_policy po
        where po.report_no=#{reportNo,jdbcType=VARCHAR}
        and po.case_times=#{caseTimes,jdbcType=INTEGER}
    </select>

    <!-- 获取预估立案信息 -->
    <select id="getEstimatePolicyByHistoryList" resultMap="estimatePolicyMap">
        select
        po.CREATED_BY ,
        po.ID_AHCS_ESTIMATE_POLICY ,
        po.REPORT_NO ,
        po.CASE_NO ,
        po.CASE_TIMES ,
        po.POLICY_NO ,
        po.POLICY_CER_NO ,
        po.ESTIMATE_AMOUNT ,
        po.ARBITRAGE_FEE ,
        po.LAWSUIT_FEE ,
        po.COMMON_ESTIMATE_FEE ,
        po.LAWYER_FEE ,
        po.EXECUTE_FEE ,
        po.VERIFY_APPRAISE_FEE ,
        po.INQUIRE_FEE,
        po.OTHER_FEE,
        po.SPECIAL_SURVEY_FEE,
        po.CO_SHARE
        from CLMS_estimate_policy po
        where po.report_no=#{reportNo,jdbcType=VARCHAR}
        and po.case_times=#{caseTimes,jdbcType=INTEGER}
    </select>

    <!-- 循环新增EstimatePolicy -->
    <insert id="addBatchEstimatePolicy" parameterType="java.util.List">
            insert into CLMS_estimate_policy
            (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_AHCS_ESTIMATE_POLICY,
            REPORT_NO,
            CASE_NO,
            CASE_TIMES,
            POLICY_NO,
            ESTIMATE_AMOUNT,
            VERIFY_APPRAISE_FEE,
            CO_SHARE,
            IS_PRINCIPAL_UNDERWRITING,
            INSURANCE_BEGIN_TIME,
            INSURANCE_END_TIME,
            POLICY_CER_NO,
            ARCHIVE_TIME
            )
        values
        <foreach collection="paramList" item="item" index="index" separator=",">
            (
            #{item.createdBy,jdbcType=VARCHAR},
            sysdate(),
            #{item.updatedBy,jdbcType=VARCHAR},
            sysdate(),
            #{item.idAhcsEstimatePolicy, jdbcType=VARCHAR},
            #{item.reportNo,jdbcType=VARCHAR},
            #{item.caseNo,jdbcType=VARCHAR},
            #{item.caseTimes,jdbcType=INTEGER},
            #{item.policyNo,jdbcType=VARCHAR},
            #{item.estimateAmount,jdbcType=NUMERIC},
            #{item.verifyAppraiseFee, jdbcType=DECIMAL},
            #{item.coShare,jdbcType=NUMERIC},
            #{item.isPrincipalUnderwriting,jdbcType=VARCHAR},
            #{item.insuranceBeginTime,javaType=DATE, jdbcType=VARCHAR},
            #{item.insuranceEndTime,javaType=DATE, jdbcType=VARCHAR},
            #{item.policyCerNo, jdbcType=VARCHAR},
            sysdate()
            )
        </foreach>
    </insert>

    <!-- 更新预估保单金额表相关费用金额 -->
    <update id="modifyBatchEstimatePolicy" parameterType="java.util.List">
        <foreach collection="paramList" index="index" item="item" open="begin" separator=";" close=";end;">
            update CLMS_estimate_policy d
            set d.updated_by = #{item.updatedBy,jdbcType=VARCHAR},
            d.ESTIMATE_AMOUNT = #{item.estimateAmount,jdbcType=NUMERIC},
            d.ARBITRAGE_FEE = #{item.arbitrageFee,jdbcType=NUMERIC},
            d.LAWSUIT_FEE = #{item.lawsuitFee,jdbcType=NUMERIC},
            d.COMMON_ESTIMATE_FEE = #{item.commonEstimateFee,jdbcType=NUMERIC},
            d.LAWYER_FEE = #{item.lawyerFee,jdbcType=NUMERIC},
            d.EXECUTE_FEE = #{item.executeFee,jdbcType=NUMERIC},
            d.VERIFY_APPRAISE_FEE = #{item.verifyAppraiseFee,jdbcType=NUMERIC},
            d.UPDATED_DATE = sysdate()
            where d.ID_AHCS_ESTIMATE_POLICY = #{item.idAhcsEstimatePolicy,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="getEstimateAmount" resultType="java.math.BigDecimal">
        select sum(a.estimate_amount)
        from CLMS_estimate_duty_record a,
        CLMS_estimate_policy b
        where a.case_no=b.case_no
        and a.case_times=b.case_times
        and b.report_no=#{reportNo}
        and b.case_times=#{caseTimes}
        and a.estimate_type='01'
        and a.IS_EFFECTIVE = 'Y'
    </select>

    <!-- 历史案件查询页面显示的预估金额返回 -->
    <select id="getAmount" resultType="java.math.BigDecimal">
        select ifnull(sum(a.estimate_amount), 0) as amount
        from CLMS_estimate_duty a,
        CLMS_estimate_policy b
        where a.case_no=b.case_no
        and a.case_times=b.case_times
        and b.report_no=#{reportNo, jdbcType=VARCHAR}
        <if test="caseNo != null and caseNo != '' ">
            and b.case_no = #{caseNo, jdbcType=VARCHAR}
        </if>
        and b.case_times=#{caseTimes}
    </select>

    <!-- 历史案件查询页面显示的预估金额返回 -->
    <select id="getEstimateAmountByCondition" resultType="java.math.BigDecimal">
        select ifnull(sum(a.estimate_amount), 0) as amount
        from CLMS_estimate_duty a,
        CLMS_estimate_policy b
        where a.case_no=b.case_no
        and a.case_times=b.case_times
        and b.policy_no=#{policyNo, jdbcType=VARCHAR}
        <if test="caseNo != null and caseNo != '' ">
            and b.case_no = #{caseNo, jdbcType=VARCHAR}
        </if>
        and b.case_times=#{caseTimes}
    </select>

    <select id="checkExists" resultType="int">
        select count(*)
        from CLMS_estimate_policy ep,
        CLMS_estimate_duty_record dr
        where ep.case_no = dr.case_no
        and ep.case_times = dr.case_times
        and dr.estimate_type = #{estimateType}
        and ep.report_no = #{reportNo}
        and ep.case_times = #{caseTimes}
        and dr.IS_EFFECTIVE = 'Y'
    </select>

    <!-- 获取预估后的保单的机构列表 -->
    <select id="getPolicyDeptCodeList" resultType="java.lang.String">
        select distinct(g.department_code)
        from CLMS_estimate_policy f,
        CLMS_policy_info g where
        f.report_no = #{reportNo, jdbcType=VARCHAR}
        and f.case_times = #{caseTimes, jdbcType=INTEGER}
        and f.report_no=g.report_no
        and f.policy_no=g.policy_no
        and f.estimate_amount is not null
    </select>

    <select id="getPolicyDeptCode" resultType="java.lang.String">
        select a.department_code
        from CLMS_policy_info a
        where a.case_no=#{caseNo, jdbcType=VARCHAR}
    </select>

    <select id="getEstimateAmountAndFee" resultType="java.math.BigDecimal">
        SELECT ifnull(sum(d.estimate_amount), 0) + ifnull(sum(d.VERIFY_APPRAISE_FEE), 0) +
        ifnull(sum(d.arbitrage_fee), 0) + ifnull(sum(d.lawsuit_fee), 0) +
        ifnull(sum(d.common_estimate_fee), 0) + ifnull(sum(d.lawyer_fee), 0) +
        ifnull(sum(d.execute_fee), 0) + ifnull(sum(d.INQUIRE_FEE), 0)+ifnull(sum(d.OTHER_FEE), 0)+ifnull(sum(d.SPECIAL_SURVEY_FEE), 0) estimateAmount
        FROM CLMS_estimate_policy p, CLMS_estimate_duty d
        where p.case_no = d.case_no
        and p.report_no = #{reportNo}
        and p.case_times = #{caseTimes}
    </select>

    <select id="getEstimatePolicySum" resultMap="policySumDto">
        select h.case_no,
        h.case_times,
        h.policy_no,
        (ifnull(sum(h.estimate_amount), 0) + ifnull(sum(h.arbitrage_fee), 0) +
        ifnull(sum(h.lawsuit_fee), 0) + ifnull(sum(h.common_estimate_fee), 0) +
        ifnull(sum(h.lawyer_fee), 0) + ifnull(sum(h.execute_fee), 0) +
        ifnull(sum(h.VERIFY_APPRAISE_FEE), 0) + + ifnull(sum(h.INQUIRE_FEE), 0)+
        ifnull(sum(h.OTHER_FEE), 0) + ifnull(sum(h.SPECIAL_SURVEY_FEE), 0)) as policy_sum_estimate
        from CLMS_estimate_policy p, CLMS_estimate_duty_record h
        where p.case_no = h.case_no
        and p.case_times = h.case_times
        and p.report_no = #{reportNo}
        and p.case_times = #{caseTimes}
        and h.estimate_type = '01'
        and h.IS_EFFECTIVE = 'Y'
        and (h.estimate_amount is not null or
        h.arbitrage_fee is not null and h.lawsuit_fee is not null or
        h.common_estimate_fee is not null or h.lawyer_fee is not null or
        h.execute_fee is not null or h.VERIFY_APPRAISE_FEE is not null or
        h.INQUIRE_FEE is not null or h.OTHER_FEE is not null
            or h.SPECIAL_SURVEY_FEE is not null )
        group by h.case_no, h.case_times, h.policy_no
    </select>

    <!-- 获取立案金额总和，包括费用 -->
    <select id="getRegisterAmount" resultType="java.math.BigDecimal">
        SELECT ifnull(sum(d.estimate_amount), 0) + ifnull(sum(d.VERIFY_APPRAISE_FEE), 0) +
        ifnull(sum(d.arbitrage_fee), 0) + ifnull(sum(d.lawsuit_fee), 0) +
        ifnull(sum(d.common_estimate_fee), 0) + ifnull(sum(d.lawyer_fee), 0) +
        ifnull(sum(d.execute_fee), 0) + ifnull(sum(d.INQUIRE_FEE), 0 )
                   + ifnull(sum(d.OTHER_FEE), 0 )  + ifnull(sum(d.SPECIAL_SURVEY_FEE), 0 )
            registerAmount
        FROM CLMS_estimate_policy p, CLMS_estimate_duty_record d
        where p.case_no = d.case_no
        and d.estimate_type='02'
        and d.IS_EFFECTIVE = 'Y'
        and p.report_no = #{reportNo, jdbcType=VARCHAR}
        and p.case_times = #{caseTimes, jdbcType=INTEGER}
    </select>

    <!-- 根据赔案号和赔付次数批量更新保单的金额 -->
    <update id="modifyPolicyAmount" parameterType="java.util.List">
        <foreach collection="policyList" index="index" item="item" open="begin" separator=";" close=";end;">
            update CLMS_estimate_policy d
            set d.updated_by = #{item.updatedBy,jdbcType=VARCHAR},
            d.ESTIMATE_AMOUNT = #{item.estimateAmount,jdbcType=NUMERIC},
            d.ARBITRAGE_FEE = #{item.arbitrageFee,jdbcType=NUMERIC},
            d.LAWSUIT_FEE = #{item.lawsuitFee,jdbcType=NUMERIC},
            d.COMMON_ESTIMATE_FEE = #{item.commonEstimateFee,jdbcType=NUMERIC},
            d.LAWYER_FEE = #{item.lawyerFee,jdbcType=NUMERIC},
            d.EXECUTE_FEE = #{item.executeFee,jdbcType=NUMERIC},
            d.VERIFY_APPRAISE_FEE = #{item.verifyAppraiseFee,jdbcType=NUMERIC},
            d.INQUIRE_FEE = #{item.inquireFee,jdbcType=NUMERIC},
            d.OTHER_FEE = #{item.otherFee,jdbcType=NUMERIC},
            d.SPECIAL_SURVEY_FEE = #{item.specialSurveyFee,jdbcType=NUMERIC},
            d.UPDATED_DATE = sysdate()
            where d.CASE_NO = #{item.caseNo, jdbcType=VARCHAR}
            and d.CASE_TIMES = #{item.caseTimes, jdbcType=INTEGER}
        </foreach>
    </update>

    <!--获取立案保单数据-->
    <select id="getEstimateDataByReportNo" resultMap="estimatePolicyInfoDTO">
        select
        t.ID_AHCS_ESTIMATE_POLICY,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.POLICY_NO,
        t.CASE_NO
        from CLMS_ESTIMATE_POLICY t
        where t.REPORT_NO=#{reportNo, jdbcType=VARCHAR}
        and t.CASE_TIMES=#{caseTimes, jdbcType=INTEGER}
    </select>

    <!--删除立案数据 紧小微案件-->
    <delete id="delEstimateDataByReportNo">
        DELETE FROM CLMS_ESTIMATE_POLICY T
        WHERE T.REPORT_NO=#{reportNo, jdbcType=VARCHAR}
        AND T.CASE_TIMES=#{caseTimes, jdbcType=INTEGER}
    </delete>

    <select id="getLatestRegisterAmount" resultType="java.math.BigDecimal">
        SELECT ifnull(sum(d.estimate_amount), 0) + ifnull(sum(d.VERIFY_APPRAISE_FEE), 0) +
        ifnull(sum(d.arbitrage_fee), 0) + ifnull(sum(d.lawsuit_fee), 0) +
        ifnull(sum(d.common_estimate_fee), 0) + ifnull(sum(d.lawyer_fee), 0) +
        ifnull(sum(d.execute_fee), 0) + ifnull(sum(d.INQUIRE_FEE), 0 )
                   + ifnull(sum(d.OTHER_FEE), 0 )  + ifnull(sum(d.SPECIAL_SURVEY_FEE), 0 )
        FROM CLMS_estimate_policy p, CLMS_estimate_duty_record d
        where p.case_no = d.case_no
        and d.IS_EFFECTIVE = 'Y'
        and p.report_no = #{reportNo, jdbcType=VARCHAR}
        and p.case_times = #{caseTimes, jdbcType=INTEGER}
        and d.estimate_type= #{estimateType,jdbcType=VARCHAR}
    </select>

    <select id="getIdAhcsEstimateDutyRecord" resultType="java.lang.String">
        SELECT d.ID_AHCS_ESTIMATE_DUTY_RECORD AS idAhcsEstimateDutyRecord
        FROM CLMS_estimate_policy p, CLMS_estimate_duty_record d
        where p.case_no = d.case_no
        and d.IS_EFFECTIVE = 'Y'
        and p.report_no = #{reportNo, jdbcType=VARCHAR}
        and p.case_times = #{caseTimes, jdbcType=INTEGER}
        and d.estimate_type= #{estimateType,jdbcType=VARCHAR}
        and (d.estimate_amount is not null
        or d.VERIFY_APPRAISE_FEE is not null
        or d.arbitrage_fee is not null
        or d.lawsuit_fee is not null
        or d.common_estimate_fee is not null
        or d.lawyer_fee is not null
        or d.execute_fee is not null
        or d.INQUIRE_FEE is not null
            or d.OTHER_FEE is not null
            or d.SPECIAL_SURVEY_FEE is not null)
        limit 1
    </select>

    <!-- 查询最新的立案金额（包括费用） -->
    <!-- 目前 CLMS_estimate_policy表 金额数据都为空，应该是是保存有bag-->
    <select id="getEstimatePolicyAmount" resultType="java.math.BigDecimal">
        SELECT ifnull(sum(p.estimate_amount), 0) + ifnull(sum(p.VERIFY_APPRAISE_FEE), 0) +
        ifnull(sum(p.arbitrage_fee), 0) + ifnull(sum(p.lawsuit_fee), 0) +
        ifnull(sum(p.common_estimate_fee), 0) + ifnull(sum(p.lawyer_fee), 0) +
        ifnull(sum(p.execute_fee), 0) +
               ifnull(sum(p.INQUIRE_FEE), 0) +
               ifnull(sum(p.OTHER_FEE), 0) + ifnull(sum(p.SPECIAL_SURVEY_FEE), 0)
            estimateAmount
        FROM CLMS_estimate_policy p
        where p.report_no = #{reportNo}
        and p.case_times = #{caseTimes}
    </select>

    <select id="getProductClass" resultType="java.lang.String">
        SELECT t.product_class
        FROM base_marketproduct_info T
        WHERE T.marketproduct_code = #{productCode, jdbcType=VARCHAR}
        AND T.VERSION = #{productVersion, jdbcType=VARCHAR}
        limit 1
    </select>

    <select id="getAmountByReportNo" resultType="java.math.BigDecimal">
        select sum(ifnull(ep.estimate_amount,0))
        from CLMS_estimate_policy ep
        where ep.case_times = #{caseTimes}
        and ep.report_no = #{reportNo}
    </select>

    <select id="getEstimateAmountByPolicyNo" resultType="java.math.BigDecimal">
        select sum(t.estimate_amount)
        from CLMS_estimate_policy t
        where t.policy_no=#{policyNo, jdbcType=VARCHAR}
        and t.report_no = #{reportNo, jdbcType=VARCHAR}
        and t.case_times = #{caseTimes, jdbcType=INTEGER}
    </select>

    <select id="getIsRegister" resultType="java.lang.String">
        select t.is_register
        from clm_whole_case_base t
        where t.report_no = #{reportNo, jdbcType=VARCHAR}
        and t.case_times = #{caseTimes, jdbcType=INTEGER}
        limit 1
    </select>

    <select id="getRegisterAmountByPolicyNo" resultType="java.math.BigDecimal">
        SELECT ifnull(sum(d.estimate_amount), 0) + ifnull(sum(d.VERIFY_APPRAISE_FEE), 0) +
        ifnull(sum(d.arbitrage_fee), 0) + ifnull(sum(d.lawsuit_fee), 0) +
        ifnull(sum(d.common_estimate_fee), 0) + ifnull(sum(d.lawyer_fee), 0) +
        ifnull(sum(d.execute_fee), 0) +  ifnull(sum(p.INQUIRE_FEE), 0) +
        ifnull(sum(p.OTHER_FEE), 0) + ifnull(sum(p.SPECIAL_SURVEY_FEE), 0) registerAmount
        FROM CLMS_estimate_policy p, CLMS_estimate_duty_record d
        where p.case_no = d.case_no
        and d.estimate_type='02'
        and d.IS_EFFECTIVE = 'Y'
        and p.report_no = #{reportNo, jdbcType=VARCHAR}
        and p.case_times = #{caseTimes, jdbcType=INTEGER}
        and p.policy_no=#{policyNo, jdbcType=VARCHAR}
    </select>


    <select id="getEstimateDataByPolicy" resultMap="estimatePolicyDTO">
        select distinct
            po.CREATED_BY ,
            po.CASE_TIMES ,
            po.POLICY_NO ,
            po.ESTIMATE_AMOUNT ,
            po.ID_AHCS_ESTIMATE_POLICY ,
            po.REPORT_NO ,
            po.CASE_NO ,
            po.ARBITRAGE_FEE ,
            po.LAWSUIT_FEE ,
            po.COMMON_ESTIMATE_FEE ,
            po.LAWYER_FEE ,
            po.EXECUTE_FEE ,
            po.VERIFY_APPRAISE_FEE ,
            po.INQUIRE_FEE,
            po.OTHER_FEE,
            po.SPECIAL_SURVEY_FEE,
            po.CO_SHARE ,
            po.IS_PRINCIPAL_UNDERWRITING,
            po.insurance_begin_time,
            po.insurance_end_time,
            po.POLICY_CER_NO,
            pi.product_code product_code,
            (CASE pi.SHARE_INSURED_AMOUNT WHEN '1' then 'Y' else 'N' end ) SHARE_INSURED_AMOUNT,
            cp.DEPARTMENT_CODE,
            cp.DEPARTMENT_ABBR_NAME
        from CLMS_estimate_policy po
                 left join CLMS_policy_info pi on pi.case_no=po.case_no and pi.report_no=po.report_no and pi.POLICY_NO=po.POLICY_NO
                 left join  (select  c.`DEPARTMENT_CODE`,  d.`DEPARTMENT_ABBR_NAME` ,C.`REPORT_NO` from  clms_policy_info c left  join  department_define d on  c.`DEPARTMENT_CODE` =d.`DEPARTMENT_CODE`
        ) cp  on cp.REPORT_NO = po.REPORT_NO
        where po.POLICY_NO=#{policyNo,jdbcType=VARCHAR}
        and po.case_times=#{caseTimes,jdbcType=INTEGER}
        <if test="reportNo != null and reportNo != '' ">
            and po.REPORT_NO = #{reportNo, jdbcType=VARCHAR}
        </if>
    </select>


    <select id="getByReportNoAndCaseTimesForRestartCase" resultMap="estimatePolicyForRestartDTO">
        select distinct
        po.CREATED_BY ,
        po.ID_AHCS_ESTIMATE_POLICY ,
        po.REPORT_NO ,
        po.CASE_NO ,
        po.CASE_TIMES ,
        po.POLICY_NO ,
        po.ESTIMATE_AMOUNT ,
        po.ARBITRAGE_FEE ,
        po.LAWSUIT_FEE ,
        po.COMMON_ESTIMATE_FEE ,
        po.LAWYER_FEE ,
        po.EXECUTE_FEE ,
        po.VERIFY_APPRAISE_FEE ,
        po.INQUIRE_FEE,
        po.OTHER_FEE,
        po.SPECIAL_SURVEY_FEE,
        po.CO_SHARE ,
        po.IS_PRINCIPAL_UNDERWRITING,
        po.insurance_begin_time,
        po.insurance_end_time,
        po.POLICY_CER_NO,
        pi.product_code product_code,
        (CASE pi.SHARE_INSURED_AMOUNT WHEN '1' then 'Y' else 'N' end ) SHARE_INSURED_AMOUNT,
        pc.INSURED_CODE,
        cp.DEPARTMENT_CODE,
        cp.DEPARTMENT_ABBR_NAME
        from CLMS_estimate_policy po inner join CLMS_POLICY_CLAIM_CASE pc on po.POLICY_NO = pc.POLICY_NO and po.CASE_NO = pc.CASE_NO
        left join CLMS_policy_info pi on pi.case_no=po.case_no and pi.report_no=po.report_no and pi.POLICY_NO=po.POLICY_NO
        left join  (select  c.`DEPARTMENT_CODE`,  d.`DEPARTMENT_ABBR_NAME` ,C.`REPORT_NO` from  clms_policy_info c left  join  department_define d on  c.`DEPARTMENT_CODE` =d.`DEPARTMENT_CODE`
        ) cp  on cp.REPORT_NO = po.REPORT_NO
        where po.report_no=#{reportNo,jdbcType=VARCHAR}
        and po.case_times=#{caseTimes,jdbcType=INTEGER}
        order  by po.POLICY_NO
    </select>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyInfoDTO">
        INSERT INTO CLMS_ESTIMATE_POLICY (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_AHCS_ESTIMATE_POLICY,
            REPORT_NO,
            CASE_NO,
            CASE_TIMES,
            POLICY_NO,
            ESTIMATE_AMOUNT,
            ARBITRAGE_FEE,
            LAWSUIT_FEE,
            COMMON_ESTIMATE_FEE,
            LAWYER_FEE,
            EXECUTE_FEE,
            VERIFY_APPRAISE_FEE,
            IS_PRINCIPAL_UNDERWRITING,
            INSURANCE_BEGIN_TIME,
            INSURANCE_END_TIME,
            CO_SHARE,
            POLICY_CER_NO,
            ARCHIVE_TIME,
            INQUIRE_FEE,
            OTHER_FEE,
            SPECIAL_SURVEY_FEE
        )
        <foreach collection="paramList" item="item" open="(" separator="union all" close=")">
            SELECT
                #{item.reopenUserId},
                NOW(),
                #{item.reopenUserId},
                NOW(),
                #{item.reopenEstimatePolicyId},
                REPORT_NO,
                CASE_NO,
                #{item.reopenCaseTimes},
                POLICY_NO,
                ESTIMATE_AMOUNT,
                ARBITRAGE_FEE,
                LAWSUIT_FEE,
                COMMON_ESTIMATE_FEE,
                LAWYER_FEE,
                EXECUTE_FEE,
                VERIFY_APPRAISE_FEE,
                IS_PRINCIPAL_UNDERWRITING,
                INSURANCE_BEGIN_TIME,
                INSURANCE_END_TIME,
                CO_SHARE,
                POLICY_CER_NO,
                NOW(),
                INQUIRE_FEE,
                OTHER_FEE,
                SPECIAL_SURVEY_FEE
            FROM CLMS_ESTIMATE_POLICY
            WHERE ID_AHCS_ESTIMATE_POLICY = #{item.idAhcsEstimatePolicy}
        </foreach>
    </insert>

    <select id="getIdByCaseNo" parameterType="java.lang.String" resultType="java.lang.String">
       select ID_AHCS_ESTIMATE_POLICY
       FROM CLMS_ESTIMATE_POLICY
       WHERE CASE_NO = #{caseNo,jdbcType=VARCHAR}
       limit 1
    </select>
</mapper>