<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.estimate.EstimateDutyHistoryMapper">
    <!-- 定义EstimatDutyHistory 的复杂关联map -->
    <resultMap type="com.paic.ncbs.claim.model.dto.estimate.EstimateDutyHistoryDTO" id="EstimateDutyHistoryMap">
        <id property="idAhcsEstimatDutyHistory" column="ID_AHCS_ESTIMATE_DUTY_HISTORY"/>
        <result property="createdBy" column="CREATED_BY"/>
        <result property="createdDate" column="CREATED_DATE"/>
        <result property="updatedBy" column="UPDATED_BY"/>
        <result property="updatedDate" column="UPDATED_DATE"/>
        <result property="policyNo" column="POLICY_NO"/>
        <result property="caseNo" column="CASE_NO"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="planCode" column="PLAN_CODE"/>
        <result property="dutyCode" column="DUTY_CODE"/>
        <result property="dutyName" column="DUTY_NAME"/>
        <result property="baseAmountPay" column="BASE_AMOUNT_PAY"/>
        <result property="dutyEstimateAmount" column="DUTY_ESTIMATE_AMOUNT"/>
        <result property="effectiveTime" column="EFFECTIVE_TIME"/>
        <result property="invalidateTime" column="INVALIDATE_TIME"/>
        <result property="migrateFrom" column="MIGRATE_FROM"/>
        <result property="arbitrageFee" column="ARBITRAGE_FEE"/>
        <result property="lawsuitFee" column="LAWSUIT_FEE"/>
        <result property="commonEstimateFee" column="COMMON_ESTIMATE_FEE"/>
        <result property="lawyerFee" column="LAWYER_FEE"/>
        <result property="executeFee" column="EXECUTE_FEE"/>
        <result property="verifyAppraiseFee" column="VERIFY_APPRAISE_FEE"/>
        <result property="inquireFee" column="INQUIRE_FEE"/>
        <result property="otherFee" column="OTHER_FEE"/>
        <result property="specialSurveyFee" column="SPECIAL_SURVEY_FEE"/>
        <result property="estimateType" column="ESTIMATE_TYPE"/>
        <result property="archiveTime" column="ARCHIVE_TIME"/>
        <result property="idPlyRiskProperty" column="ID_PLY_RISK_PROPERTY"/>
        <result property="idFlagHistoryChange" column="id_flag_history_change"/>
 		<result property="riskGroupNo" column="risk_group_no"/>
        <result property="riskGroupName" column="risk_group_name"/>
        <result property="planName" column="plan_name"/>
        <result property="taxRate" column="tax_rate"/>
    </resultMap>
    <resultMap type="com.paic.ncbs.claim.model.dto.estimate.EstimateChangePolicyFormDTO" id="RestartEstimateMap">
        <result property="reportNo" column="reportNo"/>
        <result property="caseTimes" column="case_times"/>
        <result property="registerDate" column="registerDate"/>
        <result property="sumDutyPayAmount" column="sumDutyPayAmount"/>
        <result property="sumDutyFeeAmount" column="sumDutyFeeAmount"/>
    </resultMap>


   <!-- <select id="getEstimateDutyHistoryList" resultMap="EstimateDutyHistoryMap">
        select d.CREATED_BY,
        d.CREATED_DATE,
        d.UPDATED_BY,
        d.UPDATED_DATE,
        d.ID_AHCS_ESTIMATE_DUTY_HISTORY,
        d.POLICY_NO,
        d.CASE_NO,
        d.CASE_TIMES,
        d.PLAN_CODE,
        d.DUTY_CODE,
        d.DUTY_NAME,
        d.BASE_AMOUNT_PAY,
        d.DUTY_ESTIMATE_AMOUNT,
        d.ARBITRAGE_FEE,
        d.LAWSUIT_FEE,
        d.COMMON_ESTIMATE_FEE,
        d.LAWYER_FEE,
        d.EXECUTE_FEE,
       d.VERIFY_APPRAISE_FEE,d.INQUIRE_FEE,d.OTHER_FEE,d.SPECIAL_SURVEY_FEE,
       d.ESTIMATE_TYPE
        from CLMS_estimate_duty_history d
        where d.CASE_TIMES = #{caseTimes,jdbcType = INTEGER}
        and d.CASE_NO = #{caseNo, jdbcType=VARCHAR}
        and d.PLAN_CODE = #{planCode,jdbcType = VARCHAR}
    </select>-->

    <select id="getSumPay" resultType="java.math.BigDecimal">
        select  sum(`DUTY_ESTIMATE_AMOUNT`)  from  clms_estimate_duty_history   where `POLICY_NO`
        = #{policyNo} and `DUTY_CODE`  =#{dutyCode}
    </select>

    <!-- 插入 -->
    <insert id="addHistoryRecord" parameterType="com.paic.ncbs.claim.model.dto.estimate.EstimateDutyHistoryDTO">
            insert into CLMS_estimate_duty_history(
        ID_AHCS_ESTIMATE_DUTY_HISTORY,
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            POLICY_NO,
            CASE_NO,
            CASE_TIMES,
            PLAN_CODE,
            DUTY_CODE,
            DUTY_NAME,
            BASE_AMOUNT_PAY,
            DUTY_ESTIMATE_AMOUNT,
            EFFECTIVE_TIME,
            ARBITRAGE_FEE,
            LAWSUIT_FEE,
            COMMON_ESTIMATE_FEE,
            LAWYER_FEE,
            EXECUTE_FEE,
            VERIFY_APPRAISE_FEE,INQUIRE_FEE,OTHER_FEE,SPECIAL_SURVEY_FEE,
            ESTIMATE_TYPE,
            MIGRATE_FROM,
            ARCHIVE_TIME,
            id_ply_risk_property,
            risk_group_no,
            risk_group_name,
        	id_flag_history_change,
        	chg_pay_value,
        ss_coins_pay_value,
        ss_coins_rate,
        ss_coins_chg_pay_value
            )
        <foreach collection="paramList" index="index" item="estimateDutyHistoryDTO" open="(" separator="union all" close=")">
        select
            #{estimateDutyHistoryDTO.idAhcsEstimatDutyHistory, jdbcType=VARCHAR},
            #{estimateDutyHistoryDTO.createdBy, jdbcType=VARCHAR},
            NOW(),
            #{estimateDutyHistoryDTO.updatedBy, jdbcType=VARCHAR},
            NOW(),
            #{estimateDutyHistoryDTO.policyNo, jdbcType=VARCHAR},
            #{estimateDutyHistoryDTO.caseNo, jdbcType=VARCHAR},
            #{estimateDutyHistoryDTO.caseTimes, jdbcType=INTEGER},
            #{estimateDutyHistoryDTO.planCode, jdbcType=VARCHAR},
            #{estimateDutyHistoryDTO.dutyCode, jdbcType=VARCHAR},
            #{estimateDutyHistoryDTO.dutyName, jdbcType=VARCHAR},
            #{estimateDutyHistoryDTO.baseAmountPay, jdbcType=DECIMAL},
            #{estimateDutyHistoryDTO.dutyEstimateAmount, jdbcType=DECIMAL},
            ifnull(#{estimateDutyHistoryDTO.effectiveTime},NOW()) effective_time,
            #{estimateDutyHistoryDTO.arbitrageFee, jdbcType=DECIMAL},
            #{estimateDutyHistoryDTO.lawsuitFee, jdbcType=DECIMAL},
            #{estimateDutyHistoryDTO.commonEstimateFee, jdbcType=DECIMAL},
            #{estimateDutyHistoryDTO.lawyerFee, jdbcType=DECIMAL},
            #{estimateDutyHistoryDTO.executeFee, jdbcType=DECIMAL},
            #{estimateDutyHistoryDTO.verifyAppraiseFee, jdbcType=DECIMAL},
            #{estimateDutyHistoryDTO.inquireFee, jdbcType=DECIMAL},
            #{estimateDutyHistoryDTO.otherFee, jdbcType=DECIMAL},
            #{estimateDutyHistoryDTO.specialSurveyFee, jdbcType=DECIMAL},
            #{estimateDutyHistoryDTO.estimateType,jdbcType=VARCHAR},
            ifnull(#{estimateDutyHistoryDTO.migrateFrom,jdbcType=VARCHAR},'n') migrateFrom,
            NOW() archive_time,
            #{estimateDutyHistoryDTO.idPlyRiskProperty,jdbcType=VARCHAR},
            #{estimateDutyHistoryDTO.riskGroupNo},
            #{estimateDutyHistoryDTO.riskGroupName},
            #{estimateDutyHistoryDTO.idFlagHistoryChange,jdbcType=VARCHAR},
            #{estimateDutyHistoryDTO.chgPayValue},
            #{estimateDutyHistoryDTO.ssCoinsPayValue},
            #{estimateDutyHistoryDTO.ssCoinsRate},
            #{estimateDutyHistoryDTO.ssCoinsChgPayValue}
            FROM DUAL
        </foreach>
    </insert>

    <!-- 更新ahcs_estimate_duty_history的失效时间 -->
    <update id="updateHistoryRecord">
        <foreach collection="caseNoList" item="item" open="begin" separator=";" close=";end;">
            update CLMS_estimate_duty_history d
            set d.updated_date = sysdate(),
            d.invalidate_time=sysdate()
            where d.case_no = #{item,jdbcType=VARCHAR}
            and d.case_times = #{caseTimes,jdbcType=INTEGER}
            and d.invalidate_time is null
        </foreach>
    </update>

    <select id="getEstimateDutyHistoryList" resultMap="EstimateDutyHistoryMap">
        select d.CREATED_BY,
        d.CREATED_DATE,
        d.UPDATED_BY,
        d.UPDATED_DATE,
        d.ID_AHCS_ESTIMATE_DUTY_HISTORY,
        d.POLICY_NO,
        d.CASE_NO,
        d.CASE_TIMES,
        d.PLAN_CODE,
        d.DUTY_CODE,
        d.DUTY_NAME,
        d.BASE_AMOUNT_PAY,
        d.DUTY_ESTIMATE_AMOUNT,
        d.ARBITRAGE_FEE,
        d.LAWSUIT_FEE,
        d.COMMON_ESTIMATE_FEE,
        d.LAWYER_FEE,
        d.EXECUTE_FEE,
        d.VERIFY_APPRAISE_FEE,
        d.INQUIRE_FEE,
        d.OTHER_FEE,
        d.SPECIAL_SURVEY_FEE,
        d.ESTIMATE_TYPE,
        d.ARCHIVE_TIME,
        d.id_flag_history_change,
        d.risk_group_no,
        d.risk_group_name
        from CLMS_estimate_duty_history d,
        CLMS_estimate_policy y
        where y.report_no = #{reportNo, jdbcType=VARCHAR}
        and y.CASE_NO = d.CASE_NO
        and d.CASE_TIMES = y.CASE_TIMES
        and d.ESTIMATE_TYPE = '02'
        order by CREATED_DATE desc
    </select>

    <select id="getRestartEstimateDutyList" resultMap="RestartEstimateMap">
        select
        (SELECT DISTINCT ccb.REPORT_NO FROM clm_case_base ccb WHERE ccb.CASE_NO = t.CASE_NO LIMIT 1 ) AS reportNo,
        t.created_date as registerDate,
        t.case_times,
        t.sumDutyPayAmount,
        (t.ARBITRAGE_FEE + t.LAWSUIT_FEE + t.COMMON_ESTIMATE_FEE + t.LAWYER_FEE + t.EXECUTE_FEE +
        t.VERIFY_APPRAISE_FEE + t.INQUIRE_FEE + t.OTHER_FEE + t.SPECIAL_SURVEY_FEE) AS sumDutyFeeAmount
        from(SELECT
        edr.CASE_NO,
        max(edr.created_date) as created_date,
        edr.case_times,
        ifnull(sum( edr.ESTIMATE_AMOUNT ), 0 ) AS sumDutyPayAmount,
        ifnull(sum( edr.ARBITRAGE_FEE ), 0 ) as ARBITRAGE_FEE,
        ifnull( sum( edr.LAWSUIT_FEE ), 0 ) as LAWSUIT_FEE,
        ifnull( sum( edr.COMMON_ESTIMATE_FEE ), 0 ) COMMON_ESTIMATE_FEE,
        ifnull( sum( edr.LAWYER_FEE ), 0 ) LAWYER_FEE,
        ifnull( sum( edr.EXECUTE_FEE ), 0 ) EXECUTE_FEE,
        ifnull( sum( edr.VERIFY_APPRAISE_FEE ), 0 ) VERIFY_APPRAISE_FEE,
        ifnull( sum( edr.INQUIRE_FEE ), 0 ) INQUIRE_FEE,
        ifnull( sum( edr.OTHER_FEE ), 0 ) OTHER_FEE,
        ifnull( sum( edr.SPECIAL_SURVEY_FEE ), 0 ) SPECIAL_SURVEY_FEE
        FROM
        CLMS_ESTIMATE_DUTY_RECORD edr
        WHERE
        edr.case_no = #{caseNo, jdbcType=VARCHAR}
        AND edr.case_times = #{caseTimes,jdbcType = INTEGER}
        AND edr.ESTIMATE_TYPE = '05'
        AND edr.IS_EFFECTIVE = 'Y'
        GROUP BY
        edr.case_times,edr.CASE_NO) t
    </select>

    <select id="getEstimatePlanCodeList" resultType="com.paic.ncbs.claim.model.dto.estimate.EstimateChangePlanDTO">
        select
            y.policy_no,
            y.case_times,
            plan.plan_code,
            plan.plan_name,
            plan.risk_group_no,
        	plan.risk_group_name
        from  clms_estimate_policy y,clms_estimate_plan plan
        where y.report_no = #{reportNo, jdbcType=VARCHAR}
        and y.case_times = #{caseTimes,jdbcType = INTEGER}
        and y.ID_AHCS_ESTIMATE_POLICY = plan.ID_AHCS_ESTIMATE_POLICY
    </select>

    <select id="getDutyHistoryListByIdFlag" resultMap="EstimateDutyHistoryMap">
        select
            d.ID_AHCS_ESTIMATE_DUTY_HISTORY,
            d.POLICY_NO,
            d.CASE_NO,
            d.CASE_TIMES,
            d.PLAN_CODE,
            d.DUTY_CODE,
            d.DUTY_NAME,
            d.BASE_AMOUNT_PAY,
            d.DUTY_ESTIMATE_AMOUNT,
            d.ARBITRAGE_FEE,
            d.LAWSUIT_FEE,
            d.COMMON_ESTIMATE_FEE,
            d.LAWYER_FEE,
            d.EXECUTE_FEE,
            d.VERIFY_APPRAISE_FEE,
            d.INQUIRE_FEE,
            d.OTHER_FEE,
            d.SPECIAL_SURVEY_FEE,
            d.ESTIMATE_TYPE,
            d.ARCHIVE_TIME,
            d.id_flag_history_change,
            d.risk_group_no,
            d.risk_group_name,
            (select plan_name from clms_policy_info a, clms_policy_plan b where a.id_ahcs_policy_info = b.id_ahcs_policy_info
           	 and a.case_no = d.case_no and b.plan_code = d.plan_code and b.risk_group_no = d.risk_group_no) as plan_name,
           	(select tax_rate from clms_policy_info a, clms_policy_plan b where a.id_ahcs_policy_info = b.id_ahcs_policy_info
           	 and a.case_no = d.case_no and b.plan_code = d.plan_code and b.risk_group_no = d.risk_group_no) as tax_rate
        from CLMS_estimate_duty_history d
        where d.id_flag_history_change = #{idFlagHistoryChange}
    </select>

    <update id="updateEstimateDateByCaseNo">
        UPDATE CLMS_ESTIMATE_DUTY_HISTORY
        SET UPDATED_DATE = NOW(),
            EFFECTIVE_TIME = NOW()
        WHERE
            CASE_NO = #{caseNo, jdbcType=VARCHAR}
            AND ESTIMATE_TYPE = '02'
    </update>
</mapper>