<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.lawsuit.ClmsLawsuitCaseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.lawsuit.ClmsLawsuitCase">
        <id column="id" property="id" />
        <result column="policy_no" property="policyNo" />
        <result column="report_no" property="reportNo" />
        <result column="case_times" property="caseTimes" />
        <result column="lawsuit_no" property="lawsuitNo" />
        <result column="insured_name" property="insuredName" />
        <result column="case_status" property="caseStatus" />
        <result column="hearing_type" property="hearingType" />
        <result column="lawsuit_reason" property="lawsuitReason" />
        <result column="plaintiff" property="plaintiff" />
        <result column="p_phone" property="pPhone" />
        <result column="defendant" property="defendant" />
        <result column="d_phone" property="dPhone" />
        <result column="lawyer" property="lawyer" />
        <result column="l_phone" property="lPhone" />
        <result column="court" property="court" />
        <result column="court_case_no" property="courtCaseNo" />
        <result column="hearing_date" property="hearingDate" />
        <result column="material_date" property="materialDate" />
        <result column="judgment_date" property="judgmentDate" />
        <result column="apply_amount" property="applyAmount" />
        <result column="update_amount" property="updateAmount" />
        <result column="final_amount" property="finalAmount" />
        <result column="opponent_fee" property="opponentFee" />
        <result column="our_fee" property="ourFee" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="court_clerk" property="courtClerk" />
        <result column="judge_name" property="judgeName" />
        <result column="judge_phone" property="judgePhone" />
        <result column="hearing_result" property="hearingResult" />
        <result column="is_pre_settle" property="isPreSettle" />
        <result column="is_appeal" property="isAppeal" />
        <result column="is_finished" property="isFinished" />
        <result column="is_won" property="isWon" />
        <result column="loss_reduction" property="lossReduction" />
        <result column="win_lose_reason" property="winLoseReason" />
        <result column="approver" property="approver" />
        <result column="approver_name" property="approverName" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
    </resultMap>

    <insert id="saveClmsLawsuitCase" parameterType="com.paic.ncbs.claim.dao.entity.lawsuit.ClmsLawsuitCase">
        INSERT INTO clms_lawsuit_case (
        id,
        policy_no,
        report_no,
        case_times,
        lawsuit_no,
        insured_name,
        case_status,
        hearing_type,
        lawsuit_reason,
        plaintiff,
        p_phone,
        defendant,
        d_phone,
        lawyer,
        l_phone,
        court,
        court_case_no,
        hearing_date,
        material_date,
        judgment_date,
        apply_amount,
        update_amount,
        final_amount,
        opponent_fee,
        our_fee,
        start_date,
        end_date,
        court_clerk,
        judge_name,
        judge_phone,
        hearing_result,
        is_pre_settle,
        is_appeal,
        is_finished,
        is_won,
        loss_reduction,
        win_lose_reason,
        approver,
        approver_name,
        status,
        created_by,
        sys_ctime,
        updated_by,
        sys_utime
        ) VALUES (
        #{id},
        #{policyNo},
        #{reportNo},
        #{caseTimes},
        #{lawsuitNo},
        #{insuredName},
        #{caseStatus},
        #{hearingType},
        #{lawsuitReason},
        #{plaintiff},
        #{pPhone},
        #{defendant},
        #{dPhone},
        #{lawyer},
        #{lPhone},
        #{court},
        #{courtCaseNo},
        #{hearingDate},
        #{materialDate},
        #{judgmentDate},
        #{applyAmount},
        #{updateAmount},
        #{finalAmount},
        #{opponentFee},
        #{ourFee},
        #{startDate},
        #{endDate},
        #{courtClerk},
        #{judgeName},
        #{judgePhone},
        #{hearingResult},
        #{isPreSettle},
        #{isAppeal},
        #{isFinished},
        #{isWon},
        #{lossReduction},
        #{winLoseReason},
        #{approver},
        #{approverName},
        #{status},
        #{createdBy},
        NOW(),
        #{updatedBy},
        NOW()
        )
    </insert>

    <update id="updateClmsLawsuitCase" parameterType="com.paic.ncbs.claim.dao.entity.lawsuit.ClmsLawsuitCase">
        UPDATE clms_lawsuit_case
        <set>
            <if test="policyNo != null and policyNo != ''">
                policy_no = #{policyNo},
            </if>
            <if test="reportNo != null and reportNo != ''">
                report_no = #{reportNo},
            </if>
            <if test="caseTimes != null">
                case_times = #{caseTimes},
            </if>
            <if test="lawsuitNo != null and lawsuitNo != ''">
                lawsuit_no = #{lawsuitNo},
            </if>
            <if test="insuredName != null and insuredName != ''">
                insured_name = #{insuredName},
            </if>
            <if test="caseStatus != null and caseStatus != ''">
                case_status = #{caseStatus},
            </if>
            <if test="hearingType != null and hearingType != ''">
                hearing_type = #{hearingType},
            </if>
            <if test="lawsuitReason != null and lawsuitReason != ''">
                lawsuit_reason = #{lawsuitReason},
            </if>
            <if test="plaintiff != null and plaintiff != ''">
                plaintiff = #{plaintiff},
            </if>
            <if test="pPhone != null and pPhone != ''">
                p_phone = #{pPhone},
            </if>
            <if test="defendant != null and defendant != ''">
                defendant = #{defendant},
            </if>
            <if test="dPhone != null and dPhone != ''">
                d_phone = #{dPhone},
            </if>
            <if test="lawyer != null and lawyer != ''">
                lawyer = #{lawyer},
            </if>
            <if test="lPhone != null and lPhone != ''">
                l_phone = #{lPhone},
            </if>
            <if test="court != null and court != ''">
                court = #{court},
            </if>
            <if test="courtCaseNo != null and courtCaseNo != ''">
                court_case_no = #{courtCaseNo},
            </if>
            <if test="hearingDate != null">
                hearing_date = #{hearingDate},
            </if>
            <if test="materialDate != null">
                material_date = #{materialDate},
            </if>
            <if test="judgmentDate != null">
                judgment_date = #{judgmentDate},
            </if>
            <if test="applyAmount != null">
                apply_amount = #{applyAmount},
            </if>
            <if test="updateAmount != null">
                update_amount = #{updateAmount},
            </if>
            <if test="finalAmount != null">
                final_amount = #{finalAmount},
            </if>
            <if test="opponentFee != null">
                opponent_fee = #{opponentFee},
            </if>
            <if test="ourFee != null">
                our_fee = #{ourFee},
            </if>
            <if test="startDate != null">
                start_date = #{startDate},
            </if>
            <if test="endDate != null">
                end_date = #{endDate},
            </if>
            <if test="courtClerk != null and courtClerk != ''">
                court_clerk = #{courtClerk},
            </if>
            <if test="judgeName != null and judgeName != ''">
                judge_name = #{judgeName},
            </if>
            <if test="judgePhone != null and judgePhone != ''">
                judge_phone = #{judgePhone},
            </if>
            <if test="hearingResult != null and hearingResult != ''">
                hearing_result = #{hearingResult},
            </if>
            <if test="isPreSettle != null and isPreSettle != ''">
                is_pre_settle = #{isPreSettle},
            </if>
            <if test="isAppeal != null and isAppeal != ''">
                is_appeal = #{isAppeal},
            </if>
            <if test="isFinished != null and isFinished != ''">
                is_finished = #{isFinished},
            </if>
            <if test="isWon != null and isWon != ''">
                is_won = #{isWon},
            </if>
            <if test="lossReduction != null">
                loss_reduction = #{lossReduction},
            </if>
            <if test="winLoseReason != null and winLoseReason != ''">
                win_lose_reason = #{winLoseReason},
            </if>
            <if test="approver != null and approver != ''">
                approver = #{approver},
            </if>
            <if test="approverName != null and approverName != ''">
                approver_name = #{approverName},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="sysUtime != null">
                sys_utime = NOW()
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteClmsLawsuitCase">
        DELETE FROM clms_lawsuit_case
        WHERE id = #{id}
    </delete>

    <select id="getLawsuitCaseById" resultType="com.paic.ncbs.claim.model.vo.lawsuit.ClmsLawsuitAuditCaseVO">
        SELECT
        c.id,
        c.policy_no,
        c.report_no,
        c.case_times,
        c.lawsuit_no,
        c.insured_name,
        c.case_status,
        c.hearing_type,
        c.lawsuit_reason,
        c.plaintiff,
        c.p_phone,
        c.defendant,
        c.d_phone,
        c.lawyer,
        c.l_phone,
        c.court,
        c.court_case_no,
        c.hearing_date,
        c.material_date,
        c.judgment_date,
        c.apply_amount,
        c.update_amount,
        c.final_amount,
        c.opponent_fee,
        c.our_fee,
        c.start_date,
        c.end_date,
        c.court_clerk,
        c.judge_name,
        c.judge_phone,
        c.hearing_result,
        c.is_pre_settle,
        c.is_appeal,
        c.is_finished,
        c.is_won,
        c.loss_reduction,
        c.win_lose_reason,
        c.approver,
        c.approver_name,
        audit.submitter,
        audit.submitter_name,
        audit.submit_time,
        audit.audit_opinion,
        audit.audit_department,
        audit.audit_code,
        audit.audit_name,
        audit.status,
        audit.sys_utime,
        audit.opinion_desc
        FROM clms_lawsuit_case as c
        INNER JOIN clms_lawsuit_audit as audit
        ON c.id = audit.lawsuit_case_id
        WHERE c.id = #{id}
        ORDER BY c.lawsuit_no ASC
    </select>

    <select id="getClmsLawsuitCase" resultType="com.paic.ncbs.claim.dao.entity.lawsuit.ClmsLawsuitCase">
        SELECT
        id,
        policy_no,
        report_no,
        case_times,
        lawsuit_no,
        insured_name,
        case_status,
        hearing_type,
        lawsuit_reason,
        plaintiff,
        p_phone,
        defendant,
        d_phone,
        lawyer,
        l_phone,
        court,
        court_case_no,
        hearing_date,
        material_date,
        judgment_date,
        apply_amount,
        update_amount,
        final_amount,
        opponent_fee,
        our_fee,
        start_date,
        end_date,
        court_clerk,
        judge_name,
        judge_phone,
        hearing_result,
        is_pre_settle,
        is_appeal,
        is_finished,
        is_won,
        loss_reduction,
        win_lose_reason,
        approver,
        approver_name,
        status
        FROM clms_lawsuit_case
        WHERE report_no = #{reportNo}
        AND case_times = #{caseTimes}
        ORDER BY sys_ctime DESC LIMIT 1
    </select>

    <select id="getALawyerForYourCase" resultType="java.lang.Object">
        SELECT
        entrust_name
        FROM clms_entrust_main
        WHERE report_no = #{reportNo}
        AND case_times = #{caseTimes}
        AND third_party_type = '02'
        AND entrust_status = '3'
    </select>

    <select id="getClmsLawsuit" resultType="com.paic.ncbs.claim.model.vo.lawsuit.ClmsLawsuitAuditCaseVO">
        SELECT
        lawsuit_case.id,
        lawsuit_case.lawsuit_no,
        lawsuit_case.approver,
        lawsuit_case.approver_name,
        audit.submitter,
        audit.submitter_name,
        audit.submit_time,
        audit.status,
        audit.audit_code,
        audit.audit_name
        FROM clms_lawsuit_audit audit
        INNER JOIN clms_lawsuit_case lawsuit_case
        ON audit.lawsuit_case_id = lawsuit_case.id
        WHERE lawsuit_case.report_no = #{reportNo}
        AND lawsuit_case.case_times = #{caseTimes}
        ORDER BY lawsuit_case.lawsuit_no ASC
    </select>

    <select id="getClmsLawsuitCount" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM clms_lawsuit_case
        WHERE
        report_no = #{reportNo}
        AND case_times = #{caseTimes}
    </select>


</mapper>
