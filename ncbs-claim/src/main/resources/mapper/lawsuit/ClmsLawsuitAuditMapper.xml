<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.lawsuit.ClmsLawsuitAuditMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.lawsuit.ClmsLawsuitAudit">
        <id column="id" property="id" />
        <result column="lawsuit_case_id" property="lawsuitCaseId" />
        <result column="policy_no" property="policyNo" />
        <result column="report_no" property="reportNo" />
        <result column="case_times" property="caseTimes" />
        <result column="lawsuit_no" property="lawsuitNo" />
        <result column="submitter" property="submitter" />
        <result column="submitter_name" property="submitterName" />
        <result column="submit_time" property="submitTime" />
        <result column="audit_opinion" property="auditOpinion" />
        <result column="audit_department" property="auditDepartment" />
        <result column="audit_code" property="auditCode" />
        <result column="audit_name" property="auditName" />
        <result column="status" property="status" />
        <result column="opinion_desc" property="opinionDesc" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
    </resultMap>

    <insert id="saveClmsLawsuitAudit" parameterType="com.paic.ncbs.claim.dao.entity.lawsuit.ClmsLawsuitAudit">
        INSERT INTO clms_lawsuit_audit (
        id,
        lawsuit_case_id,
        policy_no,
        report_no,
        case_times,
        lawsuit_no,
        submitter,
        submitter_name,
        submit_time,
        audit_opinion,
        audit_department,
        audit_code,
        audit_name,
        status,
        opinion_desc,
        created_by,
        sys_ctime,
        updated_by,
        sys_utime
        ) VALUES (
        replace(UUID(), '-', ''),
        #{lawsuitCaseId},
        #{policyNo},
        #{reportNo},
        #{caseTimes},
        #{lawsuitNo},
        #{submitter},
        #{submitterName},
        NOW(),
        #{auditOpinion},
        #{auditDepartment},
        #{auditCode},
        #{auditName},
        #{status},
        #{opinionDesc},
        #{createdBy},
        NOW(),
        #{updatedBy},
        NOW()
        )
    </insert>

    <update id="updateClmsLawsuitAudit" parameterType="com.paic.ncbs.claim.dao.entity.lawsuit.ClmsLawsuitAudit">
        UPDATE clms_lawsuit_audit
        <set>
            <if test="lawsuitCaseId != null and lawsuitCaseId != ''">
                lawsuit_case_id = #{lawsuitCaseId},
            </if>
            <if test="policyNo != null and policyNo != ''">
                policy_no = #{policyNo},
            </if>
            <if test="reportNo != null and reportNo != ''">
                report_no = #{reportNo},
            </if>
            <if test="caseTimes != null">
                case_times = #{caseTimes},
            </if>
            <if test="lawsuitNo != null and lawsuitNo != ''">
                lawsuit_no = #{lawsuitNo},
            </if>
            <if test="submitter != null and submitter != ''">
                submitter = #{submitter},
            </if>
            <if test="submitterName != null and submitterName != ''">
                submitter_name = #{submitterName},
            </if>
            <if test="submitTime != null">
                submit_time = #{submitTime},
            </if>
            <if test="auditOpinion != null and auditOpinion != ''">
                audit_opinion = #{auditOpinion},
            </if>
            <if test="auditDepartment != null and auditDepartment != ''">
                audit_department = #{auditDepartment},
            </if>
            <if test="auditCode != null and auditCode != ''">
                audit_code = #{auditCode},
            </if>
            <if test="auditName != null and auditName != ''">
                audit_name = #{auditName},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="opinionDesc != null and opinionDesc != ''">
                opinion_desc = #{opinionDesc},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="sysUtime != null">
                sys_utime = NOW()
            </if>
        </set>
        WHERE lawsuit_case_id = #{lawsuitCaseId}
    </update>

    <delete id="deleteClmsLawsuitAudit">
        DELETE FROM clms_lawsuit_audit WHERE lawsuit_case_id = #{lawsuitCaseId}
    </delete>

    <select id="getLawsuitAuditById" resultType="com.paic.ncbs.claim.dao.entity.lawsuit.ClmsLawsuitAudit">
        SELECT
        id,
        lawsuit_case_id,
        policy_no,
        report_no,
        case_times,
        lawsuit_no,
        submitter,
        submitter_name,
        submit_time,
        audit_opinion,
        audit_department,
        audit_code,
        audit_name,
        status,
        opinion_desc
        FROM clms_lawsuit_audit
        WHERE lawsuit_case_id = #{lawsuitCaseId}
    </select>

    <select id="getClmsLawsuitAudit" resultType="com.paic.ncbs.claim.model.vo.lawsuit.ClmsLawsuitAuditCaseVO">
        SELECT
        audit.id,
        audit.lawsuit_case_id,
        audit.policy_no,
        audit.report_no,
        audit.case_times,
        audit.lawsuit_no,
        audit.submitter,
        audit.submitter_name,
        audit.audit_code,
        audit.audit_name,
        lawsuit_case.insured_name,
        lawsuit_case.case_status
        FROM clms_lawsuit_audit audit
        INNER JOIN clms_lawsuit_case lawsuit_case
        ON audit.lawsuit_case_id = lawsuit_case.id
        WHERE audit.status = '1'
        ORDER BY audit.lawsuit_no ASC
    </select>

    <select id="getClmsLawsuitAuditConcluded"
            resultType="com.paic.ncbs.claim.model.vo.lawsuit.ClmsLawsuitAuditCaseVO">
        SELECT
        audit.id,
        audit.lawsuit_case_id,
        audit.policy_no,
        audit.report_no,
        audit.case_times,
        audit.lawsuit_no,
        audit.submitter,
        audit.submitter_name,
        audit.audit_code,
        audit.audit_name,
        lawsuit_case.insured_name,
        lawsuit_case.case_status
        FROM clms_lawsuit_audit audit
        INNER JOIN clms_lawsuit_case lawsuit_case
        ON audit.lawsuit_case_id = lawsuit_case.id
        WHERE audit.status = '2'
        ORDER BY audit.lawsuit_no ASC
    </select>


</mapper>
