<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.accident.HugeAccidentInfoMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.accident.HugeAccidentInfoDTO" id="HugeAccidentInfoMap">
        <id property="idAhcsHugeAccidentInfo" column="ID_AHCS_HUGE_ACCIDENT_INFO"/>
        <result property="createdBy" column="CREATED_BY"/>
        <result property="createdDate" column="CREATED_DATE"/>
        <result property="updatedBy" column="UPDATED_BY"/>
        <result property="updatedDate" column="UPDATED_DATE"/>
        <result property="accidentName" column="ACCIDENT_NAME"/>
        <result property="accidentType" column="ACCIDENT_TYPE"/>
        <result property="accidentTypeName" column="ACCIDENT_TYPE_NAME"/>
        <result property="startDate" column="START_DATE"/>
        <result property="endDate" column="END_DATE"/>
        <result property="isEffective" column="IS_EFFECTIVE"/>
        <result property="accidentCode" column="ACCIDENT_CODE"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.vo.accident.HugeAccidentInfoVO" extends="HugeAccidentInfoMap"
               id="HugeAccidentInfoVOMap">
        <result property="accidentTypeName" column="ACCIDENT_TYPE_NAME"/>

        <collection property="accidentAddressVOs" column="ID_AHCS_HUGE_ACCIDENT_INFO"
                    ofType="com.paic.ncbs.claim.model.vo.accident.HugeAccidentAddressVO"
                    select="com.paic.ncbs.claim.dao.mapper.accident.HugeAccidentAddressMapper.getHugeAccidentAddressVOsByHugeId">
        </collection>


        <collection property="accidentMeasureVOs" column="ID_AHCS_HUGE_ACCIDENT_INFO"
                    ofType="com.paic.ncbs.claim.model.vo.accident.HugeAccidentMeasureVO"
                    select="com.paic.ncbs.claim.dao.mapper.accident.HugeAccidentMeasureMapper.getHugeAccidentMeasureVOsByHugeId">
        </collection>
    </resultMap>


    <insert id="addHugeAccidentInfo" parameterType="com.paic.ncbs.claim.model.dto.accident.HugeAccidentInfoDTO">
        INSERT INTO CLMS_huge_accident_info (
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_HUGE_ACCIDENT_INFO,
        ACCIDENT_NAME,
        ACCIDENT_TYPE,
        START_DATE,
        END_DATE,
        IS_EFFECTIVE,
        ACCIDENT_CODE
        )
        VALUES (
        #{createdBy ,jdbcType=VARCHAR},
        sysdate(),
        #{updatedBy ,jdbcType=VARCHAR},
        sysdate(),
        #{idAhcsHugeAccidentInfo ,jdbcType=VARCHAR},
        #{accidentName ,jdbcType=VARCHAR},
        #{accidentType ,jdbcType=VARCHAR},
        #{startDate ,jdbcType=DATE},
        #{endDate ,jdbcType=DATE},
        #{isEffective,jdbcType=VARCHAR},
        #{accidentCode}
        )
    </insert>

    <update id="modifyHugeAccidentInfo"
            parameterType="com.paic.ncbs.claim.model.dto.accident.HugeAccidentInfoDTO">
        UPDATE CLMS_huge_accident_info
        <set>
            <if test="updatedBy != null and updatedBy != '' ">
                UPDATED_BY = #{updatedBy},
            </if>
            UPDATED_DATE=sysdate(),

            <if test="accidentName != null and accidentName != '' ">
                ACCIDENT_NAME = #{accidentName},
            </if>

            <if test="accidentType != null and accidentType != '' ">
                ACCIDENT_TYPE = #{accidentType},
            </if>

            <if test="startDate != null ">
                START_DATE = #{startDate},
            </if>

            <if test="endDate != null ">
                END_DATE = #{endDate},
            </if>

            <if test="endDate == null ">
                END_DATE = null,
            </if>

            <if test="accidentCode != null and accidentCode != '' ">
                ACCIDENT_CODE = #{accidentCode},
            </if>
        </set>
        WHERE ID_AHCS_HUGE_ACCIDENT_INFO=#{idAhcsHugeAccidentInfo}
    </update>

    <update id="removeHugeAccidentInfoById" parameterType="String">
        update CLMS_huge_accident_info
        set IS_EFFECTIVE = 'N'
        where ID_AHCS_HUGE_ACCIDENT_INFO = #{idAhcsHugeAccidentInfo}
    </update>

    <select id="queryOneByCondition" resultMap="HugeAccidentInfoMap">
        select  CREATED_BY,
                CREATED_DATE,
                UPDATED_BY,
                UPDATED_DATE,
                ID_AHCS_HUGE_ACCIDENT_INFO,
                ACCIDENT_NAME,
                ACCIDENT_TYPE,
                START_DATE,
                END_DATE,
                IS_EFFECTIVE,
                ACCIDENT_CODE
        from clms_huge_accident_info
        <where>
            <if test="accidentName != null and accidentName != '' ">
                AND ACCIDENT_NAME = #{accidentName}
            </if>
            <if test="accidentCode != null and accidentCode != '' ">
                AND ACCIDENT_CODE = #{accidentCode}
            </if>
            and IS_EFFECTIVE = 'Y'
        </where>
        limit 1
    </select>

    <select id="getHugeAccidentInfoCount" resultType="int">
        select count(1)
        from CLMS_huge_accident_info
        <where>
            <if test="idAhcsHugeAccidentInfo != null and idAhcsHugeAccidentInfo != '' ">
                AND ID_AHCS_HUGE_ACCIDENT_INFO = #{idAhcsHugeAccidentInfo,jdbcType=VARCHAR}
            </if>
            <if test="accidentName != null and accidentName != '' ">
                AND ACCIDENT_NAME = #{accidentName,jdbcType=VARCHAR}
            </if>
            <if test="accidentType != null and accidentType != '' ">
                AND ACCIDENT_TYPE = #{accidentType,jdbcType=VARCHAR}
            </if>
            <if test="startDate != null ">
                AND START_DATE = #{startDate,jdbcType=DATE},
            </if>
            <if test="endDate != null ">
                AND END_DATE = #{endDate,jdbcType=DATE},
            </if>
            and IS_EFFECTIVE='Y'
        </where>
    </select>

    <select id="getHugeAccidentInfo" resultMap="HugeAccidentInfoVOMap">
        select
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_HUGE_ACCIDENT_INFO,
        ACCIDENT_NAME,
        ACCIDENT_TYPE,
        START_DATE,
        END_DATE,
        IS_EFFECTIVE,
        (select VALUE_CHINESE_NAME from clm_common_parameter where value_code=ACCIDENT_TYPE limit 1) as
        ACCIDENT_TYPE_NAME
        from CLMS_huge_accident_info
        <where>
            <if test="idAhcsHugeAccidentInfo != null and idAhcsHugeAccidentInfo != '' ">
                AND ID_AHCS_HUGE_ACCIDENT_INFO = #{idAhcsHugeAccidentInfo,jdbcType=VARCHAR}
            </if>
            <if test="accidentName != null and accidentName != '' ">
                AND ACCIDENT_NAME = #{accidentName,jdbcType=VARCHAR}
            </if>
            <if test="accidentType != null and accidentType != '' ">
                AND ACCIDENT_TYPE = #{accidentType,jdbcType=VARCHAR}
            </if>
            <if test="startDate != null and endDate != null ">
                AND START_DATE&lt;#{startDate,jdbcType=DATE}
                AND END_DATE&gt;#{endDate,jdbcType=DATE}
            </if>
            <if test="startDate != null and endDate == null ">
                AND (
                START_DATE&lt;#{startDate,jdbcType=DATE} AND END_DATE&gt;#{startDate,jdbcType=DATE}
                or
                START_DATE = #{startDate,jdbcType=DATE} AND END_DATE is null
                )
            </if>


            <if test="certificateNo != null and certificateNo != '' ">
                AND ID_AHCS_HUGE_ACCIDENT_INFO in
                (
                select a.ID_AHCS_HUGE_ACCIDENT_INFO
                from CLMS_huge_accident_customer a
                where CERTIFICATE_NO = #{certificateNo,jdbcType=VARCHAR}
                <if test="insuredName != null and insuredName != '' ">
                    AND INSURED_NAME = #{insuredName,jdbcType=VARCHAR}
                </if>
                )
            </if>

            <if test="provinceCode != null and provinceCode != '' ">
                AND ID_AHCS_HUGE_ACCIDENT_INFO in
                (
                select b.ID_AHCS_HUGE_ACCIDENT_INFO
                from CLMS_huge_accident_address b
                where
                b.province_code = #{provinceCode,jdbcType=VARCHAR}
                <if test="accidentCityCode != null and accidentCityCode != '' ">
                    AND b.accident_city_code = #{accidentCityCode,jdbcType=VARCHAR}
                </if>
                <if test="accidentCountyCode != null and accidentCountyCode != '' ">
                    AND b.accident_county_code = #{accidentCountyCode,jdbcType=VARCHAR}
                </if>
                )
            </if>

            and IS_EFFECTIVE='Y'
        </where>
    </select>


</mapper>