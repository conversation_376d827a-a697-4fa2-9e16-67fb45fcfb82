<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.accident.HugeAccidentAddressMapper">

	<resultMap type="com.paic.ncbs.claim.model.dto.accident.HugeAccidentAddressDTO" id="HugeAccidentAddressMap">
		<id property="idAhcsHugeAccidentAddress" column="ID_AHCS_HUGE_ACCIDENT_ADDRESS" />
		<result property="createdBy" column="CREATED_BY" />
		<result property="createdDate" column="CREATED_DATE" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="updatedDate" column="UPDATED_DATE" />
		<result property="idAhcsHugeAccidentInfo" column="ID_AHCS_HUGE_ACCIDENT_INFO" />
		<result property="provinceCode" column="PROVINCE_CODE" />
		<result property="accidentCityCode" column="ACCIDENT_CITY_CODE" />
		<result property="accidentCountyCode" column="ACCIDENT_COUNTY_CODE" />
	</resultMap>
	
	
	<resultMap type="com.paic.ncbs.claim.model.vo.accident.HugeAccidentAddressVO" extends="HugeAccidentAddressMap" id="HugeAccidentAddressVOMap">
		<result property="provinceCodeName" column="PROVINCE_CODE_NAME" />
		<result property="accidentCityCodeName" column="ACCIDENT_CITY_CODE_NAME" />
		<result property="accidentCountyCodeName" column="ACCIDENT_COUNTY_CODE_NAME" />
	</resultMap>
	


	<select id="getHugeAccidentAddressVOsByHugeId" resultMap="HugeAccidentAddressVOMap">
		select 
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_HUGE_ACCIDENT_ADDRESS,
			ID_AHCS_HUGE_ACCIDENT_INFO,
			PROVINCE_CODE,
			ACCIDENT_CITY_CODE,
			ACCIDENT_COUNTY_CODE,
			
			(select VALUE_CHINESE_NAME from clm_common_parameter where collection_code='PC00' and value_code=PROVINCE_CODE limit 1) as PROVINCE_CODE_NAME,
			(select city_chinese_name from city_define t where city_code=ACCIDENT_CITY_CODE limit 1) as ACCIDENT_CITY_CODE_NAME,
			(select city_chinese_name from city_define t where city_code=ACCIDENT_COUNTY_CODE limit 1) as ACCIDENT_COUNTY_CODE_NAME
		
		from CLMS_huge_accident_address
		where  ID_AHCS_HUGE_ACCIDENT_INFO=#{idAhcsHugeAccidentInfo} 
	</select>
	

</mapper>