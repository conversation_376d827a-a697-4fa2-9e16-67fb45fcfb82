<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.accident.HugeAccidentMeasureMapper">

	<resultMap type="com.paic.ncbs.claim.model.dto.accident.HugeAccidentMeasureDTO" id="HugeAccidentMeasureMap">
		<id property="idAhcsHugeAccidentMeasure" column="ID_AHCS_HUGE_ACCIDENT_MEASURE" />
		<result property="createdBy" column="CREATED_BY" />
		<result property="createdDate" column="CREATED_DATE" />
		<result property="updatedBy" column="UPDATED_BY" />
		<result property="updatedDate" column="UPDATED_DATE" />
		<result property="idAhcsHugeAccidentInfo" column="ID_AHCS_HUGE_ACCIDENT_INFO" />
		<result property="measureType" column="MEASURE_TYPE" />
		<result property="measureCode" column="MEASURE_CODE" />
	</resultMap>
	
	<resultMap type="com.paic.ncbs.claim.model.vo.accident.HugeAccidentMeasureVO" extends="HugeAccidentMeasureMap" id="HugeAccidentMeasureVOMap">
		<result property="measureTypeName" column="MEASURE_TYPE_NAME" />
		
	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.vo.accident.MeasureVO" id="MeasureVOMap">
		<result property="measureCode" column="value_code" />
		<result property="measureName" column="VALUE_CHINESE_NAME" />
	</resultMap>
	

	<select id="getHugeAccidentMeasureVOsByHugeId" resultMap="HugeAccidentMeasureVOMap">
		select 
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_HUGE_ACCIDENT_MEASURE,
			ID_AHCS_HUGE_ACCIDENT_INFO,
			MEASURE_TYPE,
			MEASURE_CODE,
			
			(select VALUE_CHINESE_NAME from clm_common_parameter where value_code=MEASURE_TYPE limit 1) as MEASURE_TYPE_NAME
			
		from CLMS_huge_accident_measure
		where  ID_AHCS_HUGE_ACCIDENT_INFO=#{idAhcsHugeAccidentInfo} 
	</select>

	<select id="getMeasureMapAll" resultMap="MeasureVOMap">
		select value_code, VALUE_CHINESE_NAME
		from clm_common_parameter
		where COLLECTION_CODE = 'AHCS_MEASURE_CODE'

	</select>


</mapper>