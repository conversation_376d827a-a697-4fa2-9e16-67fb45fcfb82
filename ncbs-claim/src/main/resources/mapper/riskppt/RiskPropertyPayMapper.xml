<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.riskppt.RiskPropertyPayMapper">
    <resultMap id="riskPptPayMap" type="com.paic.ncbs.claim.model.dto.riskppt.RiskPropertyPayDTO">
        <result property="reportNo" column="REPORT_NO"/>
        <result property="policyNo" column="POLICY_NO"/>
        <result property="insuredCode" column="INSURED_CODE"/>
        <result property="idPlyRiskGroup" column="ID_PLY_RISK_GROUP"/>
        <result property="idPlyRiskProperty" column="ID_PLY_RISK_PROPERTY"/>
        <result property="planCode" column="PLAN_CODE"/>
        <result property="dutyCode" column="DUTY_CODE"/>
        <result property="dutyDetailCode" column="DUTY_DETAIL_CODE"/>
        <result property="dutyDetailPay" column="DUTY_DETAIL_PAY"/>
    </resultMap>

    <select id="getRiskPropertyPayList" parameterType="com.paic.ncbs.claim.model.dto.riskppt.RiskPropertyPayDTO" resultMap="riskPptPayMap">
        select  POLICY_NO,
                INSURED_CODE,
                ID_PLY_RISK_GROUP,
                ID_PLY_RISK_PROPERTY,
                PLAN_CODE,
                DUTY_CODE,
                DUTY_DETAIL_CODE,
                DUTY_DETAIL_PAY
        from CLMS_RISK_PROPERTY_PAYED
        where POLICY_NO = #{policyNo,jdbcType=VARCHAR}
        and INSURED_CODE = #{insuredCode,jdbcType=VARCHAR}
        and ID_PLY_RISK_GROUP = #{idPlyRiskGroup,jdbcType=VARCHAR}
        and IS_EFFECTIVE = 'Y'
    </select>

    <insert id="saveRiskPropertyPayList" parameterType="java.util.List" >
        insert into CLMS_RISK_PROPERTY_PAYED
        (
        ID_RISK_PROPERTY_PAYED,
        REPORT_NO,
        POLICY_NO,
        INSURED_CODE,
        ID_PLY_RISK_GROUP,
        ID_PLY_RISK_PROPERTY,
        PLAN_CODE,
        DUTY_CODE,
        DUTY_DETAIL_CODE,
        DUTY_DETAIL_PAY,
        CLAIM_TYPE,
        CREATED_BY,
        UPDATED_BY
        )
        values
        <foreach collection="riskPropertyPayList" separator="," index="index" item="item">
            (
            #{item.idRiskPropertyPay,jdbcType=VARCHAR},
            #{item.reportNo,jdbcType=VARCHAR},
            #{item.policyNo,jdbcType=VARCHAR},
            #{item.insuredCode,jdbcType=VARCHAR},
            #{item.idPlyRiskGroup,jdbcType=VARCHAR},
            #{item.idPlyRiskProperty,jdbcType=VARCHAR},
            #{item.planCode,jdbcType=VARCHAR},
            #{item.dutyCode,jdbcType=VARCHAR},
            #{item.dutyDetailCode,jdbcType=VARCHAR},
            #{item.dutyDetailPay,jdbcType=DECIMAL},
            #{item.claimType,jdbcType=VARCHAR},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.updatedBy,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <update id="removeRiskPropertyPay" parameterType="com.paic.ncbs.claim.model.dto.riskppt.RiskPropertyPayDTO">
        update CLMS_RISK_PROPERTY_PAYED
        set UPDATED_DATE = now(),
            UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            IS_EFFECTIVE = 'N'
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
          and IS_EFFECTIVE = 'Y'
    </update>

    <select id="getDutyDetailPayedList" resultMap="riskPptPayMap">
        select
               b.INSURED_CODE,
               a.ID_PLY_RISK_PROPERTY,
               a.POLICY_NO,
               (select c.ID_PLY_RISK_GROUP from clms_risk_property_case c
                   where c.report_no = #{reportNo,jdbcType=VARCHAR}
                     and c.is_effective = 'Y'
                     and c.task_id = 'checkDuty'
                     and c.id_ply_risk_property = a.id_ply_risk_property limit 1) ID_PLY_RISK_GROUP,
               a.PLAN_CODE,
               a.DUTY_CODE,
               a.DUTY_DETAIL_CODE,
               if(a.SETTLE_AMOUNT>0,a.SETTLE_AMOUNT,a.AUTO_SETTLE_AMOUNT) DUTY_DETAIL_PAY
        from clms_duty_detail_pay a ,clms_policy_claim_case b
        where a.CASE_NO = b.CASE_NO
          and b.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
          and a.CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
          and (a.SETTLE_AMOUNT>0 or a.AUTO_SETTLE_AMOUNT>0)
          and  a.IS_EFFECTIVE = 'Y'
    </select>

    <select id="getRiskPropertyPlanPayList" parameterType="com.paic.ncbs.claim.model.dto.riskppt.RiskPropertyPayDTO" resultMap="riskPptPayMap">
        select  ID_PLY_RISK_PROPERTY,
                PLAN_CODE,
                DUTY_CODE,
                DUTY_DETAIL_CODE,
                DUTY_DETAIL_PAY DUTY_DETAIL_PAY
        from CLMS_RISK_PROPERTY_PAYED a
        where POLICY_NO = #{policyNo,jdbcType=VARCHAR}
          and IS_EFFECTIVE = 'Y'
          and DUTY_DETAIL_PAY > 0
          and INSURED_CODE = (select c.INSURED_CODE from clms_policy_claim_case c
                             where c.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
                               and c.POLICY_NO = #{policyNo,jdbcType=VARCHAR} limit 1)
          <if test="idRiskPropertyList != null">
              and ID_PLY_RISK_PROPERTY in (
              <foreach collection="idRiskPropertyList" separator="," index="index" item="item">
                  #{item,jdbcType=VARCHAR}
              </foreach>
              )
          </if>

    </select>

    <select id="getRiskPropertyDutyPay" parameterType="com.paic.ncbs.claim.model.dto.riskppt.RiskPropertyPayDTO" resultMap="riskPptPayMap">
        select concat(PLAN_CODE,DUTY_CODE) DUTY_DETAIL_CODE,
        sum(DUTY_DETAIL_PAY) DUTY_DETAIL_PAY
        from CLMS_RISK_PROPERTY_PAYED a
        where ID_PLY_RISK_PROPERTY = #{idPlyRiskProperty,jdbcType=VARCHAR}
        and INSURED_CODE = #{insuredCode,jdbcType=VARCHAR}
        and IS_EFFECTIVE = 'Y'
        and DUTY_DETAIL_PAY > 0
        group by PLAN_CODE,DUTY_CODE
    </select>

</mapper>