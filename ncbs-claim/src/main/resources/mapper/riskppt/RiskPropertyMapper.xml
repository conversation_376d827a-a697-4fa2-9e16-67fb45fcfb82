<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.riskppt.RiskPropertyMapper">
    <resultMap id="riskPptMap" type="com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO">
        <result property="policyNo" column="POLICY_NO" />
        <result property="idPlyRiskGroup" column="ID_PLY_RISK_GROUP" />
        <result property="riskGroupType" column="RISK_GROUP_TYPE" />
        <result property="riskGroupNo" column="RISK_GROUP_NO" />
        <result property="riskGroupName" column="RISK_GROUP_NAME" />
        <result property="idPlyRiskProperty" column="ID_PLY_RISK_PROPERTY" />
        <result property="idCaseRiskProperty" column="ID_RISK_PROPERTY_CASE" />
        <result property="riskDetail" column="RISK_DETAIL" />
        <result property="riskType" column="RISK_TYPE" />
        <result property="taskId" column="TASK_ID" />
        <result property="certificateType" column="CERTIFICATE_TYPE" />
        <result property="certificateNo" column="CERTIFICATE_NO" />
        <result property="name" column="NAME" />
        <result property="age" column="AGE" />
        <result property="sex" column="SEX" />
        <result property="birthDay" column="BIRTH_DAY" />
    </resultMap>

    <select id="getPlyRiskPropertyList" parameterType="com.paic.ncbs.claim.model.dto.riskppt.PlyRiskGroupQueryDTO" resultMap="riskPptMap">
        select b.POLICY_NO,
               b.ID_PLY_RISK_GROUP,
               b.RISK_GROUP_TYPE,
               b.RISK_GROUP_NO,
               b.RISK_GROUP_NAME,
               d.ID_PLY_RISK_PROPERTY,
               c.RISK_DETAIL,
               d.NAME,
               d.CERTIFICATE_NO,
               d.CERTIFICATE_TYPE,
               d.AGE,
               d.SEX,
               (select d.TARGET_TYPE from marketproduct_info d,ply_base_info e
                where d.MARKETPRODUCT_CODE = e.PRODUCT_CODE
                  and d.VERSION = e.PRODUCT_VERSION
                  and e.POLICY_NO = b.POLICY_NO order by d.CREATED_DATE desc limit 1) RISK_TYPE
        from ply_risk_person a,ply_risk_group b ,ply_risk_property c,ply_risk_propsub_group d
        where a.ID_PLY_RISK_GROUP = b.ID_PLY_RISK_GROUP
          and b.ID_PLY_RISK_GROUP = c.ID_PLY_RISK_GROUP
          and c.ID_PLY_RISK_PROPERTY = d.ID_PLY_RISK_PROPERTY

          <if test="certificateNo != null and certificateNo != ''">
              and a.CERTIFICATE_NO = #{certificateNo,jdbcType=VARCHAR}
          </if>
          <if test="insuredName != null and insuredName != ''">
              and a.NAME = #{insuredName,jdbcType=VARCHAR}
          </if>
          <if test="certificateType != null and certificateType != ''">
              and a.CERTIFICATE_TYPE = #{certificateType,jdbcType=VARCHAR}
          </if>
          <if test="policyNoList != null">
              and b.POLICY_NO in (
              <foreach collection="policyNoList" separator="," index="index" item="item">
                  #{item,jdbcType=VARCHAR}
              </foreach>)
          </if>
          <if test="subName != null and subName != ''">
              and d.NAME = #{subName,jdbcType=VARCHAR}
          </if>
          <if test="subCertificateNo != null and subCertificateNo != ''">
              and d.CERTIFICATE_NO = #{subCertificateNo,jdbcType=VARCHAR}
          </if>
          <if test="subCertificateType != null and subCertificateType != ''">
              and d.CERTIFICATE_TYPE = #{subCertificateType,jdbcType=VARCHAR}
          </if>
    </select>

    <select id="getCaseRiskPropertyLastTaskId" parameterType="com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO" resultType="java.lang.String">
        select TASK_ID from CLMS_RISK_PROPERTY_CASE
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
          and CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
          and IS_EFFECTIVE = 'Y'
        order by CREATED_DATE desc limit 1
    </select>

    <select id="getCaseRiskPropertyList" parameterType="com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO" resultMap="riskPptMap">
        select POLICY_NO,
               ID_RISK_PROPERTY_CASE,
               ID_PLY_RISK_GROUP,
               RISK_GROUP_TYPE,
               RISK_GROUP_NO,
               RISK_GROUP_NAME,
               ID_PLY_RISK_PROPERTY,
               RISK_DETAIL,
               RISK_TYPE,
               TASK_ID,
               CERTIFICATE_TYPE,
               CERTIFICATE_NO,
               NAME,
               AGE,
               SEX,
               BIRTH_DAY
        from CLMS_RISK_PROPERTY_CASE
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
          and CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
          and IS_EFFECTIVE = 'Y'
          and TASK_ID = #{taskId,jdbcType=VARCHAR}
    </select>

    <select id="getRiskPropertyListByReportNo" parameterType="com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO" resultMap="riskPptMap">
        select distinct
            certificate_type,
            certificate_no,
            name,
            RISK_GROUP_TYPE
        from CLMS_RISK_PROPERTY_CASE
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        and CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
        and IS_EFFECTIVE = 'Y'
        and RISK_GROUP_TYPE = '996'
        limit 1
    </select>

    <insert id="saveCaseRiskPropertyList" parameterType="java.util.List" >
        insert into CLMS_RISK_PROPERTY_CASE
        (
        ID_RISK_PROPERTY_CASE,
        REPORT_NO,
        CASE_TIMES,
        POLICY_NO,
        ID_PLY_RISK_GROUP,
        RISK_GROUP_TYPE,
        RISK_GROUP_NO,
        RISK_GROUP_NAME,
        ID_PLY_RISK_PROPERTY,
        RISK_DETAIL,
        RISK_TYPE,
        TASK_ID,
        CERTIFICATE_TYPE,
        CERTIFICATE_NO,
        NAME,
        AGE,
        SEX,
        BIRTH_DAY,
        CREATED_BY,
        UPDATED_BY
        )
        values
        <foreach collection="caseRiskPropertyList" separator="," index="index" item="item">
            (
            <if test="item.idCaseRiskProperty != null">#{item.idCaseRiskProperty}</if>
            <if test="item.idCaseRiskProperty == null">REPLACE(UUID(),'-','')</if>,
            #{item.reportNo,jdbcType=VARCHAR},
            #{item.caseTimes,jdbcType=INTEGER},
            #{item.policyNo,jdbcType=VARCHAR},
            <if test="item.idPlyRiskGroup != null">#{item.idPlyRiskGroup}</if>
            <if test="item.idPlyRiskGroup == null">''</if>,
            #{item.riskGroupType,jdbcType=VARCHAR},
            #{item.riskGroupNo,jdbcType=DECIMAL},
            <if test="item.riskGroupName != null">#{item.riskGroupName}</if>
            <if test="item.riskGroupName == null">''</if>,
            <if test="item.idPlyRiskProperty != null">#{item.idPlyRiskProperty}</if>
            <if test="item.idPlyRiskProperty == null">''</if>,
            #{item.riskDetail,jdbcType=VARCHAR},
            #{item.riskType,jdbcType=VARCHAR},
            #{item.taskId,jdbcType=VARCHAR},
            #{item.certificateType,jdbcType=VARCHAR},
            #{item.certificateNo,jdbcType=VARCHAR},
            #{item.name,jdbcType=VARCHAR},
            #{item.age,jdbcType=VARCHAR},
            #{item.sex,jdbcType=VARCHAR},
            #{item.birthDay,jdbcType=TIMESTAMP},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.updatedBy,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <update id="removeCaseRiskProperty" parameterType="com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO">
        update CLMS_RISK_PROPERTY_CASE
        set UPDATED_DATE = now(),
            UPDATED_BY   = #{updatedBy,jdbcType=VARCHAR},
            IS_EFFECTIVE = 'N'
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
          and CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
        <if test="idPlyRiskProperty != null and idPlyRiskProperty != ''">
            and TASK_ID = #{taskId,jdbcType=VARCHAR}
        </if>
        and IS_EFFECTIVE = 'Y'
    </update>

    <select id="getReportRiskPropertyList" parameterType="com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO" resultMap="riskPptMap">
        select POLICY_NO,
               ID_PLY_RISK_GROUP,
               RISK_GROUP_NO,
               RISK_GROUP_NAME,
               ID_PLY_RISK_PROPERTY,
               RISK_DETAIL,
               RISK_TYPE,
               CERTIFICATE_TYPE,
               CERTIFICATE_NO,
               NAME,
               AGE,
               SEX,
               BIRTH_DAY
        from CLMS_RISK_PROPERTY_REPORT
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
          and IS_EFFECTIVE = 'Y'
        <if test="idPlyRiskGroup != null and idPlyRiskGroup !=''">
            and ID_PLY_RISK_GROUP = #{idPlyRiskGroup,jdbcType=VARCHAR}
        </if>
        <if test="idPlyRiskProperty != null and idPlyRiskProperty !=''">
            and ID_PLY_RISK_PROPERTY = #{idPlyRiskProperty,jdbcType=VARCHAR}
        </if>
        <if test="riskPropertyIdList != null ">
            and ID_PLY_RISK_PROPERTY in (
            <foreach collection="riskPropertyIdList" separator="," index="index" item="item">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
    </select>

    <insert id="saveReportRiskPropertyList" parameterType="java.util.List" >
        insert into CLMS_RISK_PROPERTY_REPORT
        (
        ID_RISK_PROPERTY_REPORT,
        REPORT_NO,
        POLICY_NO,
        ID_PLY_RISK_GROUP,
        RISK_GROUP_NO,
        RISK_GROUP_NAME,
        ID_PLY_RISK_PROPERTY,
        RISK_DETAIL,
        RISK_TYPE,
        CERTIFICATE_TYPE,
        CERTIFICATE_NO,
        NAME,
        AGE,
        SEX,
        BIRTH_DAY,
        CREATED_BY,
        UPDATED_BY
        )
        values
        <foreach collection="reportRiskPropertyList" separator="," index="index" item="item">
            (
            #{item.idReportRiskProperty,jdbcType=VARCHAR},
            #{item.reportNo,jdbcType=VARCHAR},
            #{item.policyNo,jdbcType=VARCHAR},
            #{item.idPlyRiskGroup,jdbcType=VARCHAR},
            #{item.riskGroupNo,jdbcType=DECIMAL},
            #{item.riskGroupName,jdbcType=VARCHAR},
            #{item.idPlyRiskProperty,jdbcType=VARCHAR},
            #{item.riskDetail,jdbcType=VARCHAR},
            #{item.riskType,jdbcType=VARCHAR},
            #{item.certificateType,jdbcType=VARCHAR},
            #{item.certificateNo,jdbcType=VARCHAR},
            #{item.name,jdbcType=VARCHAR},
            #{item.age,jdbcType=VARCHAR},
            #{item.sex,jdbcType=VARCHAR},
            #{item.birthDay,jdbcType=TIMESTAMP},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.updatedBy,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <update id="removeReportRiskProperty" parameterType="com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO">
        update CLMS_RISK_PROPERTY_REPORT
        set UPDATED_DATE = now(),
            UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            IS_EFFECTIVE = 'N'
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
          and IS_EFFECTIVE = 'Y'
    </update>

    <select id="getLastTaskIdCaseRiskPropertyList" parameterType="com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO" resultMap="riskPptMap">
        select POLICY_NO,
        ID_RISK_PROPERTY_CASE,
        ID_PLY_RISK_GROUP,
        RISK_GROUP_TYPE,
        RISK_GROUP_NO,
        RISK_GROUP_NAME,
        ID_PLY_RISK_PROPERTY,
        RISK_DETAIL,
        RISK_TYPE,
        TASK_ID,
        CERTIFICATE_TYPE,
        CERTIFICATE_NO,
        NAME,
        AGE,
        SEX,
        BIRTH_DAY
        from CLMS_RISK_PROPERTY_CASE
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        and CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
        and IS_EFFECTIVE = 'Y'
        and TASK_ID = (select b.TASK_ID from CLMS_RISK_PROPERTY_CASE b
                                where b.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
                                 and b.CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
                                 and b.IS_EFFECTIVE = 'Y'
                                order by b.CREATED_DATE desc limit 1)

    </select>
    
    <delete id="deleteCaseRiskProperty">
        delete
        from clms_risk_property_case
        where report_no = #{reportNo}
        and case_times = #{caseTimes}
        and task_id = #{taskId}
    </delete>

    <delete id="deleteCaseRiskPropertyById">
        delete
        from clms_risk_property_case
        where id_risk_property_case = #{idCaseRiskProperty}
    </delete>

    <select id="getPlyRiskGroupType" resultType="java.lang.String">
        select b.RISK_GROUP_TYPE
        from ply_risk_group b
        where b.POLICY_NO = #{policyNo}
          and b.RISK_GROUP_NO = #{riskGroupNo}
    </select>

    <update id="updateReportRiskPropertyTaskId" parameterType="com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO">
        update clms_risk_property_case
        set UPDATED_DATE = now(),
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        task_id = #{taskId,jdbcType=VARCHAR}
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        and IS_EFFECTIVE = 'Y'
    </update>

</mapper>