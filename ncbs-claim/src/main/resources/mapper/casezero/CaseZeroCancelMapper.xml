<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.casezero.CaseZeroCancelMapper">

    <resultMap type="com.paic.ncbs.claim.model.vo.casezero.ZeroCancelAuditInfoVO" id="aduitInfo">
        <result property="applyUM" column="APPLY_UM"/>
        <result property="applyReasonCode" column="REASON_CODE"/>
        <result property="applyReasonDetails" column="APPLY_REASON_DETAILS"/>
        <result property="applyDate" column="APPLY_DATE"/>
        <result property="applyTimes" column="APPLY_TIMES"/>
        <result property="verifyUM" column="VERIFY_UM"/>
        <result property="verifyOptionsCode" column="VERIFY_OPTIONS"/>
        <result property="verifyRemark" column="VERIFY_REMARK"/>
        <result property="verifyDate" column="VERIFY_DATE"/>
        <result property="reduceAmount" column="REGISTER_AMOUNT"/>
        <result property="taskDefinitionKey" column="ID_AHCS_EOA_INFO"/>
        <result property="applyType" column="APPLY_TYPE"/>
        <result property="reportNo" column="report_no"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO" id="tempApplyInfo">
        <result property="idAhcsZeroCancelApply" column="ID_AHCS_ZERO_CANCEL_APPLY"/>
        <result property="reportNo" column="REPORT_NO"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="applyUm" column="APPLY_UM"/>
        <result property="applyDate" column="APPLY_DATE"/>
        <result property="applyTimes" column="APPLY_TIMES"/>
        <result property="applyType" column="APPLY_TYPE"/>
        <result property="reasonCode" column="REASON_CODE"/>
        <result property="applyReasonDetails" column="APPLY_REASON_DETAILS"/>
        <result property="verifyDate" column="VERIFY_DATE"/>
        <result property="status" column="STATUS"/>
        <result property="verifyUm" column="VERIFY_UM"/>
        <result property="reduceAmount" column="REGISTER_AMOUNT"/>
        <result property="taskDefinitionKey" column="ID_AHCS_EOA_INFO"/>

    </resultMap>

    <!--保存零注申请信息 -->
    <insert id="addCaseZeroCancelApply">
        insert into CLMS_zero_cancel_apply
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_ZERO_CANCEL_APPLY,
        REPORT_NO,
        CASE_TIMES,
        APPLY_TIMES,
        APPLY_TYPE,
        REASON_CODE,
        APPLY_REASON_DETAILS,
        APPLY_UM,
        APPLY_DATE,
        APPLY_FROM,
        REGISTER_AMOUNT,
        status,
        ARCHIVE_TIME,
         ID_AHCS_EOA_INFO,
         CANCEL_TYPE
        )
        values
        (#{createdBy,jdbcType=VARCHAR},
        sysdate(),
        #{updatedBy,jdbcType=VARCHAR},
        sysdate(),
        #{idAhcsZeroCancelApply},
        #{reportNo,jdbcType=VARCHAR},
        #{caseTimes,jdbcType=INTEGER},
        #{applyTimes,jdbcType=INTEGER},
        #{applyType,jdbcType=VARCHAR},
        #{reasonCode,jdbcType=VARCHAR},
        #{applyReasonDetails,jdbcType=VARCHAR},
        #{applyUm,jdbcType=VARCHAR},
        sysdate(),
        '',
        #{reduceAmount,jdbcType=DECIMAL},
        #{status,jdbcType=VARCHAR},
        sysdate(),
        #{taskDefinitionKey,jdbcType=VARCHAR},
            #{cancelType,jdbcType=VARCHAR}
        )
    </insert>

    <!--查询零注申请信息记录 -->
    <select id="getCaseZeroCancelApplyCount" resultType="int">
        select count(1) from CLMS_zero_cancel_apply t
        where t.report_no = #{reportNo}
        and t.case_times = #{caseTimes}
        and t.status in ('1','3')
    </select>

    	<!-- 根据报案号跟赔付次数或状态查询案件零结注销数据 -->
    	<select id="getCaseZeroCancelApplyList" resultType="com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO">
    		SELECT  b.CREATED_BY createdBy,
    				b.CREATED_DATE createdDate,
    				b.UPDATED_BY updatedBy,
    				b.UPDATED_DATE updatedDate,
    				b.id_ahcs_zero_cancel_apply idAhcsZeroCancelApply,
    				b.report_no reportNo,
    				b.case_times caseTimes,
    				b.apply_times applyTimes,
    				b.apply_type applyType,
    				b.REASON_CODE reasonCode,
    				b.apply_reason_details applyReasonDetails,
    				b.apply_um applyUm,
    				b.apply_date applyDate,
    				b.verify_um verifyUm,
    				b.verify_date verifyDate,
    				b.verify_options verifyOptions,
    				b.verify_remark verifyRemark,
    				b.status status,
    				b.REGISTER_AMOUNT  reduceAmount,
    		        b.ID_AHCS_EOA_INFO taskDefinitionKey,
                    b.CANCEL_TYPE cancelType

    		FROM CLMS_zero_cancel_apply b
    		WHERE b.report_no = #{reportNo}
    		and b.case_times = #{caseTimes}
    		<if test="status != null and status != '' ">
    			and b.status = #{status}
    		</if>
    		order by b.updated_date
    	</select>

    	<!-- 供零结注销审批发送和暂存用，更新零结注销信息 -->
    	<update id="saveCaseZeroCancelAudit">
    		<![CDATA[
            update CLMS_zero_cancel_apply t
               set t.updated_by     = #{updatedBy},
                   t.updated_date   = sysdate(),
                   t.apply_type     = #{applyType, jdbcType=VARCHAR},
                   t.verify_um      = #{verifyUm},
                   t.verify_date    = sysdate(),
                   t.verify_options = #{verifyOptions, jdbcType=VARCHAR},
                   t.verify_remark  = #{verifyRemark, jdbcType=VARCHAR},
                   t.status = #{status}
             where t.report_no = #{reportNo, jdbcType=VARCHAR}
               and t.case_times = #{caseTimes, jdbcType=INTEGER}
               and (t.status = '1' or t.status= '3')
            ]]>
    	</update>

    <!-- 获取零注审批信息 -->
    <select id="getAuditInfoList" resultMap="aduitInfo">
        select t.apply_um,
        t.apply_date,
        t.reason_code,
        t.apply_reason_details,
        t.created_date,
        t.apply_times,
        t.verify_um,
        t.verify_reason_code,
        t.status,
        t.verify_options,
        t.verify_remark,
        t.verify_date,
        t.ID_AHCS_EOA_INFO taskDefinitionKey,
        t.APPLY_TYPE,
        CONCAT(t.REPORT_NO,'_',t.case_times) report_no
        from CLMS_zero_cancel_apply t
        where t.report_no = #{reportNo}
        order by t.apply_date
    </select>

    <select id="getIsContinueByReportNo" resultType="Integer">
        select count(1)
        from CLMS_zero_cancel_apply t
        where t.report_no = #{reportNo}
        and t.case_times = #{caseTimes}
        and (eoa_status='0' or status in ('0','1','3'))
    </select>

    <!--	&lt;!&ndash; 获取该报案该次赔付中当前最大的申请次数 &ndash;&gt;-->
    <select id="getMaxApplyTimes" resultType="java.lang.Integer">
        select ifnull(max(a.apply_times), 0) from CLMS_zero_cancel_apply a
        where a.report_no=#{reportNo}
        and a.case_times=#{caseTimes}
        and a.status = '2'
    </select>

    <!-- 获取零注申请暂存的信息 -->
    <select id="getTempApplyInfo" resultMap="tempApplyInfo">
        select t.ID_AHCS_ZERO_CANCEL_APPLY,
        t.APPLY_UM,
        t.APPLY_DATE,
        t.APPLY_TIMES,
        t.APPLY_TYPE,
        t.REASON_CODE,
        t.APPLY_REASON_DETAILS,
        t.ID_AHCS_EOA_INFO taskDefinitionKey
        from CLMS_zero_cancel_apply t
        where t.REPORT_NO = #{reportNo, jdbcType=VARCHAR}
        and t.CASE_TIMES = #{caseTimes, jdbcType=INTEGER}
        and t.APPLY_TIMES = #{applyTimes, jdbcType=INTEGER}
        and t.STATUS != '2'
    </select>


    <select id="getZeroCancelInfoList" resultType="com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO">
        SELECT b.CREATED_BY createdBy,
        b.CREATED_DATE createdDate,
        b.UPDATED_BY updatedBy,
        b.UPDATED_DATE updatedDate,
        b.id_ahcs_zero_cancel_apply idAhcsZeroCancelApply,
        b.report_no reportNo,
        b.case_times caseTimes,
        b.apply_times applyTimes,
        b.apply_type applyType,
        b.REASON_CODE reasonCode,
        b.apply_reason_details applyReasonDetails,
        b.apply_um applyUm,
        b.apply_date applyDate,
        b.remit_amount_info remitAmountInfo,
        b.verify_um verifyUm,
        b.verify_date verifyDate,
        b.verify_options verifyOptions,
        b.verify_remark verifyRemark,
        b.status status,
        b.REGISTER_AMOUNT reduceAmount,
        b.ID_AHCS_EOA_INFO taskDefinitionKey,
        b.CANCEL_TYPE cancelType
        FROM CLMS_zero_cancel_apply b
        WHERE b.report_no = #{reportNo}
        and b.case_times = #{caseTimes}
        order by b.apply_times desc
    </select>

    <select id="getIdZeroCancelApplyByReportNo" resultType="string">
        select t.id_ahcs_zero_cancel_apply
        from CLMS_zero_cancel_apply t
        where t.report_no = #{reportNo}
        and t.case_times = #{caseTimes}
        <if test="status != null and status != '' ">
            and t.status = #{status}
        </if>
    </select>

    <!-- 更新暂存的零结注销申请信息(暂存和申请发送使用)-->
    <update id="modifyCaseZeroCancelApply">
        <![CDATA[
        update CLMS_ZERO_CANCEL_APPLY t
           set t.UPDATED_BY   = #{updatedBy},
               t.UPDATED_DATE = sysdate(),
               t.APPLY_TYPE   = #{applyType},
               t.APPLY_DATE   = sysdate(),
               t.REASON_CODE  = #{reasonCode},
               t.APPLY_REASON_DETAILS = #{applyReasonDetails},
               t.APPLY_UM     = #{applyUm},
               t.STATUS       = #{status},
               t.REGISTER_AMOUNT = #{reduceAmount,jdbcType=DECIMAL}
         where t.ID_AHCS_ZERO_CANCEL_APPLY = #{idAhcsZeroCancelApply}
        ]]>
    </update>

    <select id="getCaseZeroCancelApplyAuditList" resultType="com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO">
        SELECT  b.CREATED_BY createdBy,
        b.CREATED_DATE createdDate,
        b.UPDATED_BY updatedBy,
        b.UPDATED_DATE updatedDate,
        b.id_ahcs_zero_cancel_apply idAhcsZeroCancelApply,
        b.report_no reportNo,
        b.case_times caseTimes,
        b.apply_times applyTimes,
        b.apply_type applyType,
        b.REASON_CODE reasonCode,
        b.apply_reason_details applyReasonDetails,
        b.apply_um applyUm,
        b.apply_date applyDate,
        b.REMIT_AMOUNT_INFO remitAmountInfo,
        b.verify_um verifyUm,
        b.verify_date verifyDate,
        b.verify_options verifyOptions,
        b.verify_remark verifyRemark,
        b.status status,
        b.REGISTER_AMOUNT  reduceAmount,
        b.ID_AHCS_EOA_INFO taskDefinitionKey,
        b.CANCEL_TYPE cancelType
        FROM
        CLMS_zero_cancel_apply b
        WHERE
        b.report_no = #{reportNo}
        and b.case_times = #{caseTimes}
        and b.status in('1','3')
        order by b.updated_date desc
    </select>

    <update id="modifyZeroApplyType" parameterType="com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO">
        update CLMS_zero_cancel_apply t
        set t.updated_by     = #{updatedBy},
        t.updated_date   = now(),
        t.apply_type     = #{applyType, jdbcType=VARCHAR},
        t.verify_remark  = #{verifyRemark, jdbcType=VARCHAR}
        where t.id_ahcs_zero_cancel_apply = #{idAhcsZeroCancelApply, jdbcType=VARCHAR}
    </update>

    <select id="getZeroCancelApplyCount" resultType="java.lang.Integer">
        select count(1)
        from clms_zero_cancel_apply
        where REPORT_NO = #{reportNo, jdbcType=VARCHAR}
        and CASE_TIMES = #{caseTimes, jdbcType=INTEGER}
        and (STATUS = '1' or DEPT_VERIFY_OPTIONS='1' or VERIFY_OPTIONS='1') limit 1
    </select>

    <select id="getZeroCancelApplyTime" resultType="com.paic.ncbs.claim.model.dto.endcase.CaseZeroCancelDTO">
        select APPLY_DATE applyDate,ID_AHCS_EOA_INFO taskDefinitionKey from CLMS_zero_cancel_apply where   report_no=#{reportNo}
        and APPLY_TYPE='1'
        ORDER BY APPLY_DATE desc  limit 1
    </select>

</mapper>