<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.paic.ncbs.claim.dao.mapper.casezero.ZeroRemitAmountMapper">
    <resultMap type="com.paic.ncbs.claim.model.vo.endcase.ZeroRemitAmountDTO" id="zeroRemitAmountResult">
        <result column="CREATED_BY" property="createdBy"/>
        <result column="CREATED_DATE" property="createdDate"/>
        <result column="UPDATED_BY" property="updatedBy"/>
        <result column="UPDATED_DATE" property="updatedDate"/>
        <result column="ID_AHCS_ZERO_REMIT_AMOUNT" property="idZeroRemitAmount"/>
        <result column="ID_AHCS_ZERO_CANCEL_APPLY" property="idAhcsZeroCancelApply"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="CASE_NO" property="caseNo"/>
        <result column="POLICY_NO" property="policyNo"/>
        <result column="POLICY_CER_NO" property="policyCerNo"/>
        <result column="PLAN_CODE" property="planCode"/>
        <result column="PLAN_NAME" property="planName"/>
        <result column="DUTY_CODE" property="dutyCode"/>
        <result column="DUTY_NAME" property="dutyName"/>
        <result column="REMIT_AMOUNT" property="remitAmount"/>
        <result column="REMIT_DAYS" property="remitDays"/>
        <result column="REMIT_TIMES" property="remitTimes"/>
        <result column="IS_EFFECTIVE" property="isDffective"/>
        <result column="TASK_CODE" property="taskCode"/>
        <result column="ID_AHCS_POLICY_INFO" property="idAhcsPolicyInfo"/>
        <result column="ID_AHCS_POLICY_PLAN" property="idAhcsPolicyPlan"/>
        <result column="PLAN_NAME" property="planName"/>
        <result column="DUTY_NAME" property="dutyName"/>
    </resultMap>

    <!-- 新增单个免赔额信息 -->
    <insert id="addZeroRemitAmount">
        insert into CLMS_ZERO_REMIT_AMOUNT
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_ZERO_CANCEL_APPLY,
        REPORT_NO,
        CASE_TIMES,
        TASK_CODE,
        CASE_NO,
        POLICY_NO,
        POLICY_CER_NO,
        PLAN_CODE,
        PLAN_NAME,
        DUTY_CODE,
        DUTY_NAME,
        REMIT_AMOUNT,
        REMIT_DAYS,
        REMIT_TIMES,
        IS_EFFECTIVE,
        ARCHIVE_TIME)
        values
        (#{userId},
        sysdate(),
        #{userId},
        sysdate(),
        #{idAhcsZeroCancelApply,jdbcType=VARCHAR},
        #{reportNo,jdbcType=VARCHAR},
        #{caseTimes,jdbcType=INTEGER},
        #{taskCode,jdbcType=VARCHAR},
        #{caseNo,jdbcType=VARCHAR},
        #{policyNo,jdbcType=VARCHAR},
        #{policyCerNo,jdbcType=VARCHAR},
        #{planCode,jdbcType=VARCHAR},
        #{planName,jdbcType=VARCHAR},
        #{dutyCode,jdbcType=VARCHAR},
        #{dutyName,jdbcType=VARCHAR},
        #{remitAmount,jdbcType=NUMERIC},
        #{remitDays,jdbcType=NUMERIC},
        #{remitTimes,jdbcType=INTEGER},
        #{isDffective,jdbcType=VARCHAR},
        <if test="archiveTime != null ">
            #{archiveTime,jdbcType=TIMESTAMP}
        </if>
        <if test="archiveTime == null ">
            ,sysdate()
        </if>)
    </insert>

    <!-- 根据零注id查询免赔额信息 -->
    <select id="getZeroRemitAmountByIdZeroCancel" parameterType="string" resultMap="zeroRemitAmountResult">
        select zra.CASE_NO,
        zra.POLICY_NO,
        zra.POLICY_CER_NO,
        zra.PLAN_CODE,
        zra.PLAN_NAME,
        zra.DUTY_CODE,
        zra.DUTY_NAME,
        zra.REMIT_AMOUNT,
        zra.REMIT_DAYS
        from CLMS_ZERO_REMIT_AMOUNT zra
        where zra.ID_AHCS_ZERO_CANCEL_APPLY = #{idAhcsZeroCancelApply}
        and zra.IS_EFFECTIVE = 'Y'
    </select>

    <!-- 根据零注id 失效免赔额信息 -->
    <update id="removeRemitAmountByIdZeroCancel" parameterType="string">
        update CLMS_ZERO_REMIT_AMOUNT zra
        set zra.UPDATED_DATE = now(),
        zra.IS_EFFECTIVE = 'N'
        where zra.ID_AHCS_ZERO_CANCEL_APPLY = #{idAhcsZeroCancelApply}
    </update>


    <!-- 根据保单号获取赔案号 -->
    <select id="getCaseNoByPolicyNo" parameterType="string" resultType="string">
        select pi.CASE_NO
        from CLMS_POLICY_INFO pi
        where pi.REPORT_NO = #{reportNo}
        and pi.POLICY_NO = #{policyNo}
    </select>

</mapper>