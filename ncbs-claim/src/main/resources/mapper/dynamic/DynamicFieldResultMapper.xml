<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.dynamic.DynamicFieldResultMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.dynamic.DynamicFieldsResultEntity">
        <id column="id" property="id" jdbcType="VARCHAR" />
        <result column="del_flag" property="delFlag" jdbcType="VARCHAR" />
        <result column="report_no" property="reportNo" jdbcType="VARCHAR" />
        <result column="case_times" property="caseTimes" jdbcType="INTEGER" />
        <result column="template_id" property="templateId" jdbcType="VARCHAR" />
        <result column="field_code" property="fieldCode" jdbcType="VARCHAR" />
        <result column="field_value" property="fieldValue" jdbcType="VARCHAR" />
        <result column="value_mapper" property="valueMapper" jdbcType="VARCHAR" />
        <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
        <result column="sys_ctime" property="sysCtime" jdbcType="TIMESTAMP" />
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR" />
        <result column="sys_utime" property="sysUtime" jdbcType="TIMESTAMP" />
    </resultMap>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        INSERT INTO clm_dynamic_field_result (
        created_by,
        updated_by,
        sys_ctime,
        sys_utime,
        del_flag,
        template_id,
        report_no,
        case_times,
        field_code,
        field_value,
        value_mapper
        )
        SELECT
        #{userId},
        #{userId},
        NOW(),
        NOW(),
        del_flag,
        template_id,
        report_no,
        #{reopenCaseTimes},
        field_code,
        field_value,
        value_mapper
        FROM clm_dynamic_field_result
        WHERE report_no=#{reportNo}
        and case_times= #{caseTimes}
    </insert>


</mapper>