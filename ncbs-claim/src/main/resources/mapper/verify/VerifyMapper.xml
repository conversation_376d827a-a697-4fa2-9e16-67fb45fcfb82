<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.verify.VerifyMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.verify.VerifyDTO" id="verify">
        <id property="idAhcsVerify" column="id_ahcs_verify"/>
        <result property="reportNo" column="report_no"/>
        <result property="caseTimes" column="case_times"/>
        <result property="idAhcsBatch" column="id_ahcs_batch"/>
        <result property="verifyUn" column="verify_un"/>
        <result property="verifyDate" column="verify_date"/>
        <result property="verifyOpinion" column="verify_opinion"/>
        <result property="verifyConclusion" column="verify_conclusion"/>
        <result property="settleUM" column="SETTLE_UM"/>
        <result property="settleDate" column="SETTLE_DATE"/>
    </resultMap>

    <insert id="insertVerify" parameterType="com.paic.ncbs.claim.model.dto.verify.VerifyDTO">
        INSERT INTO CLMS_VERIFY (
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_VERIFY,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_BATCH,
        SETTLE_UM,
        SETTLE_DATE,
        ARCHIVE_TIME )
        VALUES (
        #{createdBy ,jdbcType=VARCHAR},
        NOW(),
        #{updatedBy ,jdbcType=VARCHAR},
        NOW(),
        #{idAhcsVerify ,jdbcType=VARCHAR},
        #{reportNo ,jdbcType=VARCHAR},
        #{caseTimes ,jdbcType=NUMERIC},
        #{idAhcsBatch ,jdbcType=VARCHAR},
        #{settleUM ,jdbcType=VARCHAR},
        NOW(),
        NOW() )
    </insert>

    <select id="getVerify" resultMap="verify">
        select ID_AHCS_VERIFY,
            REPORT_NO,
            CASE_TIMES,
            ID_AHCS_BATCH,
            VERIFY_UN,
            VERIFY_DATE,
            VERIFY_OPINION,
            VERIFY_CONCLUSION,
            SETTLE_UM,
            SETTLE_DATE
            from clms_verify
        where REPORT_NO = #{reportNo ,jdbcType=VARCHAR}
        and CASE_TIMES = #{caseTimes ,jdbcType=NUMERIC}
        and IS_EFFECTIVE = 'Y'
    </select>

    <select id="getVerifyList" resultMap="verify">
        select ID_AHCS_VERIFY,
            REPORT_NO,
            CASE_TIMES,
            ID_AHCS_BATCH,
            VERIFY_UN,
            VERIFY_DATE,
            VERIFY_OPINION,
            VERIFY_CONCLUSION,
            SETTLE_UM,
            CREATED_DATE AS SETTLE_DATE
        from clms_verify
        where REPORT_NO = #{reportNo ,jdbcType=VARCHAR}
        and CASE_TIMES = #{caseTimes ,jdbcType=NUMERIC}
        order by CREATED_DATE desc
    </select>

    <update id="updateVerify" parameterType="com.paic.ncbs.claim.model.dto.verify.VerifyDTO">
        update clms_verify
        set UPDATED_DATE = now(),
        <if test="isEffective != null">
            IS_EFFECTIVE = #{isEffective ,jdbcType=VARCHAR},
        </if>
        UPDATED_BY = #{updatedBy ,jdbcType=VARCHAR},
        VERIFY_UN = #{verifyUn ,jdbcType=VARCHAR},
        VERIFY_DATE = now(),
        VERIFY_OPINION = #{verifyOpinion ,jdbcType=VARCHAR},
        VERIFY_CONCLUSION = #{verifyConclusion ,jdbcType=VARCHAR}
        where ID_AHCS_VERIFY = #{idAhcsVerify ,jdbcType=VARCHAR}
    </update>

    <update id="deleteVerify" parameterType="com.paic.ncbs.claim.model.dto.verify.VerifyDTO">
        update clms_verify
        set UPDATED_DATE = now(),
        UPDATED_BY = #{updatedBy ,jdbcType=VARCHAR},
        IS_EFFECTIVE = 'N'
        where report_no = #{reportNo ,jdbcType=VARCHAR}
        and case_times = #{caseTimes}
    </update>

</mapper>