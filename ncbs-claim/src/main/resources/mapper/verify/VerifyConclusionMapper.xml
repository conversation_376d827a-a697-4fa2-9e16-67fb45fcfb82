<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.verify.VerifyConclusionMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.verify.VerifyConclusionDTO" id="result2">
        <id column="ID_AHCS_VERIFY_CONCLUSION" property="ahcsVerifyConclusionId"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="AUDITING_UM" property="auditingUm"/>
        <result column="AUDITING_DATE" property="auditingDate"/>
        <result column="AUDITING_COMMONT" property="auditingCommont"/>
        <result column="CONLUSION_CAUSE_CODE" property="conclusionCauseCode"/>
        <result column="CONLUSION_CAUSE_DESC" property="conclusionCauseDesc"/>
        <result column="REJECT_AMOUNT" property="rejectAmount"/>
        <result column="REJECT_NOTIFY_DATE" property="rejectNotifyDate"/>
        <result column="STATUS" property="status"/>
        <result column="department_code" property="departmentCode"/>
        <result column="TASK_CODE" property="taskCode"/>
        <result column="LOSS_OBJECT_NO" property="lossObjectNo"/>
        <result column="ARCHIVE_TIME" property="archiveTime"/>
    </resultMap>

    <!-- 保存核责表信息 -->
    <insert id="saveDuty" parameterType="com.paic.ncbs.claim.model.dto.verify.VerifyConclusionDTO">
        insert into CLMS_VERIFY_CONCLUSION
        (
        ID_AHCS_VERIFY_CONCLUSION,
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        AUDITING_UM,
        AUDITING_DATE,
        AUDITING_COMMONT,
        CONLUSION_CAUSE_CODE,
        CONLUSION_CAUSE_DESC,
        REJECT_AMOUNT,
        REJECT_NOTIFY_DATE,
        status,
        department_code,
        TASK_CODE,
        LOSS_OBJECT_NO,
        ARCHIVE_TIME
        )
        values
        (
        #{ahcsVerifyConclusionId},
        #{createdBy},
        NOW(),
        #{updatedBy},
        NOW(),
        #{reportNo},
        #{caseTimes},
        #{auditingUm},
        NOW(),
        #{auditingCommont,jdbcType=VARCHAR},
        #{conclusionCauseCode,jdbcType=VARCHAR},
        #{conclusionCauseDesc,jdbcType=VARCHAR},
        #{rejectAmount,jdbcType=NUMERIC},
        #{rejectNotifyDate,jdbcType=DATE},
        #{status,jdbcType=VARCHAR},
        #{departmentCode,jdbcType=VARCHAR},
        #{taskCode,jdbcType=VARCHAR},
        #{lossObjectNo,jdbcType=VARCHAR},
        <if test="archiveTime != null ">
            #{archiveTime,jdbcType=TIMESTAMP}
        </if>
        <if test="archiveTime == null ">
            NOW()
        </if>
        )
    </insert>

    <!-- 根据报案号和赔付次数获取核责表信息 -->
    <select id="getDuty" resultMap="result2">
        select t.ID_AHCS_VERIFY_CONCLUSION,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.AUDITING_UM,
        t.AUDITING_DATE,
        t.AUDITING_COMMONT,
        t.CONLUSION_CAUSE_CODE,
        t.CONLUSION_CAUSE_DESC,
        t.REJECT_AMOUNT,
        t.REJECT_NOTIFY_DATE,
        TASK_CODE,
        t.STATUS,
        t.ARCHIVE_TIME
        from CLMS_VERIFY_CONCLUSION t
        where t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        AND t.IS_EFFECTIVE = 'Y'
        <if test="status != null and status != '' ">
            and t.STATUS = #{status}
        </if>
        <if test="taskCode != null and taskCode != '' ">
            and t.task_code = #{taskCode}
        </if>
        and t.task_code =
        (select * from
        (select t1.task_code from CLMS_VERIFY_CONCLUSION t1 where
        t1.REPORT_NO = #{reportNo}
        and t1.CASE_TIMES = #{caseTimes}
        AND t1.IS_EFFECTIVE = 'Y'
        <if test="status != null and status != '' ">
            and t1.STATUS = #{status}
        </if>
        <if test="taskCode != null and taskCode != '' ">
            and t1.task_code = #{taskCode}
        </if>
        order by t1.created_date desc)
        as temp limit 1
        )
    </select>

    <!-- 根据报案号查询核责最大的赔付次数 -->
    <select id="getDutyCaseTimesByReportNo" parameterType="string" resultType="int">
        select IFNULL(max(case_Times), 0)
        from CLMS_VERIFY_CONCLUSION
        where report_no = #{reportNo}
        and status = '1'
        AND IS_EFFECTIVE = 'Y'
    </select>

    <delete id="removeDuty">
        delete from CLMS_VERIFY_CONCLUSION where REPORT_NO = #{reportNo} and CASE_TIMES = #{caseTimes} and task_code =
        #{taskCode}
    </delete>

    <update id="updateEffective" parameterType="com.paic.ncbs.claim.model.dto.verify.VerifyConclusionDTO">
        UPDATE
        CLMS_VERIFY_CONCLUSION
        SET
        UPDATED_BY = #{updatedBy},
        UPDATED_DATE = NOW(),
        IS_EFFECTIVE = 'N'
        WHERE
        REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        AND TASK_CODE = #{taskCode}
        <if test="lossObjectNo != null and lossObjectNo != '' ">
            and LOSS_OBJECT_NO = #{lossObjectNo}
        </if>
        AND IS_EFFECTIVE = 'Y'
    </update>

    <!-- 新增核责表信息 -->
    <insert id="addDuty" parameterType="com.paic.ncbs.claim.model.dto.verify.VerifyConclusionDTO">
        insert into CLMS_VERIFY_CONCLUSION
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        AUDITING_UM,
        AUDITING_DATE,
        AUDITING_COMMONT,
        CONLUSION_CAUSE_CODE,
        CONLUSION_CAUSE_DESC,
        REJECT_AMOUNT,
        REJECT_NOTIFY_DATE,
        STATUS,
        department_code,
        TASK_CODE,
        ARCHIVE_TIME
        )
        values
        (#{createdBy},
        NOW(),
        #{updatedBy},
        NOW(),
        #{reportNo},
        #{caseTimes},
        #{auditingUm},
        #{auditingDate},
        #{auditingCommont,jdbcType=VARCHAR},
        #{conclusionCauseCode,jdbcType=VARCHAR},
        #{conclusionCauseDesc,jdbcType=VARCHAR},
        #{rejectAmount,jdbcType=NUMERIC},
        #{rejectNotifyDate,jdbcType=DATE},
        #{status,jdbcType=VARCHAR},
        #{departmentCode,jdbcType=VARCHAR},
        #{taskCode,jdbcType=VARCHAR},
        <if test="archiveTime != null ">
            #{archiveTime,jdbcType=TIMESTAMP}
        </if>
        <if test="archiveTime == null ">
            NOW()
        </if>
        )
    </insert>

    <!-- 根据报案号和赔付次数获取核责人 -->
    <select id="getVerifyCnclusionUm" resultType="string">
        select vc.AUDITING_UM
        from CLMS_VERIFY_CONCLUSION vc
        where vc.REPORT_NO = #{reportNo}
        and vc.CASE_TIMES = #{caseTimes}
        and vc.task_code = #{taskCode}
        AND vc.IS_EFFECTIVE = 'Y'
        limit 1
    </select>

    <select id="getConclusionCauseDesc" resultType="string">
        select vc.CONLUSION_CAUSE_DESC
        from CLMS_VERIFY_CONCLUSION vc
        where vc.REPORT_NO = #{reportNo}
        and vc.CASE_TIMES = #{caseTimes}
        AND vc.IS_EFFECTIVE = 'Y'
        limit 1
    </select>

    <!-- 保存核责退客户推送信息 -->
    <insert id="saveAdditionDocSendInfo" parameterType="com.paic.ncbs.claim.model.dto.verify.AdditionDocSendDTO">
        INSERT INTO CLMS_ADDITION_DOC_SEND
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_ADDITION_DOC_SEND,
        REPORT_NO,
        CASE_TIMES,
        SMALL_TYPE_CODES,
        SEND_PHONE,
        SEND_DESC,
        SEND_TIME,
        APPLY_BY,
        RELATION_ID,
        ARCHIVE_TIME)
        VALUES(
        #{createdBy,jdbcType=VARCHAR},
        NOW(),
        #{updatedBy,jdbcType=VARCHAR},
        NOW(),
        #{idAhcsAdditionDocSend,jdbcType=VARCHAR},
        #{reportNo,jdbcType=VARCHAR},
        #{caseTimes,jdbcType=NUMERIC},
        #{smallCodes,jdbcType=VARCHAR},
        #{sendPhone,jdbcType=VARCHAR},
        #{sendDesc,jdbcType=VARCHAR},
        NOW(),
        #{applyBy,jdbcType=VARCHAR},
        #{relationId,jdbcType=VARCHAR},
        NOW()
        )
    </insert>

    <!--获取核责退客户推送信息-->
    <select id="getAdditionDocSendInfo" resultType="com.paic.ncbs.claim.model.dto.verify.AdditionDocSendDTO">
        SELECT T.REPORT_NO reportNo,
        T.CASE_TIMES caseTimes,
        T.SMALL_TYPE_CODES smallCodes,
        T.SEND_PHONE sendPhone,
        T.SEND_DESC sendDesc,
        T.SEND_TIME sendTime,
        T.UPLOAD_TIME uploadTime,
        T.APPLY_BY applyBy,
        T.RECALL_FLAG recallFlag
        FROM CLMS_ADDITION_DOC_SEND T
        WHERE T.REPORT_NO = #{reportNo}
        AND T.CASE_TIMES = #{caseTimes}
        <if test='uploadStatus == "1"'>
            AND T.UPLOAD_TIME IS NOT NULL
        </if>
        <if test='uploadStatus == "0"'>
            AND T.UPLOAD_TIME IS NULL
        </if>
        <if test=" recallFlag != null and recallFlag != '' ">
            AND T.RECALL_FLAG = #{recallFlag}
        </if>
        <if test=" relationId != null and relationId != '' ">
            AND T.RELATION_ID = #{relationId}
        </if>
        ORDER BY T.CREATED_DATE DESC,ID_AHCS_ADDITION_DOC_SEND
    </select>

    <!-- 核责退客户撤回时，更新记录 为已撤回-->
    <delete id="deleteAdditionDocSendInfo">
        UPDATE CLMS_ADDITION_DOC_SEND T
        SET T.RECALL_FLAG = '1'
        WHERE T.REPORT_NO = #{reportNo}
        AND T.CASE_TIMES = #{caseTimes}
        AND T.UPLOAD_TIME IS NULL
        AND T.RECALL_FLAG = '0'
    </delete>

    <!-- 更新材料补充时间 -->
    <update id="updateUploadTime" parameterType="com.paic.ncbs.claim.model.dto.verify.AdditionDocSendDTO">
        UPDATE CLMS_ADDITION_DOC_SEND T
        SET T.UPDATED_DATE = NOW(),
        T.UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        T.UPLOAD_TIME = #{uploadTime,jdbcType=TIMESTAMP}
        WHERE T.REPORT_NO = #{reportNo}
        AND T.CASE_TIMES = #{caseTimes}
        AND T.UPLOAD_TIME IS NULL
        AND T.RECALL_FLAG = '0'
    </update>

    <select id="getSmallCodeByReportNo" resultType="com.paic.ncbs.claim.model.dto.doc.DocumentTypeDTO">
        SELECT DISTINCT
        T.SMALL_TYPE_CODE smallCode,
        T.SMALL_TYPE_NAME smallName
        FROM CLMS_DOCUMENT_TYPE T, CLMS_CASE_CLASS CC
        WHERE T.MAP_TYPE = '1'
        AND CC.CASE_SUB_CLASS = T.BIG_TYPE_CODE
        AND CC.REPORT_NO = #{reportNo}
        AND CC.CASE_TIMES = #{caseTimes}
        AND CC.IS_EFFECTIVE = 'Y'
    </select>

    <select id="getSmallTypeName" resultType="String">
        SELECT DISTINCT T.SMALL_TYPE_NAME
        FROM CLMS_DOCUMENT_TYPE T
        where t.map_type = '1'
        and t.small_type_code IN
        <foreach collection="smallCodeArr" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getDutyRefuseReason" resultType="String">
        select
        t2.conclusion_cause_desc
        from CLMS_VERIFY_CONCLUSION t1,
        CLMS_verify_cnclusion_cause t2
        where t1.REPORT_NO = #{reportNo}
        and t1.CASE_TIMES = #{caseTimes}
        and t1.conlusion_cause_code=t2.conclusion_cause_code
        AND t1.IS_EFFECTIVE = 'Y'
        and t1.task_code =
        (select * from
        (select t.task_code from CLMS_VERIFY_CONCLUSION t where
        t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        AND t.IS_EFFECTIVE = 'Y'
        order by t.created_date desc)
        as temp limit 1
        )
    </select>

    <update id="updateAfterOtMailSend">
        UPDATE CLMS_ADDITION_DOC_SEND T
        SET T.OT_MAIL_SEND = '02'
        ,UPDATED_DATE = NOW()
        WHERE T.REPORT_NO = #{reportNo}
        AND T.CASE_TIMES = #{caseTimes}
        AND T.RECALL_FLAG = '0'
    </update>

    <!-- 根据报案号和赔付次数获取核赔说明 -->
    <select id="getVerifyCommont" resultType="string">
        select t.AUDITING_COMMONT
        from CLMS_VERIFY_CONCLUSION t
        where t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        and t.AUDITING_COMMONT is not null
        AND t.IS_EFFECTIVE = 'Y'
        and t.task_code =
        (select * from
        (select t1.task_code from CLMS_VERIFY_CONCLUSION t1 where
        t1.REPORT_NO = #{reportNo}
        and t1.CASE_TIMES = #{caseTimes}
        and t1.AUDITING_COMMONT is not null
        AND t1.IS_EFFECTIVE = 'Y'
        order by t1.created_date desc)
        as temp limit 1
        )
    </select>
</mapper>