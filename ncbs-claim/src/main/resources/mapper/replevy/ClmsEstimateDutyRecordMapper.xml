<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.replevy.dao.ClmsEstimateDutyRecordMapper">

    <!-- 估损责任记录查询结果映射 -->
    <resultMap id="EstimateDutyRecordQueryResultMap" type="com.paic.ncbs.claim.replevy.dto.EstimateDutyRecordQueryDTO">
        <result column="DUTY_CODE" property="dutyCode"/>
        <result column="duty_NAME" property="dutyName"/>
        <result column="PLAN_CODE" property="planCode"/>
    </resultMap>

    <!-- 根据报案号查询估损责任记录 -->
    <select id="getEstimateDutyRecordByReportNo" resultMap="EstimateDutyRecordQueryResultMap">
        SELECT  
            DUTY_CODE,
            duty_NAME,
            PLAN_CODE
        FROM CLMS_ESTIMATE_DUTY_RECORD pp, clm_case_base cb
        WHERE pp.CASE_NO = cb.CASE_NO 
          AND cb.REPORT_NO = #{reportNo}
        GROUP BY PLAN_CODE
    </select>

</mapper>
