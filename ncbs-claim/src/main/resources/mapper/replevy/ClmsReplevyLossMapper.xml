<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.paic.ncbs.claim.replevy.dao.ClmsReplevyLossMapper">
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="com.paic.ncbs.claim.replevy.entity.ClmsReplevyLoss">
		 <id column="id" property="id"/> 
		 <result column="replevy_detail_id" property="replevyDetailId"/> 
		 <result column="report_no" property="reportNo"/> 
		 <result column="replevy_no" property="replevyNo"/> 
		 <result column="replevy_times" property="replevyTimes"/>
		 <result column="case_times" property="caseTimes"/>
		 <result column="clause_code" property="clauseCode"/> 
		 <result column="plan_code" property="planCode"/>
		 <result column="plan_name" property="planName"/>
		 <result column="duty_code" property="dutyCode"/>
		 <result column="duty_name" property="dutyName"/> 
		 <result column="duty_detail_code" property="dutyDetailCode"/>
		 <result column="replevied_type" property="repleviedType"/> 
		 <result column="replevied_loss" property="repleviedLoss"/> 
		 <result column="price" property="price"/> 
		 <result column="unit" property="unit"/> 
		 <result column="currency" property="currency"/> 
		 <result column="replevied_money" property="repleviedMoney"/> 
		 <result column="approve_flag" property="approveFlag"/> 
		 <result column="valid_flag" property="validFlag"/> 
		 <result column="flag" property="flag"/> 
		 <result column="serial_no" property="serialNo"/> 
		 <result column="created_by" property="createdBy"/> 
		 <result column="sys_ctime" property="sysCtime"/> 
		 <result column="updated_by" property="updatedBy"/> 
		 <result column="sys_utime" property="sysUtime"/> 
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="Base_Column_List">
		 id, replevy_detail_id, report_no, replevy_no, replevy_times,case_times,
		 clause_code, plan_code,plan_name, duty_code, duty_name, duty_detail_code,
		 replevied_type, replevied_loss, price, unit,
		 currency, replevied_money, approve_flag, valid_flag, flag,
		 serial_no, created_by, sys_ctime, updated_by, sys_utime
		
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null" >
			and id = #{id}
		</if>
		<if test="replevyDetailId != null" >
			and replevy_detail_id = #{replevyDetailId}
		</if>
		<if test="reportNo != null" >
			and report_no = #{reportNo}
		</if>
		<if test="replevyNo != null" >
			and replevy_no = #{replevyNo}
		</if>
		<if test="replevyTimes != null" >
			and replevy_times = #{replevyTimes}
		</if>
		<if test="caseTimes != null" >
			and case_times = #{caseTimes}
		</if>
		<if test="clauseCode != null" >
			and clause_code = #{clauseCode}
		</if>
		<if test="planCode != null" >
			and plan_code = #{planCode}
		</if>
		<if test="planName != null" >
			and plan_name = #{planName}
		</if>
		<if test="dutyCode != null" >
			and duty_code = #{dutyCode}
		</if>
		<if test="dutyName != null" >
			and duty_name = #{dutyName}
		</if>
		<if test="dutyDetailCode != null" >
			and duty_detail_code = #{dutyDetailCode}
		</if>
		<if test="repleviedType != null" >
			and replevied_type = #{repleviedType}
		</if>
		<if test="repleviedLoss != null" >
			and replevied_loss = #{repleviedLoss}
		</if>
		<if test="price != null" >
			and price = #{price}
		</if>
		<if test="unit != null" >
			and unit = #{unit}
		</if>
		<if test="currency != null" >
			and currency = #{currency}
		</if>
		<if test="repleviedMoney != null" >
			and replevied_money = #{repleviedMoney}
		</if>
		<if test="approveFlag != null" >
			and approve_flag = #{approveFlag}
		</if>
		<if test="validFlag != null" >
			and valid_flag = #{validFlag}
		</if>
		<if test="flag != null" >
			and flag = #{flag}
		</if>
		<if test="serialNo != null" >
			and serial_no = #{serialNo}
		</if>
		<if test="createdBy != null" >
			and created_by = #{createdBy}
		</if>
		<if test="sysCtime != null" >
			and sys_ctime = #{sysCtime}
		</if>
		<if test="updatedBy != null" >
			and updated_by = #{updatedBy}
		</if>
		<if test="sysUtime != null" >
			and sys_utime = #{sysUtime}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from clms_replevy_loss
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_replevy_loss
		where id = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_replevy_loss
		where id in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyLoss">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from clms_replevy_loss
		where id = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from clms_replevy_loss
		where id in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyLoss">
		insert into clms_replevy_loss (id, replevy_detail_id, report_no, replevy_no, replevy_times,
		    case_times,clause_code, plan_code,plan_name, duty_code, duty_name, duty_detail_code,
		    replevied_type, replevied_loss, price, unit,
			currency, replevied_money, approve_flag, valid_flag, flag, 
			serial_no, created_by, sys_ctime, updated_by, sys_utime
			)
		values(#{id}, #{replevyDetailId}, #{reportNo}, #{replevyNo}, #{replevyTimes}, 
			#{clauseCode}, #{planCode},#{planName}, #{dutyCode}, #{dutyName}, #{dutyDetailCode},
		    #{repleviedType}, #{repleviedLoss}, #{price}, #{unit},
			#{currency}, #{repleviedMoney}, #{approveFlag}, #{validFlag}, #{flag}, 
			#{serialNo}, #{createdBy}, #{sysCtime}, #{updatedBy}, #{sysUtime}
			)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyLoss">
		insert into clms_replevy_loss
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				id,
			</if>
			<if test="replevyDetailId != null" >
				replevy_detail_id,
			</if>
			<if test="reportNo != null" >
				report_no,
			</if>
			<if test="replevyNo != null" >
				replevy_no,
			</if>
			<if test="replevyTimes != null" >
				replevy_times,
			</if>
			<if test="caseTimes != null" >
				case_times,
			</if>
			<if test="clauseCode != null" >
				clause_code,
			</if>
			<if test="planCode != null" >
				plan_code,
			</if>
			<if test="planName != null" >
				plan_name,
			</if>
			<if test="dutyCode != null" >
				duty_code,
			</if>
			<if test="dutyName != null" >
				duty_name,
			</if>
			<if test="dutyDetailCode != null" >
				duty_detail_code,
			</if>
			<if test="repleviedType != null" >
				replevied_type,
			</if>
			<if test="repleviedLoss != null" >
				replevied_loss,
			</if>
			<if test="price != null" >
				price,
			</if>
			<if test="unit != null" >
				unit,
			</if>
			<if test="currency != null" >
				currency,
			</if>
			<if test="repleviedMoney != null" >
				replevied_money,
			</if>
			<if test="approveFlag != null" >
				approve_flag,
			</if>
			<if test="validFlag != null" >
				valid_flag,
			</if>
			<if test="flag != null" >
				flag,
			</if>
			<if test="serialNo != null" >
				serial_no,
			</if>
			<if test="createdBy != null" >
				created_by,
			</if>
			<if test="sysCtime != null" >
				sys_ctime,
			</if>
			<if test="updatedBy != null" >
				updated_by,
			</if>
			<if test="sysUtime != null" >
				sys_utime,
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="replevyDetailId != null" >
				#{replevyDetailId},
			</if>
			<if test="reportNo != null" >
				#{reportNo},
			</if>
			<if test="replevyNo != null" >
				#{replevyNo},
			</if>
			<if test="replevyTimes != null" >
				#{replevyTimes},
			</if>
			<if test="caseTimes != null" >
				#{caseTimes},
			</if>
			<if test="clauseCode != null" >
				#{clauseCode},
			</if>
			<if test="planCode != null" >
				#{planCode},
			</if>
			<if test="planName != null" >
				#{planName},
			</if>
			<if test="dutyCode != null" >
				#{dutyCode},
			</if>
			<if test="dutyName != null" >
				#{dutyName},
			</if>
			<if test="dutyDetailCode != null" >
				#{dutyDetailCode},
			</if>
			<if test="repleviedType != null" >
				#{repleviedType},
			</if>
			<if test="repleviedLoss != null" >
				#{repleviedLoss},
			</if>
			<if test="price != null" >
				#{price},
			</if>
			<if test="unit != null" >
				#{unit},
			</if>
			<if test="currency != null" >
				#{currency},
			</if>
			<if test="repleviedMoney != null" >
				#{repleviedMoney},
			</if>
			<if test="approveFlag != null" >
				#{approveFlag},
			</if>
			<if test="validFlag != null" >
				#{validFlag},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="serialNo != null" >
				#{serialNo},
			</if>
			<if test="createdBy != null" >
				#{createdBy},
			</if>
			<if test="sysCtime != null" >
				#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				#{sysUtime},
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyLoss">
		update clms_replevy_loss
		<set>
			<if test="replevyDetailId != null" >
				replevy_detail_id=#{replevyDetailId},
			</if>
			<if test="reportNo != null" >
				report_no=#{reportNo},
			</if>
			<if test="replevyNo != null" >
				replevy_no=#{replevyNo},
			</if>
			<if test="replevyTimes != null" >
				replevy_times=#{replevyTimes},
			</if>
			<if test="caseTimes != null" >
				case_times = #{caseTimes},
			</if>
			<if test="clauseCode != null" >
				clause_code=#{clauseCode},
			</if>
			<if test="planCode != null" >
				plan_code=#{planCode},
			</if>
			<if test="planName != null" >
				plan_name=#{planName},
			</if>
			<if test="dutyCode != null" >
				duty_code=#{dutyCode},
			</if>
			<if test="dutyName != null" >
				duty_name=#{dutyName},
			</if>
			<if test="dutyDetailCode != null" >
				duty_detail_code=#{dutyDetailCode},
			</if>
			<if test="repleviedType != null" >
				replevied_type=#{repleviedType},
			</if>
			<if test="repleviedLoss != null" >
				replevied_loss=#{repleviedLoss},
			</if>
			<if test="price != null" >
				price=#{price},
			</if>
			<if test="unit != null" >
				unit=#{unit},
			</if>
			<if test="currency != null" >
				currency=#{currency},
			</if>
			<if test="repleviedMoney != null" >
				replevied_money=#{repleviedMoney},
			</if>
			<if test="approveFlag != null" >
				approve_flag=#{approveFlag},
			</if>
			<if test="validFlag != null" >
				valid_flag=#{validFlag},
			</if>
			<if test="flag != null" >
				flag=#{flag},
			</if>
			<if test="serialNo != null" >
				serial_no=#{serialNo},
			</if>
			<if test="createdBy != null" >
				created_by=#{createdBy},
			</if>
			<if test="sysCtime != null" >
				sys_ctime=#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				updated_by=#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				sys_utime=#{sysUtime},
			</if>
		</set>
		where id = #{id}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyLoss">
		update clms_replevy_loss
		set replevy_detail_id=#{replevyDetailId},
			report_no=#{reportNo},
			replevy_no=#{replevyNo},
			replevy_times=#{replevyTimes},
		    case_times=#{caseTimes},
			clause_code=#{clauseCode},
			plan_code=#{planCode},
			plan_name=#{planName},
			duty_code=#{dutyCode},
			duty_name=#{dutyName},
			duty_detail_code=#{dutyDetailCode},
			replevied_type=#{repleviedType},
			replevied_loss=#{repleviedLoss},
			price=#{price},
			unit=#{unit},
			currency=#{currency},
			replevied_money=#{repleviedMoney},
			approve_flag=#{approveFlag},
			valid_flag=#{validFlag},
			flag=#{flag},
			serial_no=#{serialNo},
			created_by=#{createdBy},
			sys_ctime=#{sysCtime},
			updated_by=#{updatedBy},
			sys_utime=#{sysUtime}
		where id = #{id}
	</update>
	<!-- 按主键查询一条记录 -->
	<select id="selectByReplevyDetailId" resultMap="BaseResultMap" parameterType="map">
		select
		<include refid="Base_Column_List" />
		from clms_replevy_loss
		where replevy_detail_id=#{replevyDetailId} order by sys_ctime asc
	</select>
	<delete id="deleteByReplevyDetailId" parameterType="map">
		delete from clms_replevy_loss
		where replevy_detail_id=#{replevyDetailId}
	</delete>
	<select id="getTotalRepleviedMoney" resultType="java.math.BigDecimal" parameterType="map">
		select sum(replevied_money) from clms_replevy_loss
		where replevy_no=#{replevyNo} and valid_flag='Y'
	</select>
	<select id="selectPlanByReplevyNo" resultMap="BaseResultMap" parameterType="java.lang.String">
		select
			replevy_no,
			replevy_times,
			plan_code,
			sum(replevied_money) as replevied_money
			from clms_replevy_loss
		where replevy_no = #{replevyNo}
			and valid_flag = 'Y'
			group by replevy_no,
			replevy_times,
			plan_code
	</select>
	<select id="selectByReplevyNoAndPlanCode" resultMap="BaseResultMap" parameterType="map">
		select
		<include refid="Base_Column_List" />
		from clms_replevy_loss
		where replevy_no= #{replevyNo}
			and plan_code = #{planCode}
			and valid_flag = 'Y'
	</select>
	<select id="getHistoryRepleviedAmountByReplevyNo" resultType="com.paic.ncbs.claim.replevy.dto.DutyPlanAmountDTO">
		SELECT
			dp.DUTY_CODE as dutyCode,
			dp.PLAN_CODE as planCode,
			sum(dp.replevied_money) as totalAmount
		FROM clms_replevy_loss dp
		WHERE dp.report_no = #{reportNo}
		  AND dp.valid_flag = 'Y'
		<if test="replevyDetailId!=null">
			AND dp.replevy_detail_id != #{replevyDetailId}
		</if>
		GROUP BY dp.DUTY_CODE, dp.PLAN_CODE
	</select>
	<select id="getTotalRepleviedMoneyByDetailId" resultType="java.math.BigDecimal">
		SELECT COALESCE(SUM(replevied_money), 0)
		FROM clms_replevy_loss
		WHERE replevy_detail_id = #{replevyDetailId}
		  AND valid_flag = 'Y'
	</select>
</mapper>
