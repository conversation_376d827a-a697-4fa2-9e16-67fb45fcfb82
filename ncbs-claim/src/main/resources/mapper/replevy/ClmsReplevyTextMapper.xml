<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.paic.ncbs.claim.replevy.dao.ClmsReplevyTextMapper">
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="com.paic.ncbs.claim.replevy.entity.ClmsReplevyText">
		 <id column="id" property="id"/> 
		 <result column="replevy_id" property="replevyId"/>
		 <result column="replevy_charge_id" property="replevyChargeId"/>
		 <result column="report_no" property="reportNo"/>
		 <result column="replevy_no" property="replevyNo"/> 
		 <result column="replevy_times" property="replevyTimes"/>
		 <result column="case_times" property="caseTimes"/>
		 <result column="replevy_role" property="replevyRole"/> 
		 <result column="opinion_type" property="opinionType"/>
		 <result column="apply_um" property="applyUm"/>
		 <result column="apply_text" property="applyText"/>
		 <result column="apply_date" property="applyDate"/>
		 <result column="approve_um" property="approveUm"/> 
		 <result column="make_com" property="makeCom"/> 
		 <result column="approve_opinion" property="approveOpinion"/> 
		 <result column="handle_text" property="handleText"/> 
		 <result column="valid_flag" property="validFlag"/> 
		 <result column="flag" property="flag"/> 
		 <result column="serial_no" property="serialNo"/> 
		 <result column="handle_date" property="handleDate"/> 
		 <result column="created_by" property="createdBy"/> 
		 <result column="sys_ctime" property="sysCtime"/> 
		 <result column="updated_by" property="updatedBy"/> 
		 <result column="sys_utime" property="sysUtime"/> 
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="Base_Column_List">
		 id, replevy_id,replevy_charge_id, report_no, replevy_no, replevy_times,case_times,
		 replevy_role, opinion_type, apply_um,apply_text, apply_date, approve_um, make_com,
		 approve_opinion, handle_text, valid_flag, flag, serial_no,
		 handle_date, created_by, sys_ctime, updated_by, sys_utime
		
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null" >
			and id = #{id}
		</if>
		<if test="replevyId != null" >
			and replevy_id = #{replevyId}
		</if>
		<if test="replevyChargeId != null" >
			and replevy_charge_id = #{replevyChargeId}
		</if>
		<if test="reportNo != null" >
			and report_no = #{reportNo}
		</if>
		<if test="replevyNo != null" >
			and replevy_no = #{replevyNo}
		</if>
		<if test="replevyTimes != null" >
			and replevy_times = #{replevyTimes}
		</if>
		<if test="caseTimes != null" >
			and case_times = #{caseTimes}
		</if>
		<if test="replevyRole != null" >
			and replevy_role = #{replevyRole}
		</if>
		<if test="opinionType != null" >
			and opinion_type = #{opinionType}
		</if>
		<if test="applyUm != null" >
			and apply_um = #{applyUm}
		</if>
		<if test="applyText != null" >
			and apply_text = #{applyText}
		</if>
		<if test="applyDate != null" >
			and apply_date = #{applyDate}
		</if>
		<if test="approveUm != null" >
			and approve_um = #{approveUm}
		</if>
		<if test="makeCom != null" >
			and make_com = #{makeCom}
		</if>
		<if test="approveOpinion != null" >
			and approve_opinion = #{approveOpinion}
		</if>
		<if test="handleText != null" >
			and handle_text = #{handleText}
		</if>
		<if test="validFlag != null" >
			and valid_flag = #{validFlag}
		</if>
		<if test="flag != null" >
			and flag = #{flag}
		</if>
		<if test="serialNo != null" >
			and serial_no = #{serialNo}
		</if>
		<if test="handleDate != null" >
			and handle_date = #{handleDate}
		</if>
		<if test="createdBy != null" >
			and created_by = #{createdBy}
		</if>
		<if test="sysCtime != null" >
			and sys_ctime = #{sysCtime}
		</if>
		<if test="updatedBy != null" >
			and updated_by = #{updatedBy}
		</if>
		<if test="sysUtime != null" >
			and sys_utime = #{sysUtime}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from clms_replevy_text
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_replevy_text
		where id = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_replevy_text
		where id in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyText">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from clms_replevy_text
		where id = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from clms_replevy_text
		where id in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyText">
		insert into clms_replevy_text (id, replevy_id,replevy_charge_id, report_no, replevy_no, replevy_times,
		    case_times,replevy_role, opinion_type, apply_um,apply_text, apply_date, approve_um, make_com,
			approve_opinion, handle_text, valid_flag, flag, serial_no, 
			handle_date, created_by, sys_ctime, updated_by, sys_utime
			)
		values(#{id}, #{replevyId}, #{replevyChargeId}, #{reportNo}, #{replevyNo}, #{replevyTimes},#{caseTimes},
			#{replevyRole}, #{opinionType}, #{applyUm},#{applyText},#{applyDate}, #{approveUm}, #{makeCom},
			#{approveOpinion}, #{handleText}, #{validFlag}, #{flag}, #{serialNo}, 
			#{handleDate}, #{createdBy}, #{sysCtime}, #{updatedBy}, #{sysUtime}
			)
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyText">
		insert into clms_replevy_text
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				id,
			</if>
			<if test="replevyId != null" >
				replevy_id,
			</if>
			<if test="replevyChargeId != null" >
				replevy_charge_id,
			</if>
			<if test="reportNo != null" >
				report_no,
			</if>
			<if test="replevyNo != null" >
				replevy_no,
			</if>
			<if test="replevyTimes != null" >
				replevy_times,
			</if>
			<if test="caseTimes != null" >
				case_times,
			</if>
			<if test="replevyRole != null" >
				replevy_role,
			</if>
			<if test="opinionType != null" >
				opinion_type,
			</if>
			<if test="applyUm != null" >
				apply_um,
			</if>
			<if test="applyText != null" >
				apply_text,
			</if>
			<if test="applyDate != null" >
				apply_date,
			</if>
			<if test="approveUm != null" >
				approve_um,
			</if>
			<if test="makeCom != null" >
				make_com,
			</if>
			<if test="approveOpinion != null" >
				approve_opinion,
			</if>
			<if test="handleText != null" >
				handle_text,
			</if>
			<if test="validFlag != null" >
				valid_flag,
			</if>
			<if test="flag != null" >
				flag,
			</if>
			<if test="serialNo != null" >
				serial_no,
			</if>
			<if test="handleDate != null" >
				handle_date,
			</if>
			<if test="createdBy != null" >
				created_by,
			</if>
			<if test="sysCtime != null" >
				sys_ctime,
			</if>
			<if test="updatedBy != null" >
				updated_by,
			</if>
			<if test="sysUtime != null" >
				sys_utime,
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="replevyId != null" >
				#{replevyId},
			</if>
			<if test="replevyChargeId != null" >
				#{replevyChargeId},
			</if>
			<if test="reportNo != null" >
				#{reportNo},
			</if>
			<if test="replevyNo != null" >
				#{replevyNo},
			</if>
			<if test="replevyTimes != null" >
				#{replevyTimes},
			</if>
			<if test="caseTimes != null" >
				#{caseTimes},
			</if>
			<if test="replevyRole != null" >
				#{replevyRole},
			</if>
			<if test="opinionType != null" >
				#{opinionType},
			</if>
			<if test="applyUm != null" >
				#{applyUm},
			</if>
			<if test="applyText != null" >
				#{applyText},
			</if>
			<if test="applyDate != null" >
				#{applyDate},
			</if>
			<if test="approveUm != null" >
				#{approveUm},
			</if>
			<if test="makeCom != null" >
				#{makeCom},
			</if>
			<if test="approveOpinion != null" >
				#{approveOpinion},
			</if>
			<if test="handleText != null" >
				#{handleText},
			</if>
			<if test="validFlag != null" >
				#{validFlag},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="serialNo != null" >
				#{serialNo},
			</if>
			<if test="handleDate != null" >
				#{handleDate},
			</if>
			<if test="createdBy != null" >
				#{createdBy},
			</if>
			<if test="sysCtime != null" >
				#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				#{sysUtime},
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyText">
		update clms_replevy_text
		<set>
			<if test="replevyId != null" >
				replevy_id=#{replevyId},
			</if>
			<if test="replevyChargeId != null" >
				and replevy_charge_id = #{replevyChargeId}
			</if>
			<if test="reportNo != null" >
				report_no=#{reportNo},
			</if>
			<if test="replevyNo != null" >
				replevy_no=#{replevyNo},
			</if>
			<if test="replevyTimes != null" >
				replevy_times=#{replevyTimes},
			</if>
			<if test="caseTimes != null" >
				case_times = #{caseTimes},
			</if>
			<if test="replevyRole != null" >
				replevy_role=#{replevyRole},
			</if>
			<if test="opinionType != null" >
				opinion_type=#{opinionType},
			</if>
			<if test="applyUm != null" >
				apply_um=#{applyUm},
			</if>
			<if test="applyText != null" >
				apply_text=#{applyText},
			</if>
			<if test="applyDate != null" >
				apply_date=#{applyDate},
			</if>
			<if test="approveUm != null" >
				approve_um=#{approveUm},
			</if>
			<if test="makeCom != null" >
				make_com=#{makeCom},
			</if>
			<if test="approveOpinion != null" >
				approve_opinion=#{approveOpinion},
			</if>
			<if test="handleText != null" >
				handle_text=#{handleText},
			</if>
			<if test="validFlag != null" >
				valid_flag=#{validFlag},
			</if>
			<if test="flag != null" >
				flag=#{flag},
			</if>
			<if test="serialNo != null" >
				serial_no=#{serialNo},
			</if>
			<if test="handleDate != null" >
				handle_date=#{handleDate},
			</if>
			<if test="createdBy != null" >
				created_by=#{createdBy},
			</if>
			<if test="sysCtime != null" >
				sys_ctime=#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				updated_by=#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				sys_utime=#{sysUtime},
			</if>
		</set>
		where id = #{id}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyText">
		update clms_replevy_text
		set replevy_id=#{replevyId},
		    replevy_charge_id = #{replevyChargeId},
			report_no=#{reportNo},
			replevy_no=#{replevyNo},
			replevy_times=#{replevyTimes},
		    case_times = #{caseTimes},
			replevy_role=#{replevyRole},
		    opinion_type=#{opinionType},
			apply_um=#{applyUm},
			apply_text= #{applyText},
			apply_date= #{applyDate},
			approve_um=#{approveUm},
			make_com=#{makeCom},
			approve_opinion=#{approveOpinion},
			handle_text=#{handleText},
			valid_flag=#{validFlag},
			flag=#{flag},
			serial_no=#{serialNo},
			handle_date=#{handleDate},
			created_by=#{createdBy},
			sys_ctime=#{sysCtime},
			updated_by=#{updatedBy},
			sys_utime=#{sysUtime}
		where id = #{id}
	</update>
	<select id="selectClmsReplevyTextVo" resultType="com.paic.ncbs.claim.replevy.vo.ClmsReplevyTextVo" parameterType="com.paic.ncbs.claim.replevy.vo.ClmsReplevyTextVo">
		select
		<include refid="Base_Column_List" />
		from clms_replevy_text
		where 1=1
		<if test="reportNo != null and reportNo!=''" >
			and	report_no=#{reportNo}
		</if>
		<if test="caseTimes != null" >
			and	case_times=#{caseTimes}
		</if>
		<if test="replevyNo != null and replevyNo!=''" >
			and	replevy_no=#{replevyNo}
		</if>
		<if test="replevyTimes != null" >
			and	replevy_times=#{replevyTimes}
		</if>
		<if test="flag != null and flag!=''" >
			and	flag=#{flag}
		</if>
		<if test="replevyId != null" >
			and	replevy_id=#{replevyId}
		</if>
		<if test="replevyChargeId != null" >
			and	replevy_charge_id=#{replevyChargeId}
		</if>
		<if test="opinionType != null and opinionType!=''" >
			and	opinion_type=#{opinionType}
		</if>
		and valid_flag='Y'
		order by sys_ctime desc
	</select>

	<!-- 按主键查询一条记录 -->
	<select id="getReplevyTextList"  resultType="com.paic.ncbs.claim.replevy.entity.ClmsReplevyText" parameterType="map">
		select
		<include refid="Base_Column_List" />
		from clms_replevy_text
		where 1=1
		<if test="replevyNo != null and replevyNo!=''" >
			and	replevy_no=#{replevyNo}
		</if>
		<if test="replevyChargeId != null and replevyChargeId!=''" >
			and	replevy_charge_id=#{replevyChargeId}
		</if>
		<if test="opinionType != null and opinionType!=''" >
			and	opinion_type=#{opinionType}
		</if>
		and valid_flag='Y'
		order by sys_ctime asc
	</select>
</mapper>