<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ============================================================== -->
<!-- =======通过ins-framework-mybatis工具自动生成，请勿手工修改！======= -->
<!-- =======本配置文件中定义的节点可在自定义配置文件中直接使用！       ======= -->
<!-- ============================================================== -->
<!-- ============================================================== -->
<mapper namespace="com.paic.ncbs.claim.replevy.dao.ClmsReplevySuggestMapper">
	<!-- 通用查询结果对象-->
	<resultMap id="BaseResultMap" type="com.paic.ncbs.claim.replevy.entity.ClmsReplevySuggest">
		 <id column="id" property="id"/> 
		 <result column="report_no" property="reportNo"/> 
		 <result column="replevy_no" property="replevyNo"/> 
		 <result column="replevy_times" property="replevyTimes"/>
		 <result column="case_times" property="caseTimes"/>
		 <result column="node_type" property="nodeType"/> 
		 <result column="operator_code" property="operatorCode"/> 
		 <result column="operator_name" property="operatorName"/> 
		 <result column="make_com" property="makeCom"/> 
		 <result column="replevy_flag" property="replevyFlag"/> 
		 <result column="suggest_text" property="suggestText"/> 
		 <result column="message_type" property="messageType"/> 
		 <result column="valid_flag" property="validFlag"/> 
		 <result column="flag" property="flag"/> 
		 <result column="serial_no" property="serialNo"/> 
		 <result column="created_by" property="createdBy"/> 
		 <result column="sys_ctime" property="sysCtime"/> 
		 <result column="updated_by" property="updatedBy"/> 
		 <result column="sys_utime" property="sysUtime"/> 
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="Base_Column_List">
		 id, report_no, replevy_no, replevy_times,case_times, node_type,
		 operator_code, operator_name, make_com, replevy_flag, suggest_text,
		 message_type, valid_flag, flag, serial_no, created_by,
		 sys_ctime, updated_by, sys_utime
	</sql>

	<!-- 按对象查询记录的WHERE部分 -->
	<sql id="Base_Select_By_Entity_Where">
		<if test="id != null" >
			and id = #{id}
		</if>
		<if test="reportNo != null" >
			and report_no = #{reportNo}
		</if>
		<if test="replevyNo != null" >
			and replevy_no = #{replevyNo}
		</if>
		<if test="replevyTimes != null" >
			and replevy_times = #{replevyTimes}
		</if>
		<if test="caseTimes != null" >
			and case_times = #{caseTimes}
		</if>
		<if test="nodeType != null" >
			and node_type = #{nodeType}
		</if>
		<if test="operatorCode != null" >
			and operator_code = #{operatorCode}
		</if>
		<if test="operatorName != null" >
			and operator_name = #{operatorName}
		</if>
		<if test="makeCom != null" >
			and make_com = #{makeCom}
		</if>
		<if test="replevyFlag != null" >
			and replevy_flag = #{replevyFlag}
		</if>
		<if test="suggestText != null" >
			and suggest_text = #{suggestText}
		</if>
		<if test="messageType != null" >
			and message_type = #{messageType}
		</if>
		<if test="validFlag != null" >
			and valid_flag = #{validFlag}
		</if>
		<if test="flag != null" >
			and flag = #{flag}
		</if>
		<if test="serialNo != null" >
			and serial_no = #{serialNo}
		</if>
		<if test="createdBy != null" >
			and created_by = #{createdBy}
		</if>
		<if test="sysCtime != null" >
			and sys_ctime = #{sysCtime}
		</if>
		<if test="updatedBy != null" >
			and updated_by = #{updatedBy}
		</if>
		<if test="sysUtime != null" >
			and sys_utime = #{sysUtime}
		</if>
	</sql>

	<!-- 按对象查询记录的SQL部分 -->
	<sql id="Base_Select_By_Entity">
		select
			<include refid="Base_Column_List" />
		from clms_replevy_suggest
		<where>
			<include refid="Base_Select_By_Entity_Where" />
		</where>
	</sql>

	<!-- 按主键查询一条记录 -->
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_replevy_suggest
		where id = #{param1}
	</select>

	<!-- 按主键List查询多条记录 -->
	<select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap" parameterType="map">
		select
			<include refid="Base_Column_List" />
		from clms_replevy_suggest
		where id in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<!-- 按对象查询一页记录（多条记录） -->
	<select id="selectPage" resultMap="BaseResultMap" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevySuggest">
		<include refid="Base_Select_By_Entity" />
	</select>

	<!-- 按主键删除一条记录 -->
	<delete id="deleteByPrimaryKey" parameterType="map">
		delete from clms_replevy_suggest
		where id = #{param1}
	</delete>

	<!-- 按主键List删除多条记录 -->
	<delete id="deleteBatchByPrimaryKeys" parameterType="map">
		delete from clms_replevy_suggest
		where id in 
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</delete>

	<!-- 完整插入一条记录-->
	<insert id="insert" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevySuggest">
		insert into clms_replevy_suggest (id, report_no, replevy_no, replevy_times,case_times, node_type,
			operator_code, operator_name, make_com, replevy_flag, suggest_text, 
			message_type, valid_flag, flag, serial_no, created_by, 
			sys_ctime, updated_by, sys_utime)
		values(#{id}, #{reportNo}, #{replevyNo}, #{replevyTimes}, #{caseTimes},#{nodeType},
			#{operatorCode}, #{operatorName}, #{makeCom}, #{replevyFlag}, #{suggestText}, 
			#{messageType}, #{validFlag}, #{flag}, #{serialNo}, #{createdBy}, 
			#{sysCtime}, #{updatedBy}, #{sysUtime})
	</insert>

	<!-- 插入一条记录(为空的字段不操作) -->
	<insert id="insertSelective" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevySuggest">
		insert into clms_replevy_suggest
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				id,
			</if>
			<if test="reportNo != null" >
				report_no,
			</if>
			<if test="replevyNo != null" >
				replevy_no,
			</if>
			<if test="replevyTimes != null" >
				replevy_times,
			</if>
			<if test="caseTimes != null" >
				case_times,
			</if>
			<if test="nodeType != null" >
				node_type,
			</if>
			<if test="operatorCode != null" >
				operator_code,
			</if>
			<if test="operatorName != null" >
				operator_name,
			</if>
			<if test="makeCom != null" >
				make_com,
			</if>
			<if test="replevyFlag != null" >
				replevy_flag,
			</if>
			<if test="suggestText != null" >
				suggest_text,
			</if>
			<if test="messageType != null" >
				message_type,
			</if>
			<if test="validFlag != null" >
				valid_flag,
			</if>
			<if test="flag != null" >
				flag,
			</if>
			<if test="serialNo != null" >
				serial_no,
			</if>
			<if test="createdBy != null" >
				created_by,
			</if>
			<if test="sysCtime != null" >
				sys_ctime,
			</if>
			<if test="updatedBy != null" >
				updated_by,
			</if>
			<if test="sysUtime != null" >
				sys_utime,
			</if>
		</trim>
		values <trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="id != null" >
				#{id},
			</if>
			<if test="reportNo != null" >
				#{reportNo},
			</if>
			<if test="replevyNo != null" >
				#{replevyNo},
			</if>
			<if test="replevyTimes != null" >
				#{replevyTimes},
			</if>
			<if test="caseTimes != null" >
				#{caseTimes},
			</if>
			<if test="nodeType != null" >
				#{nodeType},
			</if>
			<if test="operatorCode != null" >
				#{operatorCode},
			</if>
			<if test="operatorName != null" >
				#{operatorName},
			</if>
			<if test="makeCom != null" >
				#{makeCom},
			</if>
			<if test="replevyFlag != null" >
				#{replevyFlag},
			</if>
			<if test="suggestText != null" >
				#{suggestText},
			</if>
			<if test="messageType != null" >
				#{messageType},
			</if>
			<if test="validFlag != null" >
				#{validFlag},
			</if>
			<if test="flag != null" >
				#{flag},
			</if>
			<if test="serialNo != null" >
				#{serialNo},
			</if>
			<if test="createdBy != null" >
				#{createdBy},
			</if>
			<if test="sysCtime != null" >
				#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				#{sysUtime},
			</if>
		</trim>
	</insert>

	<!-- 更新一条记录(为空的字段不操作) -->
	<update id="updateSelectiveByPrimaryKey" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevySuggest">
		update clms_replevy_suggest
		<set>
			<if test="reportNo != null" >
				report_no=#{reportNo},
			</if>
			<if test="replevyNo != null" >
				replevy_no=#{replevyNo},
			</if>
			<if test="replevyTimes != null" >
				replevy_times=#{replevyTimes},
			</if>
			<if test="caseTimes != null" >
				case_times = #{caseTimes},
			</if>
			<if test="nodeType != null" >
				node_type=#{nodeType},
			</if>
			<if test="operatorCode != null" >
				operator_code=#{operatorCode},
			</if>
			<if test="operatorName != null" >
				operator_name=#{operatorName},
			</if>
			<if test="makeCom != null" >
				make_com=#{makeCom},
			</if>
			<if test="replevyFlag != null" >
				replevy_flag=#{replevyFlag},
			</if>
			<if test="suggestText != null" >
				suggest_text=#{suggestText},
			</if>
			<if test="messageType != null" >
				message_type=#{messageType},
			</if>
			<if test="validFlag != null" >
				valid_flag=#{validFlag},
			</if>
			<if test="flag != null" >
				flag=#{flag},
			</if>
			<if test="serialNo != null" >
				serial_no=#{serialNo},
			</if>
			<if test="createdBy != null" >
				created_by=#{createdBy},
			</if>
			<if test="sysCtime != null" >
				sys_ctime=#{sysCtime},
			</if>
			<if test="updatedBy != null" >
				updated_by=#{updatedBy},
			</if>
			<if test="sysUtime != null" >
				sys_utime=#{sysUtime},
			</if>
		</set>
		where id = #{id}
	</update>

	<!-- 完整更新一条记录 -->
	<update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.replevy.entity.ClmsReplevySuggest">
		update clms_replevy_suggest
		set report_no=#{reportNo},
			replevy_no=#{replevyNo},
			replevy_times=#{replevyTimes},
		    case_times = #{caseTimes},
			node_type=#{nodeType},
			operator_code=#{operatorCode},
			operator_name=#{operatorName},
			make_com=#{makeCom},
			replevy_flag=#{replevyFlag},
			suggest_text=#{suggestText},
			message_type=#{messageType},
			valid_flag=#{validFlag},
			flag=#{flag},
			serial_no=#{serialNo},
			created_by=#{createdBy},
			sys_ctime=#{sysCtime},
			updated_by=#{updatedBy},
			sys_utime=#{sysUtime}
		where id = #{id}
	</update>

</mapper>