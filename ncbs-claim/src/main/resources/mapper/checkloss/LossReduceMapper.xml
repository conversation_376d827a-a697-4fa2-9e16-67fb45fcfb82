<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.LossReduceMapper">


	<insert id="saveLossReduce" parameterType="com.paic.ncbs.claim.model.dto.checkloss.LossReduceDTO">
		INSERT INTO CLMS_LOSS_REDUCE (CREATED_BY,
									  CREATED_DATE,
									  UPDATED_BY,
									  UPDATED_DATE,
									  REPORT_NO,
									  CASE_TIMES,
									  REDUCE_PROJECT,
									  REDUCE_AMOUNT,
									  REDUCE_TYPE,
									  SURVEY_TYPE,
									  REDUCE_BY,
									  ADDITIONAL_DESC,
									  CHECK_DESC,
									  STATUS,
									  POLICY_NO,
									  POLICY_CER_NO,
									  archive_time,
									  latest_batch,
									  social_credit_code,
									  company_name,
									  ID_AHCS_LOSS_REDUCE)
		VALUES (#{createdBy, jdbcType=VARCHAR},
				sysdate(),
				#{updatedBy, jdbcType=VARCHAR},
				sysdate(),
				#{reportNo, jdbcType=VARCHAR},
				#{caseTimes, jdbcType=NUMERIC},
				#{reduceProject, jdbcType=VARCHAR},
				#{reduceAmount, jdbcType=NUMERIC},
				#{reduceType, jdbcType=VARCHAR},
				#{surveyType, jdbcType=VARCHAR},
				#{reduceByStr, jdbcType=VARCHAR},
				#{additionalDesc, jdbcType=VARCHAR},
				#{checkDesc, jdbcType=VARCHAR},
				#{status, jdbcType=VARCHAR},
				#{policyNo, jdbcType=VARCHAR},
				#{policyCerNo, jdbcType=VARCHAR},
				sysdate(),
				#{latestBatch, jdbcType=VARCHAR},
				#{socialCreditCode, jdbcType=VARCHAR},
				#{companyName, jdbcType=VARCHAR} ,
				#{idAhcsLossReduce, jdbcType=VARCHAR} )
	</insert>


	<select id="getLossReduce" resultType="com.paic.ncbs.claim.model.dto.checkloss.LossReduceDTO">
		SELECT REPORT_NO reportNo,
			   CASE_TIMES caseTimes,
			   REDUCE_PROJECT reduceProject,
			   (SELECT CP.VALUE_CHINESE_NAME
				FROM CLM_COMMON_PARAMETER CP
				WHERE CP.COLLECTION_CODE = 'AHCS_REDUCE_PROJECT'
				  AND CP.VALUE_CODE = T.REDUCE_PROJECT) reduceProjectName,
			   REDUCE_AMOUNT reduceAmount,
			   REDUCE_TYPE reduceType,
			   SURVEY_TYPE surveyType,
			   (SELECT CP.VALUE_CHINESE_NAME
				FROM CLM_COMMON_PARAMETER CP
				WHERE CP.COLLECTION_CODE = 'AHCS_SURVEY_TYPE'
				  AND CP.VALUE_CODE = T.SURVEY_TYPE) surveyTypeName,
# 			   wm_concat(REDUCE_BY) reduceBystr,
			   ADDITIONAL_DESC additionalDesc,
			   CHECK_DESC checkDesc,
			   POLICY_NO  policyNo,
			   POLICY_CER_NO  policyCerNo,
			   STATUS status,
			   SOCIAL_CREDIT_CODE socialCreditCode,
			   COMPANY_NAME companyName
		FROM CLMS_LOSS_REDUCE T
		WHERE REPORT_NO = #{reportNo}
		  AND CASE_TIMES = #{caseTimes}
		  AND IS_EFFECTIVE = 'Y'
        limit 1
	</select>
	


	<update id="updateEffective" parameterType="com.paic.ncbs.claim.model.dto.checkloss.LossReduceDTO">
		UPDATE
		CLMS_LOSS_REDUCE
		SET
		UPDATED_BY = #{updatedBy},
		UPDATED_DATE = sysdate(),
		IS_EFFECTIVE = 'N'
		WHERE
		REPORT_NO = #{reportNo}
		AND CASE_TIMES = #{caseTimes}
		AND IS_EFFECTIVE = 'Y'
	</update>

	<insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
		INSERT INTO CLMS_LOSS_REDUCE (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_LOSS_REDUCE,
			REPORT_NO,
			CASE_TIMES,
			REDUCE_PROJECT,
			REDUCE_AMOUNT,
			REDUCE_TYPE,
			SURVEY_TYPE,
			REDUCE_BY,
			ADDITIONAL_DESC,
			CHECK_DESC,
			STATUS,
			POLICY_NO,
			POLICY_CER_NO,
			ARCHIVE_TIME,
			IS_EFFECTIVE,
			LATEST_BATCH,
			SOCIAL_CREDIT_CODE,
			COMPANY_NAME
		)
		SELECT
			#{userId},
			NOW(),
			#{userId},
			NOW(),
			MD5(UUID()),
			REPORT_NO,
			#{reopenCaseTimes},
			REDUCE_PROJECT,
			REDUCE_AMOUNT,
			REDUCE_TYPE,
			SURVEY_TYPE,
			REDUCE_BY,
			ADDITIONAL_DESC,
			CHECK_DESC,
			STATUS,
			POLICY_NO,
			POLICY_CER_NO,
			NOW(),
			IS_EFFECTIVE,
			LATEST_BATCH,
			SOCIAL_CREDIT_CODE,
			COMPANY_NAME
		FROM CLMS_LOSS_REDUCE
		WHERE REPORT_NO=#{reportNo}
		AND CASE_TIMES=#{caseTimes}
		AND IS_EFFECTIVE='Y'
	</insert>
</mapper>