<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.PersonObjectMapper">
	<resultMap type="com.paic.ncbs.claim.model.dto.duty.PersonObjectDTO" id="result1">
		<result column="LOSS_OBJECT_NO" property="lossObjectNo"/>
	</resultMap>
	
	<resultMap type="com.paic.ncbs.claim.model.dto.duty.PersonObjectDTO" id="result2">
		<id column="ID_AHCS_PERSON_OBJECT" property="personObjectId"/>
		<result column="REPORT_NO" property="reportNo"/>
		<result column="LOSS_OBJECT_NO" property="lossObjectNo"/>
		<result column="PARTY_NO" property="partyNo"/>
	</resultMap>
	 
	 <select id="getChannelProcessId" parameterType="string"  resultMap="result1">
		select LOSS_OBJECT_NO
		  from CLMS_PERSON_OBJECT
		 where REPORT_NO = #{reportNo}
		 		<if test="partyNo != null and partyNo != '' ">
				and PARTY_NO = #{partyNo}
			 	 </if>
		   
	</select>
	
	<insert id="savePersonObject" parameterType="com.paic.ncbs.claim.model.dto.duty.PersonObjectDTO">
		insert into CLMS_PERSON_OBJECT
		  (
			ID_AHCS_PERSON_OBJECT,
			CREATED_BY,
		   CREATED_DATE,
		   UPDATED_BY,
		   UPDATED_DATE,
		   REPORT_NO,
		   LOSS_OBJECT_NO,
		   PARTY_NO,
		   archive_time
		   )
		values
		  (
		   md5(uuid()),
		   #{createdBy},
		   SYSDATE(),
		   #{updatedBy},
		   SYSDATE(),
		   #{reportNo},
		   #{lossObjectNo},
		   #{partyNo},
		   SYSDATE()
		)
	</insert>
	
	 <select id="getPartyNo"   resultType="string" >
		select 
				t1.client_no 
		from 
				clms_insured_person t1 ,
				clms_policy_info t2
		where 
		 		t1.id_ahcs_policy_info=t2.id_ahcs_policy_info  
		 		and t2.report_no=#{reportNo}
	</select>

	<select id="getClientNoByPolicyNo" parameterType="string" resultType="string">
	  select t1.client_no 
		from CLMS_insured_person t1 , CLMS_policy_info t2
	   where t1.id_ahcs_policy_info = t2.id_ahcs_policy_info  
		 	 and t2.report_no= #{reportNo} 
		 	 and t2.id_ahcs_policy_info = #{idPolicyInfo} 
		 	 limit 1
	</select>
	

	<select id="getClientNoByReportNo"  resultType="string">
	    SELECT ip.CLIENT_NO 
		  FROM CLMS_REPORT_CUSTOMER ip
		 WHERE ip.report_no = #{reportNo} 
		       limit 1
	</select>
	

	<select id="getClientNoByInsuredPerson" parameterType="string" resultType="string">
	    SELECT t1.CLIENT_NO 
		  FROM CLMS_REPORT_CUSTOMER ip, CLMS_insured_person t1
		 WHERE ip.report_no = #{reportNo} 
		       and t1.id_ahcs_policy_info = #{idPolicyInfo}
		       and ((ip.CERTIFICATE_NO = t1.CERTIFICATE_NO and t1.NAME=ip.NAME) or ip.CLIENT_NO=t1.CLIENT_NO)
		       limit 1
	</select>
	

	<select id="getCustomerNoByPolicyNo" parameterType="string" resultType="int">
	  select count(t1.client_no)  
		from CLMS_insured_person t1 , CLMS_policy_info t2
	   where t1.id_ahcs_policy_info = t2.id_ahcs_policy_info  
		 	 and t2.report_no= #{reportNo} 
		 	 and t2.id_ahcs_policy_info = #{idPolicyInfo} 
	</select>
	

	<select id="getCustomerNoByReportNo" parameterType="string" resultType="string">
	  select t1.RISK_PERSON_NO 
		from CLMS_insured_person t1 , CLMS_policy_info t2, CLMS_REPORT_CUSTOMER t3
	   where t2.report_no = t3.report_no 
	         and t1.id_ahcs_policy_info = t2.id_ahcs_policy_info  
		 	 and t3.report_no = #{reportNo} 
		 	 and t1.id_ahcs_policy_info = #{idPolicyInfo} 
		 	 and (t3.CERTIFICATE_NO = t1.CERTIFICATE_NO or t3.CLIENT_NO=t1.CLIENT_NO)
		 	 limit 1
		 	 
		 	 
	</select>


	<select id="getLossObjectNoByPartNo" parameterType="string" resultType="string">
		select LOSS_OBJECT_NO
		from CLMS_PERSON_OBJECT
		where REPORT_NO = #{reportNo}
		<if test="partyNo != null and partyNo != '' ">
			and PARTY_NO = #{partyNo}
		</if>

	</select>
	
</mapper>