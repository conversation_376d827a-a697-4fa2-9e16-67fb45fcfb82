<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.PersonDeathMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.duty.PersonDeathDTO" id="result">
        <id column="ID_AHCS_PERSON_DEATH" property="personDeathId"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess"/>
        <result column="DEATH_PROVER" property="deathProver"/>
        <result column="DEATH_PROVER_DESC" property="deathProverDesc"/>
        <result column="DEATH_DATE" property="deathDate"/>
        <result column="DEATH_REASON" property="deathReason"/>
        <result column="DEATH_REASON_UNKNOWN" property="deathReasonUnknown"/>
        <result column="DEATH_STATUS" property="deathStatus"/>
        <result column="IS_AUTOPSY" property="isAutopsy"/>
        <result column="TASK_ID" property="taskId"/>
        <result column="STATUS" property="status"/>
        <result column="DEATH_PAPER_TYPE" property="deathPaperType"/>
        <result column="IS_THIRD_PAY" property="isThirdPay"/>
        <result column="AMOUNT" property="amount"/>
        <result column="ARCHIVE_TIME" property="archiveTime"/>
        <result column="DEATH_REASON_CODE" property="deathReasonCode"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.vo.settle.SettleDeathVO" id="result1">
        <result column="deathDate" property="deathDate"/>
        <result column="deathProver" property="deathProver"/>
        <result column="isThirdPay" property="isThirdPay"/>
        <result column="amount" property="amount"/>

    </resultMap>

    <insert id="savePersonDeath" parameterType="com.paic.ncbs.claim.model.dto.duty.PersonDeathDTO">
        insert into CLMS_person_death
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        DEATH_PROVER,
        DEATH_PROVER_DESC,
        DEATH_DATE,
        DEATH_REASON,
        DEATH_REASON_UNKNOWN,
        DEATH_STATUS,
        IS_AUTOPSY,
        TASK_ID,
        STATUS,
        DEATH_PAPER_TYPE,
        IS_THIRD_PAY,
        AMOUNT,
        ARCHIVE_TIME,
        ID_AHCS_PERSON_DEATH,
        IS_EFFECTIVE,
        DEATH_REASON_CODE
        )
        values
        (#{createdBy},
        SYSDATE(),
        #{createdBy},
        SYSDATE(),
        #{reportNo},
        #{caseTimes},
        #{idAhcsChannelProcess},
        #{deathProver},
        #{deathProverDesc},
        #{deathDate},
        #{deathReason},
        #{deathReasonUnknown},
        #{deathStatus},
        #{isAutopsy},
        #{taskId},
        #{status},
        #{deathPaperType},
        #{isThirdPay},
        #{amount},
        <if test="archiveTime != null ">
            #{archiveTime},
        </if>
        <if test="archiveTime == null ">
            SYSDATE(),
        </if>
        left(hex(uuid()),32),
        'Y',
        #{deathReasonCode}
        )
    </insert>

    <delete id="removePersonDeath">
        delete from CLMS_person_death where ID_AHCS_CHANNEL_PROCESS=#{idAhcsChannelProcess}
        <if test="taskId != null and taskId != '' ">
            and TASK_ID = #{taskId}
        </if>
    </delete>

    <update id="updateEffective" parameterType="com.paic.ncbs.claim.model.dto.duty.PersonDeathDTO">
        UPDATE
        CLMS_PERSON_DEATH
        SET
        UPDATED_BY = #{updatedBy},
        UPDATED_DATE = SYSDATE(),
        IS_EFFECTIVE = 'N'
        WHERE
        ID_AHCS_CHANNEL_PROCESS=#{idAhcsChannelProcess}
        <if test="taskId != null and taskId != '' ">
            AND TASK_ID = #{taskId}
        </if>
        AND IS_EFFECTIVE = 'Y'
    </update>

    <!-- 根据通道号获取最新环节的死亡信息 -->
    <select id="getPersonDeath" parameterType="string" resultMap="result">
        select t.ID_AHCS_PERSON_DEATH,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.DEATH_PROVER,
        t.DEATH_PROVER_DESC,
        t.DEATH_DATE,
        t.DEATH_REASON,
        t.DEATH_REASON_UNKNOWN,
        t.DEATH_STATUS,
        t.IS_AUTOPSY,
        t.TASK_ID,
        t.STATUS,
        t.DEATH_PAPER_TYPE,
        t.IS_THIRD_PAY,
        t.AMOUNT,
        t.DEATH_REASON_CODE
        from CLMS_person_death t
        where t.id_ahcs_channel_process = #{idAhcsChannelProcess}
        AND t.IS_EFFECTIVE = 'Y'
        <if test="status != null and status != '' ">
            and t.STATUS = #{status}
        </if>
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        and t.task_id =
        (select * from
        (select t1.task_id from CLMS_person_death t1 where
        t1.id_ahcs_channel_process = #{idAhcsChannelProcess}
        AND t1.IS_EFFECTIVE = 'Y'
        <if test="status != null and status != '' ">
            and t1.STATUS = #{status}
        </if>
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        order by t1.created_date desc)
        as temp limit 1
        )
    </select>

    <select id="getSettleDeath" parameterType="string" resultMap="result1">
        select t1.death_date deathDate,
        case t1.death_prover
        when 'DP_2604' then ifnull(t1.death_prover_desc, '其他机构')
        else
        (select t2.value_chinese_name
        from clm_common_parameter t2
        where t2.value_code = t1.death_prover
        and t2.collection_code = 'AHCS_DEATH_PROVER') end as deathProver,
        IS_THIRD_PAY isThirdPay,
        AMOUNT amount
        from CLMS_person_death t1
        where t1.id_ahcs_channel_process = #{idAhcsChannelProcess}
        AND t1.IS_EFFECTIVE = 'Y'
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        <if test="status != null and status != '' ">
            and t1.STATUS = #{status}
        </if>
        and t1.task_id =
        (select * from
        (select t.task_id from CLMS_person_death t where
        t.id_ahcs_channel_process = #{idAhcsChannelProcess}
        AND t.IS_EFFECTIVE = 'Y'
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        <if test="status != null and status != '' ">
            and t.STATUS = #{status}
        </if>
        order by t.created_date desc)
        as temp limit 1
        )
    </select>

    <!-- 根据通道号、环节号获取死亡信息 -->
    <select id="getPersonDeathDTO" parameterType="string" resultMap="result">
        select t.ID_AHCS_PERSON_DEATH,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.DEATH_PROVER,
        t.DEATH_PROVER_DESC,
        t.DEATH_DATE,
        t.DEATH_REASON,
        t.DEATH_REASON_UNKNOWN,
        t.DEATH_STATUS,
        t.IS_AUTOPSY,
        t.TASK_ID,
        t.STATUS,
        t.DEATH_PAPER_TYPE,
        t.IS_THIRD_PAY,
        t.AMOUNT,
        t.ARCHIVE_TIME,
        t.DEATH_REASON_CODE
        from CLMS_person_death t
        where t.id_ahcs_channel_process = #{idAhcsChannelProcess}
        and t.STATUS = '1'
        and t.TASK_ID = #{taskId}
        AND t.IS_EFFECTIVE = 'Y'
    </select>

    <select id="getPersonDeathByReportNo" parameterType="string" resultMap="result">
        select t.ID_AHCS_PERSON_DEATH,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.DEATH_PROVER,
        t.DEATH_PROVER_DESC,
        t.DEATH_DATE,
        t.DEATH_REASON,
        t.DEATH_REASON_UNKNOWN,
        t.DEATH_STATUS,
        t.IS_AUTOPSY,
        t.TASK_ID,
        t.is_third_pay,
        t.amount,
        t.DEATH_PAPER_TYPE,
        t.STATUS,
        t.DEATH_REASON_CODE
        from CLMS_person_death t
        where t.REPORT_NO = #{reportNo} and t.CASE_TIMES = #{caseTimes}
        AND t.IS_EFFECTIVE = 'Y'
        <if test="status != null and status != '' ">
            and t.STATUS = #{status}
        </if>
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        and t.task_id =
        (select * from
        (select t1.task_id from CLMS_person_death t1 where
        t1.REPORT_NO = #{reportNo} and t1.CASE_TIMES = #{caseTimes}
        AND t1.IS_EFFECTIVE = 'Y'
        <if test="status != null and status != '' ">
            and t1.STATUS = #{status}
        </if>
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        order by t1.created_date desc)
        as temp limit 1
        )
        limit 1
    </select>

    <!-- 获取死亡时间 -->
    <select id="getPersonDeathDateByReportNo" resultType="string">
        SELECT date_format(APD.DEATH_DATE,'%Y%m%d') DEATH_DATE
        FROM CLMS_PERSON_DEATH APD
        WHERE APD.REPORT_NO = #{reportNo}
        AND APD.CASE_TIMES = #{caseTimes}
        AND APD.DEATH_DATE is not null
        AND APD.IS_EFFECTIVE = 'Y'
        AND APD.TASK_ID =
        (select * from
        (select t1.TASK_ID from CLMS_person_death t1
        where t1.REPORT_NO = #{reportNo}
        and t1.CASE_TIMES = #{caseTimes}
        and t1.DEATH_DATE is not null
        AND t1.IS_EFFECTIVE = 'Y'
        order by t1.CREATED_DATE desc)
        as temp limit 1
        )
        limit 1
    </select>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        INSERT INTO CLMS_PERSON_DEATH (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_AHCS_PERSON_DEATH,
            REPORT_NO,
            CASE_TIMES,
            ID_AHCS_CHANNEL_PROCESS,
            DEATH_PROVER,
            DEATH_PROVER_DESC,
            DEATH_DATE,
            DEATH_PAPER_TYPE,
            IS_THIRD_PAY,
            AMOUNT,
            DEATH_REASON,
            DEATH_REASON_UNKNOWN,
            DEATH_STATUS,
            IS_AUTOPSY,
            TASK_ID,
            STATUS,
            ARCHIVE_TIME,
            IS_EFFECTIVE,
            ID_AHCS_ADDITIONAL_SURVEY,
            DEATH_REASON_CODE
        )
        SELECT
            #{userId},
            NOW(),
            #{userId},
            NOW(),
            MD5(UUID()),
            REPORT_NO,
            #{reopenCaseTimes},
            #{idClmChannelProcess},
            DEATH_PROVER,
            DEATH_PROVER_DESC,
            DEATH_DATE,
            DEATH_PAPER_TYPE,
            IS_THIRD_PAY,
            AMOUNT,
            DEATH_REASON,
            DEATH_REASON_UNKNOWN,
            DEATH_STATUS,
            IS_AUTOPSY,
            TASK_ID,
            STATUS,
            NOW(),
            IS_EFFECTIVE,
            ID_AHCS_ADDITIONAL_SURVEY,
            DEATH_REASON_CODE
        FROM CLMS_PERSON_DEATH
        WHERE REPORT_NO=#{reportNo}
        AND CASE_TIMES=#{caseTimes}
        AND IS_EFFECTIVE='Y'
    </insert>

    <!--根据报案号，赔付次数，taskId查询数据-->
    <select id="selectPersonDeathDTO" parameterType="com.paic.ncbs.claim.model.vo.trace.PersonTranceRequestVo" resultType="com.paic.ncbs.claim.model.vo.duty.PersonDeathVO">
        select t.ID_AHCS_PERSON_DEATH,
        t.REPORT_NO,
        t.CASE_TIMES,
        t.ID_AHCS_CHANNEL_PROCESS,
        t.DEATH_PROVER,
        t.DEATH_PROVER_DESC,
        t.DEATH_DATE,
        t.DEATH_REASON,
        t.DEATH_REASON_UNKNOWN,
        t.DEATH_STATUS,
        t.IS_AUTOPSY,
        t.TASK_ID,
        t.STATUS,
        t.DEATH_PAPER_TYPE,
        t.IS_THIRD_PAY,
        t.AMOUNT,
        t.ARCHIVE_TIME,
        t.DEATH_REASON_CODE
        from CLMS_person_death t
        where 1=1
        <if test="taskId != null and taskId!=''" >
            and t.TASK_ID = #{taskId}
        </if>
        <if test="reportNo != null and reportNo!=''" >
            and t.report_no=#{reportNo}
        </if>
        <if test="caseTimes != null" >
            and t.case_times=#{caseTimes}
        </if>
            and t.IS_EFFECTIVE = 'Y'
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.model.dto.duty.PersonDeathDTO">
    update CLMS_person_death
    <set>
        <if test="reportNo != null and reportNo!=''">
            report_no = #{reportNo,jdbcType=VARCHAR},
        </if>
        <if test="caseTimes != null">
            case_times = #{caseTimes},
        </if>
        <if test="deathProverDesc != null and deathProverDesc!=''">
            ID_AHCS_CHANNEL_PROCESS = #{deathProverDesc,jdbcType=VARCHAR},
        </if>
        <if test="deathProver != null and deathProver!=''">
            DEATH_PROVER_DESC = #{deathProver},
        </if>
        <if test="deathDate != null ">
            DEATH_DATE = #{deathDate},
        </if>
        <if test="deathReason != null  and deathReason!=''">
            DEATH_REASON = #{deathReason},
        </if>
        <if test="deathReasonUnknown != null  and deathReasonUnknown!=''">
            DEATH_REASON_UNKNOWN = #{deathReasonUnknown},
        </if>
        <if test="deathStatus != null  and deathStatus!=''">
            DEATH_STATUS = #{deathStatus},
        </if>
        <if test="isAutopsy != null  and isAutopsy!=''">
            IS_AUTOPSY = #{isAutopsy},
        </if>
        <if test="taskId != null  and taskId!=''">
            TASK_ID = #{taskId},
        </if>
        <if test="status != null  and status!=''">
            STATUS = #{status},
        </if>
        <if test="deathPaperType != null  and deathPaperType!=''">
            DEATH_PAPER_TYPE = #{deathPaperType},
        </if>
        <if test="isEffective != null and isEffective!=''">
            IS_EFFECTIVE = #{isEffective},
        </if>
        <if test="isThirdPay != null  and isThirdPay!=''">
            IS_THIRD_PAY = #{isThirdPay},
        </if>
        <if test="amount != null ">
            amount = #{isThirdPay},
        </if>
        <if test="archiveTime != null ">
            ARCHIVE_TIME = #{archiveTime},
        </if>
        <if test="createdDate != null">
            created_Date = #{createdDate},
        </if>
        <if test="updatedDate != null">
            updated_Date = #{updatedDate},
        </if>
        <if test="createdBy != null and createdBy!=''">
            created_By = #{createdBy},
        </if>
        <if test="updatedBy != null and updatedBy!=''">
            updated_By = #{updatedBy},
        </if>
        <if test="deathReasonCode != null and deathReasonCode!=''">
            DEATH_REASON_CODE = #{deathReasonCode},
        </if>
    </set>
    where ID_AHCS_PERSON_DEATH = #{personDeathId,jdbcType=VARCHAR} and  IS_EFFECTIVE = 'Y'
    </update>
    <delete id="deletePersonDeath">
        delete from CLMS_person_death
        where 1=1
        <if test="reportNo != null and reportNo!=''" >
            and report_no=#{reportNo}
        </if>
        <if test="caseTimes != null" >
            and case_times=#{caseTimes}
        </if>
    </delete>
</mapper>