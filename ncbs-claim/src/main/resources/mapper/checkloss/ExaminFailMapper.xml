<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.ExaminFailMapper">
	<resultMap type="com.paic.ncbs.claim.model.dto.checkloss.ExaminFailDTO" id="examinFailListResult">
		<result column="REPORT_NO" property="reportNo" />
		<result column="CASE_TIMES" property="caseTimes" />
		<result column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess" />
		<result column="FAIL_TYPE" property="failType" />
		<result column="FAIL_SUBJECT" property="failSubject" />
		<result column="ACCIDENT_TYPE" property="accidentType" />
		<result column="LOSS_AMOUNT" property="lossAmount" />
		<result column="TASK_CODE" property="taskCode" />
		<result column="STATUS" property="status" />
		<result column="EXAM_SCORE" property="examScore" />
		<result column="ARCHIVE_TIME"  property="archiveTime"/>
	</resultMap>

	<insert  id="addExaminFail">
		INSERT INTO CLMS_EXAMIN_FAIL(
		CREATED_BY,
		CREATED_DATE,
		UPDATED_BY,
		UPDATED_DATE,
		REPORT_NO,
		CASE_TIMES,
		ID_AHCS_CHANNEL_PROCESS,
		FAIL_TYPE,
		FAIL_SUBJECT,
		ACCIDENT_TYPE,
		LOSS_AMOUNT,
		TASK_CODE,
		STATUS,
		EXAM_SCORE,
		FAIL_TIMES,
		ID_AHCS_EXAMIN_FAIL,
		archive_time)
		VALUES(
		#{createdBy,jdbcType=VARCHAR},
		sysdate(),
		#{updatedBy,jdbcType=VARCHAR},
		sysdate(),
		#{reportNo,jdbcType=VARCHAR},
		#{caseTimes,jdbcType=NUMERIC},
		#{idAhcsChannelProcess,jdbcType=VARCHAR},
		#{failType,jdbcType=VARCHAR},
		#{failSubject,jdbcType=VARCHAR},
		#{accidentType,jdbcType=VARCHAR},
		#{lossAmount,jdbcType=NUMERIC},
		#{taskCode,jdbcType=VARCHAR},
		#{status,jdbcType=VARCHAR},
		#{examScore,jdbcType=NUMERIC},
		#{failTimes,jdbcType=NUMERIC},
		left(hex(uuid()),32),
		<if test="archiveTime != null ">
			#{archiveTime,jdbcType=TIMESTAMP}
		</if>
		<if test="archiveTime == null ">
			sysdate()
		</if>
		)
	</insert>

	<delete id="removeExaminFail">
		DELETE FROM CLMS_EXAMIN_FAIL WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
		<if test="taskCode != null and taskCode != '' ">
			AND TASK_CODE = #{taskCode}
		</if>
		<if test="channelProcessId != null and channelProcessId != '' ">
			AND ID_AHCS_CHANNEL_PROCESS = #{channelProcessId}
		</if>
	</delete>

	<select id="getExaminFail" resultType="com.paic.ncbs.claim.model.dto.checkloss.ExaminFailDTO">
		SELECT
		REPORT_NO reportNo,
		CASE_TIMES caseTimes,
		ID_AHCS_CHANNEL_PROCESS idAhcsChannelProcess,
		FAIL_TYPE failType,
		FAIL_SUBJECT failSubject,
		ACCIDENT_TYPE accidentType,
		LOSS_AMOUNT lossAmount,
		TASK_CODE taskCode,
		EXAM_SCORE examScore,
		FAIL_TIMES failTimes,
		POLICY_NO policyNo,
		STATUS status
		FROM CLMS_EXAMIN_FAIL
		WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
		<if test="status != null and status != '' ">
			AND STATUS = #{status}
		</if>
		<if test="taskCode != null and taskCode != '' ">
			AND TASK_CODE = #{taskCode}
		</if>
		<if test="channelProcessId != null and channelProcessId != '' ">
			AND ID_AHCS_CHANNEL_PROCESS = #{channelProcessId}
		</if>
		limit 1
	</select>

	<select id="getSettleExaminFail" resultType="com.paic.ncbs.claim.model.vo.settle.SettleExaminFailVO">
		SELECT REPORT_NO reportNo,
		CASE_TIMES caseTimes,
		ID_AHCS_CHANNEL_PROCESS idAhcsChannelProcess,
		FAIL_TYPE failType,
		(SELECT CP.VALUE_CHINESE_NAME
		FROM CLM_COMMON_PARAMETER CP
		WHERE CP.COLLECTION_CODE = 'AHCS_SUB_ACC_TYPE'
		AND CP.VALUE_CODE = T.ACCIDENT_TYPE) accidentTypeName,
		FAIL_SUBJECT failSubject,
		(SELECT CP.VALUE_CHINESE_NAME
		FROM CLM_COMMON_PARAMETER CP
		WHERE CP.COLLECTION_CODE = 'AHCS_FAIL_SUBJECT'
		AND CP.VALUE_CODE = T.FAIL_SUBJECT) failSubjectName,
		ACCIDENT_TYPE accidentType,
		LOSS_AMOUNT lossAmount,
		TASK_CODE taskCode,
		STATUS status
		FROM CLMS_EXAMIN_FAIL T
		WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
		<if test="status != null and status != '' ">
			AND STATUS = #{status}
		</if>
		<if test="taskCode != null and taskCode != '' ">
			AND TASK_CODE = #{taskCode}
		</if>
		<if test="idAhcsChannelProcess != null and idAhcsChannelProcess != '' ">
			AND ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
		</if>
		limit 1
	</select>


	<!-- 根据通道号、环节号获取 考试不通过信息-->
	<select id="getExaminFailList" parameterType="string" resultMap="examinFailListResult">
		select REPORT_NO,
		FAIL_TYPE,
		FAIL_SUBJECT,
		ACCIDENT_TYPE,
		LOSS_AMOUNT,
		TASK_CODE,
		STATUS,
		EXAM_SCORE,
		ARCHIVE_TIME
		from CLMS_EXAMIN_FAIL ef
		where ef.ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
		and ef.STATUS = '1'
		and ef.TASK_CODE = #{taskCode}
	</select>

	<!-- 新增多条 考试不通过信息 -->
	<insert id="addExaminFailList">
		insert into CLMS_EXAMIN_FAIL
		(CREATED_BY,
		CREATED_DATE,
		UPDATED_BY,
		UPDATED_DATE,
		REPORT_NO,
		CASE_TIMES,
		ID_AHCS_CHANNEL_PROCESS,
		FAIL_TYPE,
		FAIL_SUBJECT,
		ACCIDENT_TYPE,
		LOSS_AMOUNT,
		TASK_CODE,
		STATUS,
		EXAM_SCORE,
		ARCHIVE_TIME)
		<foreach collection="examinFailList" index="index" item="item" open="(" close=")" separator="union all">
			select #{userId},
			sysdate(),
			#{userId},
			sysdate(),
			#{item.reportNo},
			#{caseTimes},
			#{channelProcessId,jdbcType=VARCHAR},
			#{item.failType,jdbcType=VARCHAR},
			#{item.failSubject,jdbcType=VARCHAR},
			#{item.accidentType,jdbcType=VARCHAR},
			#{item.lossAmount,jdbcType=NUMERIC},
			#{item.taskCode,jdbcType=VARCHAR},
			#{item.status,jdbcType=VARCHAR},
			#{item.examScore,jdbcType=NUMERIC},
			<if test="item.archiveTime != null ">
				#{item.archiveTime,jdbcType=TIMESTAMP}
			</if>
			<if test="item.archiveTime == null ">
				sysdate()
			</if>
			from DUAL
		</foreach>
	</insert>

	<update id="updateExaminFail" parameterType="com.paic.ncbs.claim.model.dto.checkloss.ExaminFailDTO">
		update CLMS_EXAMIN_FAIL
		<trim prefix="set" suffixOverrides=",">
			<if test="updatedBy != null">UPDATED_BY=#{updatedBy},</if>
			UPDATED_DATE=sysdate(),
			<if test="failType != null">FAIL_TYPE=#{failType},</if>
			<if test="lossAmount != null">LOSS_AMOUNT=#{lossAmount},</if>
			<if test="failSubject != null">FAIL_SUBJECT=#{failSubject},</if>
			<if test="accidentType != null">ACCIDENT_TYPE=#{accidentType},</if>
		</trim>
		WHERE REPORT_NO = #{reportNo}
		AND CASE_TIMES = #{caseTimes}
		AND TASK_CODE = #{taskCode}
	</update>

	<select id="countByExaminFailConditon" parameterType="com.paic.ncbs.claim.model.dto.checkloss.ExaminFailConditonDTO" resultType="java.lang.Integer">
		select count(*) from CLMS_EXAMIN_FAIL a, CLMS_POLICY_INFO b,clms_CASE_PROCESS c, CLMS_AUTO_SETTLE_DETAIL d
		where
		a.REPORT_NO = b.REPORT_NO and b.REPORT_NO = c.REPORT_NO and c.REPORT_NO = d.REPORT_NO
		<if test="policyNo !=null and ''!= policyNo ">
			and   b.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
		</if>
		<if test="processStatus !=null and ''!= processStatus ">
			and  c.PROCESS_STATUS = #{processStatus,jdbcType=VARCHAR}
		</if>
		<if test="failSubject !=null and ''!= failSubject ">
			and  a.FAIL_SUBJECT = #{failSubject,jdbcType=VARCHAR}
		</if>
		<if test="failType !=null and ''!= failType ">
			and  a.FAIL_TYPE = #{failType,jdbcType=VARCHAR}
		</if>
		<if test="autoSettleAmount !=null ">
			and  d.AUTO_SETTLE_AMOUNT > #{autoSettleAmount,jdbcType=VARCHAR}
		</if>
	</select>


	<select id="countByExaminFailSubjectConditon" parameterType="com.paic.ncbs.claim.model.dto.checkloss.ExaminFailConditonDTO" resultType="java.lang.Integer">
		select count(*) from CLMS_EXAMIN_FAIL a, CLMS_POLICY_INFO b
		where
		a.REPORT_NO = b.REPORT_NO
		<if test="policyNo !=null and ''!= policyNo ">
			and   b.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
		</if>
		<if test="failSubject !=null and ''!= failSubject ">
			and  a.FAIL_SUBJECT = #{failSubject,jdbcType=VARCHAR}
		</if>
		<if test="failType !=null and ''!= failType ">
			and  a.FAIL_TYPE = #{failType,jdbcType=VARCHAR}
		</if>
	</select>

</mapper>