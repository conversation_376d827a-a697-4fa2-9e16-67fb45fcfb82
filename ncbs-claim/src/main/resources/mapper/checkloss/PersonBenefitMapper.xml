<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.PersonBenefitMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.duty.PersonBenefitDTO" id="personBenefitListResult">
		<id column="ID_AHCS_PERSON_BENEFIT" property="idAhcsPersonBenefit"/>
		<result column="REPORT_NO" property="reportNo"/>
		<result column="CASE_TIMES" property="caseTimes"/>
		<result column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess"/>
		<result column="BENEFIT_TYPE" property="benefitType"/>
		<result column="START_DATE" property="startDate"/>
		<result column="END_DATE" property="endDate"/>
		<result column="HOSPITAL_DAYS" property="hospitalDays"/>
		<result column="PROJ_LEVEL" property="projLevel"/>
		<result column="DEPT_CODE" property="deptCode"/>
		<result column="PART_CODE" property="partCode"/>
		<result column="OPERATION_CODE" property="operationCode"/>
		<result column="OPERATION_LEVEL" property="operationLevel"/>
		<result column="IS_AFFILIATE" property="isAffiliate"/>
		<result column="TASK_ID" property="taskId"/>
		<result column="STATUS" property="status"/>
		<result column="ORDER_NO" property="orderNo"/>
		<result column="ARCHIVE_TIME"  property="archiveTime"/>
	</resultMap>
	
	<insert id="addPersonBenefit">
	INSERT INTO CLMS_PERSON_BENEFIT (
		ID_AHCS_PERSON_BENEFIT,
		CREATED_BY,
		CREATED_DATE,
		UPDATED_BY,
		UPDATED_DATE,
		REPORT_NO,
		CASE_TIMES,
		ID_AHCS_CHANNEL_PROCESS,
		BENEFIT_TYPE,
		START_DATE,
		END_DATE,
		HOSPITAL_DAYS,
		PROJ_LEVEL,
		DEPT_CODE,
		PART_CODE,
		OPERATION_CODE,
		OPERATION_LEVEL,
		IS_AFFILIATE,
		TASK_ID,
		STATUS,
		ORDER_NO,
		ARCHIVE_TIME
		) 
		<foreach collection="personBenefitList" index="index" item="item" open="(" close=")" separator="union all">
			SELECT
			     md5(uuid()),
				 #{item.createdBy},
				SYSDATE(),
				 #{item.updatedBy},
				SYSDATE(),
				 #{item.reportNo},
				 #{item.caseTimes},
				 #{item.idAhcsChannelProcess},
				 #{item.benefitType},
				 #{item.startDate},
				 #{item.endDate},
				 #{item.hospitalDays},
				 #{item.projLevel},
				 #{item.deptCode},
				 #{item.partCode},
				 #{item.operationCode},
				 #{item.operationLevel},
				 #{item.isAffiliate},
				 #{item.taskId},
				 #{item.status},
				 #{item.orderNo}	,
				 <if test="item.archiveTime != null ">
				 #{item.archiveTime}
				</if>
				<if test="item.archiveTime == null ">
					 SYSDATE()
				 </if>			
			FROM DUAL			
		</foreach>		
	</insert>
	
	<select id="getPersonBenefit" resultType="com.paic.ncbs.claim.model.dto.duty.PersonBenefitDTO">
    SELECT REPORT_NO reportNo,
           CASE_TIMES caseTimes,
           ID_AHCS_CHANNEL_PROCESS idAhcsChannelProcess,
           BENEFIT_TYPE benefitType,
           (SELECT CP.VALUE_CHINESE_NAME
              FROM CLM_COMMON_PARAMETER CP
             WHERE CP.COLLECTION_CODE = 'AHCS_BENEFIT_CODE'
               AND CP.VALUE_CODE = BENEFIT_TYPE) benefitTypeName,
           START_DATE startDate,
           END_DATE endDate,
           HOSPITAL_DAYS hospitalDays,
           CATAGMA_PART catagmaPart,
           PROJ_LEVEL projLevel,
           DEPT_CODE deptCode,
           PART_CODE partCode,
           OPERATION_CODE operationCode,
           OPERATION_LEVEL operationLevel,
           IS_AFFILIATE isAffiliate,
           TASK_ID taskId,
           STATUS status,
           ORDER_NO orderNo
      FROM CLMS_PERSON_BENEFIT T
	  WHERE T.REPORT_NO = #{reportNo} 
		AND T.CASE_TIMES = #{caseTimes}
		<if test=" taskId != null and taskId !='' ">
	  		AND T.TASK_ID = #{taskId}	
	    </if>
		<if test=" channelProcessId != null and channelProcessId !='' ">
			AND T.ID_AHCS_CHANNEL_PROCESS = #{channelProcessId}
		</if>
	</select>	
	
	<select id="getPersonBenefitByType" resultType="com.paic.ncbs.claim.model.dto.duty.PersonBenefitDTO">
		SELECT REPORT_NO reportNo,
	           CASE_TIMES caseTimes,
	           ID_AHCS_CHANNEL_PROCESS idAhcsChannelProcess,
	           BENEFIT_TYPE benefitType,
	           (SELECT CP.VALUE_CHINESE_NAME
	              FROM CLM_COMMON_PARAMETER CP
	             WHERE CP.COLLECTION_CODE = 'AHCS_BENEFIT_CODE'
	               AND CP.VALUE_CODE = BENEFIT_TYPE) benefitTypeName,
	           START_DATE startDate,
	           END_DATE endDate,
	           HOSPITAL_DAYS hospitalDays,
	           PROJ_LEVEL projLevel,
	           DEPT_CODE deptCode,
	           PART_CODE partCode,
	           OPERATION_CODE operationCode,
	           OPERATION_LEVEL operationLevel,
	           IS_AFFILIATE isAffiliate,
	           TASK_ID taskId,
	           STATUS status,
	           ORDER_NO orderNo
	      FROM CLMS_PERSON_BENEFIT T
		  WHERE T.REPORT_NO = #{reportNo} 
			AND T.CASE_TIMES = #{caseTimes}
			AND T.BENEFIT_TYPE = #{benefitType}
			<if test=" taskId != null and taskId !='' ">
		  		AND T.TASK_ID = #{taskId}	
		    </if>
	</select>
	
	<select id="getPersonBenefitByTypes" resultType="com.paic.ncbs.claim.model.dto.duty.PersonBenefitDTO">
		SELECT T.REPORT_NO reportNo,
		       T.CASE_TIMES caseTimes,
		       T.ID_AHCS_CHANNEL_PROCESS idAhcsChannelProcess,
		       T.BENEFIT_TYPE benefitType,
		       (SELECT CP.VALUE_CHINESE_NAME
		          FROM CLM_COMMON_PARAMETER CP
		         WHERE CP.COLLECTION_CODE = 'AHCS_BENEFIT_CODE'
		           AND CP.VALUE_CODE = T.BENEFIT_TYPE) benefitTypeName,
		       T.START_DATE startDate,
		       T.END_DATE endDate,
		       T.HOSPITAL_DAYS hospitalDays,
		       T.PROJ_LEVEL projLevel,
		       T. DEPT_CODE deptCode,
		       (SELECT OD.DEPT_NAME
		          FROM CLMS_OPERATION_DEFINE OD
		         WHERE OD.DEPT_CODE = T.DEPT_CODE
		           limit 1) deptName,
		       T.PART_CODE partCode,
		       (SELECT OD.PART_NAME
		          FROM CLMS_OPERATION_DEFINE OD
		         WHERE OD.PART_CODE = T.PART_CODE
		           limit 1) partName,
		       T.OPERATION_CODE operationCode,
		       (SELECT OD.OPERATION_NAME
		          FROM CLMS_OPERATION_DEFINE OD
		         WHERE OD.OPERATION_CODE = T.OPERATION_CODE
		           limit 1) operationName,
		       T.OPERATION_LEVEL operationLevel,
		       T.IS_AFFILIATE isAffiliate,
		       T.TASK_ID taskId,
		       T.STATUS status,
		       T.ORDER_NO orderNo
		  FROM CLMS_PERSON_BENEFIT T
		  WHERE T.REPORT_NO = #{reportNo} 
			AND T.CASE_TIMES = #{caseTimes}
		   <if test=" taskId != null and taskId !='' ">
		  	 AND T.TASK_ID = #{taskId}	
		    </if>
			<if test=" idAhcsChannelProcess != null and idAhcsChannelProcess !='' ">
				AND T.ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
			</if>
			AND T.BENEFIT_TYPE IN 
			<foreach collection="benefitTypes" item="item" open="(" close=")" separator=",">
				#{item}			
			</foreach>
	</select>
	
	
	<delete id="removePersonBenefit">
		DELETE FROM CLMS_PERSON_BENEFIT T
		WHERE T.REPORT_NO = #{reportNo} 
		  AND T.CASE_TIMES = #{caseTimes}	
		  <if test=" taskId != null and taskId !='' ">
		  	AND T.TASK_ID = #{taskId}	
		  </if>
		  <if test=" channelId != null and channelId !='' ">
			AND T.ID_AHCS_CHANNEL_PROCESS = #{channelId}
		  </if>
	</delete>	
	
	<!-- 根据通道号、环节号获取津贴信息 -->
	<select id="getPersonBenefitList" resultMap="personBenefitListResult">
    SELECT REPORT_NO,
           CASE_TIMES,
           ID_AHCS_CHANNEL_PROCESS,
           BENEFIT_TYPE,
           START_DATE,
           END_DATE,
           HOSPITAL_DAYS,
           PROJ_LEVEL,
           DEPT_CODE,
           PART_CODE,
           OPERATION_CODE,
           OPERATION_LEVEL,
           IS_AFFILIATE,
           TASK_ID,
           STATUS,
           ORDER_NO,
		   ARCHIVE_TIME 
      FROM CLMS_PERSON_BENEFIT pb
	 WHERE pb.ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess} 
		   AND pb.STATUS = '1' 
		   AND pb.TASK_ID = #{taskId} 
	</select>
	
	<!-- 新增多条 津贴信息 -->
	<insert id="addPersonBenefitList">
		insert into CLMS_PERSON_BENEFIT
		  (CREATED_BY,
		   CREATED_DATE,
		   UPDATED_BY,
		   UPDATED_DATE,
		   ID_AHCS_PERSON_BENEFIT,
		   REPORT_NO,
		   CASE_TIMES,
		   ID_AHCS_CHANNEL_PROCESS,
		   BENEFIT_TYPE,
		   START_DATE,
		   END_DATE,
		   HOSPITAL_DAYS,
		   PROJ_LEVEL,
		   DEPT_CODE,
		   PART_CODE,
		   OPERATION_CODE,
		   OPERATION_LEVEL,
		   IS_AFFILIATE,
		   TASK_ID,
		   STATUS,
		   ORDER_NO,
		   archive_time)
		<foreach collection="personBenefitList" index="index" item="item" open="(" close=")" separator="union all">
    select #{userId},
		   SYSDATE(),
		   #{userId},
		   SYSDATE(),
		   left(hex(uuid()),32),
		   #{item.reportNo},
		   #{caseTimes},
		   #{channelProcessId},
		   #{item.benefitType},
		   #{item.startDate},
		   #{item.endDate},
		   #{item.hospitalDays},
		   #{item.projLevel},
		   #{item.deptCode},
		   #{item.partCode},
		   #{item.operationCode},
		   #{item.operationLevel},
		   #{item.isAffiliate},
		   #{item.taskId} ,
		   #{item.status},
		   #{item.orderNo}  ,
		   <if test="item.archiveTime != null ">
				 #{item.archiveTime}
			</if>
			<if test="item.archiveTime == null ">
				 SYSDATE()
			 </if>	
	  from DUAL
		</foreach>
	</insert>

	<insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
		INSERT INTO CLMS_PERSON_BENEFIT (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_PERSON_BENEFIT,
			REPORT_NO,
			CASE_TIMES,
			ID_AHCS_CHANNEL_PROCESS,
			BENEFIT_TYPE,
			START_DATE,
			END_DATE,
			HOSPITAL_DAYS,
			CATAGMA_PART,
			PROJ_LEVEL,
			CATAGMA_TYPE,
			DEPT_CODE,
			PART_CODE,
			OPERATION_CODE,
			OPERATION_LEVEL,
			IS_AFFILIATE,
			NURSE_DAYS,
			TASK_ID,
			STATUS,
			ORDER_NO,
			ARCHIVE_TIME,
			ID_AHCS_ADDITIONAL_SURVEY
		)
		SELECT
			#{userId},
			NOW(),
			#{userId},
			NOW(),
			MD5(UUID()),
			REPORT_NO,
			#{reopenCaseTimes},
			#{idClmChannelProcess},
			BENEFIT_TYPE,
			START_DATE,
			END_DATE,
			HOSPITAL_DAYS,
			CATAGMA_PART,
			PROJ_LEVEL,
			CATAGMA_TYPE,
			DEPT_CODE,
			PART_CODE,
			OPERATION_CODE,
			OPERATION_LEVEL,
			IS_AFFILIATE,
			NURSE_DAYS,
			TASK_ID,
			STATUS,
			ORDER_NO,
			NOW(),
			ID_AHCS_ADDITIONAL_SURVEY
			FROM CLMS_PERSON_BENEFIT
		WHERE REPORT_NO=#{reportNo}
		AND CASE_TIMES=#{caseTimes}
	</insert>
</mapper>