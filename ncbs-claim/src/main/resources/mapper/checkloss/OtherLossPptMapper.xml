<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.OtherLossPptMapper">
	<resultMap type="com.paic.ncbs.claim.model.dto.duty.OtherLossPptDTO" id="result">
		<result column="REPORT_NO" property="reportNo" />
		<result column="CASE_TIMES" property="caseTimes" />
		<result column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess" />
		<result column="LOSS_ITEM" property="lossItem" />
		<result column="ACCIDENT_REASON" property="accidentReason" />
		<result column="LOSS_COUNT" property="lossCount" />
		<result column="SPECIFICATION" property="specification" />
		<result column="LOSS_UNIT" property="lossUnit" />
		<result column="LOSS_UNIT_PRICE" property="lossUnitPrice" />
		<result column="LOSS_DEGREE" property="lossDegree" />
		<result column="REDUCE_AMOUNT" property="reduceAmount" />
		<result column="LOSS_BASIS" property="lossBasis" />
		<result column="LOSS_TOTAL_AMOUNT" property="lossTotalAmount" />
		<result column="TASK_CODE" property="taskCode" />
		<result column="STATUS" property="status" />
		<result column="ARCHIVE_TIME"  property="archiveTime"/>
		<result column="PARENT_CODE"  property="parentCode"/>
		<result column="FORE_PARENT_CODE"  property="foreParentCode"/>
	</resultMap>

	<delete id="removeOtherLossPptList" >
		DELETE FROM CLMS_OTHER_LOSS_PPT WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes} AND TASK_CODE = #{taskCode}
	</delete>
	
	<select id="getOtherLossPptListByReportNo" parameterType="string" resultMap="result">
		SELECT  
			t1.REPORT_NO
			,t1.CASE_TIMES
			,t1.ID_AHCS_CHANNEL_PROCESS
			,t1.LOSS_ITEM
			,t1.ACCIDENT_REASON
			,t1.LOSS_COUNT
			,t1.SPECIFICATION
			,t1.LOSS_UNIT
			,t1.LOSS_UNIT_PRICE
			,t1.LOSS_DEGREE
			,t1.REDUCE_AMOUNT
			,t1.LOSS_BASIS
			,t1.LOSS_TOTAL_AMOUNT
			,t1.TASK_CODE
			,t1.STATUS
			,t2.PARENT_CODE
			,(select case when t3.PARENT_CODE='0' then '' else t3.PARENT_CODE end  from CLMS_LOSS_ITEM_DEFINE t3 where t3.LOSS_ITEM_CODE = t2.PARENT_CODE) FORE_PARENT_CODE
		FROM CLMS_OTHER_LOSS_PPT t1,CLMS_LOSS_ITEM_DEFINE t2
		WHERE t1.LOSS_ITEM = t2.LOSS_ITEM_CODE and t1.REPORT_NO = #{reportNo} AND t1.CASE_TIMES = #{caseTimes}
		<if test="status != null and status != '' ">
	    	AND t1.STATUS = #{status}
	    </if>
	    <if test="taskCode != null and taskCode != '' ">
	    	AND t1.TASK_CODE = #{taskCode}
	    </if>		
	</select>
	
	<!-- 新增多条 三者车辆信息-->
	<insert id="addOtherLossPptList">
	    insert into CLMS_OTHER_LOSS_PPT
		  (CREATED_BY,
		   CREATED_DATE,
		   UPDATED_BY,
		   UPDATED_DATE,
			ID_AHCS_OTHER_LOSS_PPT,
		  	REPORT_NO
			,CASE_TIMES
			,ID_AHCS_CHANNEL_PROCESS
			,LOSS_ITEM
			,ACCIDENT_REASON
			,LOSS_COUNT
			,SPECIFICATION
			,LOSS_UNIT
			,LOSS_UNIT_PRICE
			,LOSS_DEGREE
			,REDUCE_AMOUNT
			,LOSS_BASIS
			,LOSS_TOTAL_AMOUNT
			,TASK_CODE
			,STATUS
		   	,archive_time)
	    values
		<foreach collection="otherLossPptList" index="index" item="item"  separator=",">
   		   (#{userId},
		   now(),
		   #{userId},
		   now(),
   		   #{item.idOtherLossPpt},
		   #{item.reportNo},
		   #{caseTimes},
		   #{channelProcessId},
		   #{item.lossItem},
		   #{item.accidentReason} ,
		   #{item.lossCount} ,
		   #{item.specification},
		   #{item.lossUnit},
		   #{item.lossUnitPrice},
		   #{item.lossDegree},
		   #{item.reduceAmount},
		   #{item.lossBasis},
		   #{item.lossTotalAmount},
		   #{item.taskCode},
		   #{item.status},
		   now())
		</foreach>
	</insert> 
	
		<select id="getOtherLossPptList" parameterType="string" resultMap="result">
		 select ef.REPORT_NO
				,ef.CASE_TIMES
				,ef.ID_AHCS_CHANNEL_PROCESS
				,ef.LOSS_ITEM
				,ef.ACCIDENT_REASON
				,ef.LOSS_COUNT
				,ef.SPECIFICATION
				,ef.LOSS_UNIT
				,ef.LOSS_UNIT_PRICE
				,ef.LOSS_DEGREE
				,ef.REDUCE_AMOUNT
				,ef.LOSS_BASIS
				,ef.LOSS_TOTAL_AMOUNT
				,ef.TASK_CODE
				,ef.STATUS
			    ,ef.ARCHIVE_TIME 
		   from CLMS_OTHER_LOSS_PPT ef
		  where ef.ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess} 
		 	    and ef.STATUS = '1' 
		        and ef.TASK_CODE = #{taskCode} 
	</select> 
	
	<select id="getAccidentReasonList" parameterType="string" resultType="string">
		select DUTY_NAME from CLMS_policy_duty where id_ahcs_policy_plan in
			(select tt.id_ahcs_policy_plan from CLMS_policy_plan tt where tt.id_ahcs_policy_info in
               (select id_ahcs_policy_info from CLMS_policy_info where report_no = #{reportNo}))
	</select>
</mapper>