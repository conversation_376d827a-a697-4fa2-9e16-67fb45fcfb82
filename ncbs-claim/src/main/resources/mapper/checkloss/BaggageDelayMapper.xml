<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.BaggageDelayMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.checkloss.BaggageDelayDTO" id="baggageDelayListResult">
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess"/>
        <result column="FLIGHT_NO" property="flightNo"/>
        <result column="ARRIVAL_TIME" property="arrivalTime"/>
        <result column="SIGN_IN_TIME" property="signInTime"/>
        <result column="DEPART_PLACE" property="departPlace"/>
        <result column="ARRIVAL_PLACE" property="arrivalPlace"/>
        <result column="DELAY_DURATION" property="delayDuration"/>
        <result column="DELAY_DURATION_UNIT" property="delayDurationUnit"/>
        <result column="TASK_CODE" property="taskCode"/>
        <result column="STATUS" property="status"/>
        <result column="ARCHIVE_TIME" property="archiveTime"/>
    </resultMap>

    <insert id="addBaggageDelay">
        INSERT INTO CLMS_BAGGAGE_DELAY
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        FLIGHT_NO,
        ARRIVAL_TIME,
        SIGN_IN_TIME,
        DEPART_PLACE,
        ARRIVAL_PLACE,
        DELAY_DURATION,
        DELAY_DURATION_UNIT,
        TASK_CODE,
        STATUS,
        IS_EFFECTIVE,
        archive_time,
        ID_AHCS_BAGGAGE_DELAY
        )
        VALUES(
        #{createdBy,jdbcType=VARCHAR},
        now(),
        #{updatedBy,jdbcType=VARCHAR},
        now(),
        #{reportNo,jdbcType=VARCHAR},
        #{caseTimes,jdbcType=NUMERIC},
        #{idAhcsChannelProcess,jdbcType=VARCHAR},
        #{flightNo,jdbcType=VARCHAR},
        #{arrivalTime,jdbcType=TIMESTAMP},
        #{signInTime,jdbcType=TIMESTAMP},
        #{departPlace,jdbcType=VARCHAR},
        #{arrivalPlace,jdbcType=VARCHAR},
        #{delayDuration,jdbcType=VARCHAR},
        #{delayDurationUnit,jdbcType=VARCHAR},
        #{taskCode,jdbcType=VARCHAR},
        #{status,jdbcType=VARCHAR} ,
        'Y',
        <if test="archiveTime != null ">
            #{archiveTime,jdbcType=TIMESTAMP},
        </if>
        <if test="archiveTime == null ">
            now(),
        </if>
        left(hex(uuid()),32)
        )

    </insert>

    <delete id="removeBaggageDelay">
        DELETE CLMS_BAGGAGE_DELAY WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
        <if test="taskCode != null and taskCode != '' ">
            AND TASK_CODE = #{taskCode}
        </if>
        <if test="channelProcessId != null and channelProcessId != '' ">
            AND ID_AHCS_CHANNEL_PROCESS = #{channelProcessId}
        </if>
    </delete>

    <update id="updateEffective" parameterType="com.paic.ncbs.claim.model.dto.checkloss.BaggageDelayDTO">
        UPDATE
        CLMS_BAGGAGE_DELAY
        SET
        UPDATED_BY = #{updatedBy},
        UPDATED_DATE = now(),
        IS_EFFECTIVE = 'N'
        WHERE
        REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        <if test="taskCode != null and taskCode != '' ">
            AND TASK_CODE = #{taskCode}
        </if>
        <if test=" idAhcsChannelProcess != null and idAhcsChannelProcess !='' ">
            AND ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        </if>
        AND IS_EFFECTIVE = 'Y'
    </update>

    <select id="getBaggageDelay" resultType="com.paic.ncbs.claim.model.dto.checkloss.BaggageDelayDTO">
        SELECT
        REPORT_NO reportNo,
        CASE_TIMES caseTimes,
        ID_AHCS_CHANNEL_PROCESS idAhcsChannelProcess,
        FLIGHT_NO flightNo,
        ARRIVAL_TIME arrivalTime,
        SIGN_IN_TIME signInTime,
        DEPART_PLACE departPlace,
        ARRIVAL_PLACE arrivalPlace,
        (SELECT AI.AIRPORT_NAME
        FROM CLMS_AIRPORT_INFO AI
        WHERE AI.AIRPORT_CODE = DEPART_PLACE) departPlaceName,
        (SELECT AI.AIRPORT_NAME
        FROM CLMS_AIRPORT_INFO AI
        WHERE AI.AIRPORT_CODE = ARRIVAL_PLACE) arrivalPlaceName,
        DELAY_DURATION delayDuration,
        DELAY_DURATION_UNIT delayDurationUnit,
        TASK_CODE taskCode,
        STATUS status
        FROM CLMS_BAGGAGE_DELAY T
        WHERE REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        <if test="status != null and status != '' ">
            AND STATUS = #{status}
        </if>
        <if test="taskCode != null and taskCode != '' ">
            AND TASK_CODE = #{taskCode}
        </if>
        AND IS_EFFECTIVE = 'Y'
        <if test="channelProcessId != null and channelProcessId != '' ">
            AND ID_AHCS_CHANNEL_PROCESS = #{channelProcessId}
        </if>
        limit 1
    </select>

    <select id="getSettleBaggageDelay" resultType="com.paic.ncbs.claim.model.vo.settle.SettleBaggageDelayVO">
        SELECT REPORT_NO reportNo,
        CASE_TIMES caseTimes,
        ID_AHCS_CHANNEL_PROCESS idAhcsChannelProcess,
        FLIGHT_NO flightNo,
        ARRIVAL_TIME arrivalTime,
        SIGN_IN_TIME signInTime,
        DEPART_PLACE departPlace,
        ARRIVAL_PLACE arrivalPlace,
        DELAY_DURATION delayDuration,
        DELAY_DURATION_UNIT delayDurationUnit,
        (SELECT CP.VALUE_CHINESE_NAME
        FROM CLM_COMMON_PARAMETER CP
        WHERE CP.COLLECTION_CODE = 'AHCS_DURATION_UNIT'
        AND DELAY_DURATION_UNIT = CP.VALUE_CODE) delayDurationUnitName,
        TASK_CODE taskCode,
        STATUS status
        FROM CLMS_BAGGAGE_DELAY T
        WHERE REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        <if test="status != null and status != '' ">
            AND STATUS = #{status}
        </if>
        <if test="taskCode != null and taskCode != '' ">
            AND TASK_CODE = #{taskCode}
        </if>
        AND IS_EFFECTIVE = 'Y'
        <if test="idAhcsChannelProcess != null and idAhcsChannelProcess != '' ">
            AND ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        </if>
        limit 1
    </select>


    <!-- 根据通道号、环节号获取 行李延误信息-->
    <select id="getBaggageDelayList" parameterType="string" resultMap="baggageDelayListResult">
        select REPORT_NO,
        FLIGHT_NO,
        ARRIVAL_TIME,
        SIGN_IN_TIME,
        DEPART_PLACE,
        ARRIVAL_PLACE,
        DELAY_DURATION,
        DELAY_DURATION_UNIT,
        TASK_CODE,
        STATUS,
        ARCHIVE_TIME
        from CLMS_BAGGAGE_DELAY bd
        where bd.ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        and bd.STATUS = '1'
        and bd.TASK_CODE = #{taskCode}
        AND bd.IS_EFFECTIVE = 'Y'
    </select>

    <!-- 新增多条 行李延误信息 -->
    <insert id="addBaggageDelayList">
        insert into CLMS_BAGGAGE_DELAY
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        FLIGHT_NO,
        ARRIVAL_TIME,
        SIGN_IN_TIME,
        DEPART_PLACE,
        ARRIVAL_PLACE,
        DELAY_DURATION,
        DELAY_DURATION_UNIT,
        TASK_CODE,
        STATUS,
        ARCHIVE_TIME)
        <foreach collection="baggageDelayList" index="index" item="item" open="(" close=")" separator="union all">
            select #{userId},
            now(),
            #{userId},
            now(),
            #{item.reportNo},
            #{caseTimes},
            #{channelProcessId,jdbcType=VARCHAR},
            #{item.flightNo,jdbcType=VARCHAR},
            #{item.arrivalTime,jdbcType=TIMESTAMP},
            #{item.signInTime,jdbcType=TIMESTAMP},
            #{item.departPlace,jdbcType=VARCHAR},
            #{item.arrivalPlace,jdbcType=VARCHAR},
            #{item.delayDuration,jdbcType=VARCHAR},
            #{item.delayDurationUnit,jdbcType=VARCHAR},
            #{item.taskCode,jdbcType=VARCHAR},
            #{item.status,jdbcType=VARCHAR},
            <if test="item.archiveTime != null ">
                #{item.archiveTime,jdbcType=TIMESTAMP}
            </if>
            <if test="item.archiveTime == null ">
                now()
            </if>
        </foreach>
    </insert>

    <update id="updateBaggageDelay" parameterType="com.paic.ncbs.claim.model.dto.checkloss.BaggageDelayDTO">
        update CLMS_BAGGAGE_DELAY
        <trim prefix="set" suffixOverrides=",">
            <if test="updatedBy != null">UPDATED_BY=#{updatedBy},</if>
            UPDATED_DATE=now(),
            <if test="delayDuration != null">DELAY_DURATION=#{delayDuration},</if>
            <if test="delayDurationUnit != null">DELAY_DURATION_UNIT=#{delayDurationUnit},</if>
        </trim>
        WHERE REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        AND TASK_CODE = #{taskCode}
        AND IS_EFFECTIVE = 'Y'
    </update>
</mapper>