<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.PersonRescueMapper">
	
	<insert id="addPersonRescue">
		INSERT INTO CLMS_PERSON_RESCUE(
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			REPORT_NO,
			CASE_TIMES,
			RESCUE_TYPE,
			AMOUNT,
			TASK_ID,
			STATUS,
			archive_time
			)
		<foreach collection="personRescueList" index="index" item="item" open="(" close=")" separator="union all">	
			SELECT
				 #{item.createdBy},
				SYSDATE(),
				 #{item.updatedBy},
				SYSDATE(),
				 #{item.reportNo},
				 #{item.caseTimes},
				 #{item.rescueType},
				 #{item.amount},
				 #{item.taskId},
				 #{item.status},
				 <if test="item.archiveTime != null ">
				 #{item.archiveTime}
				</if>
				<if test="item.archiveTime == null ">
					 SYSDATE()
				 </if>	
			FROM DUAL
		</foreach>
	</insert>
	
	<delete id="removePersonRescue">
		DELETE CLMS_PERSON_RESCUE
		 WHERE REPORT_NO = #{reportNo}
	    AND CASE_TIMES = #{caseTimes}	
		<if test="taskId != null and taskId != '' ">
	    	AND TASK_ID = #{taskId}
	    </if>
	</delete>
	
	<select id="getPersonRescue" resultType="com.paic.ncbs.claim.model.dto.duty.PersonRescueDTO">
	  SELECT
        REPORT_NO reportNo,
        CASE_TIMES caseTimes,
          RESCUE_TYPE rescueType,
          (SELECT CP.VALUE_CHINESE_NAME FROM CLM_COMMON_PARAMETER CP
           WHERE CP.COLLECTION_CODE ='AHCS_DETAIL_ELEMENT2' AND CP.VALUE_CODE = RESCUE_TYPE) rescueTypeName,
          AMOUNT amount,
          TASK_ID taskId,
          STATUS status,
		  ARCHIVE_TIME archiveTime 
	  FROM CLMS_PERSON_RESCUE
	  WHERE REPORT_NO = #{reportNo}
	    AND CASE_TIMES = #{caseTimes}
	    <if test="status != null and status != '' ">
	    	AND STATUS = #{status}
	    </if>
		<if test="taskId != null and taskId != '' ">
	    	AND TASK_ID = #{taskId}
	    </if>
	</select>

    <!-- 新增救援信息 -->
	<insert id="addPersonRescueList">		
		INSERT INTO CLMS_PERSON_RESCUE(
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			REPORT_NO,
			CASE_TIMES,
			RESCUE_TYPE,
			AMOUNT,
			TASK_ID,
			STATUS,
			archive_time
			)
		<foreach collection="personRescueList" index="index" item="item" open="(" close=")" separator="union all">	
		  SELECT #{userId},
				 SYSDATE(),
				 #{userId},
				 SYSDATE(),
				 #{item.reportNo},
				 #{caseTimes},
				 #{item.rescueType},
				 #{item.amount},
				 #{item.taskId},
				 #{item.status},
				  <if test="item.archiveTime != null ">
				 #{item.archiveTime}
				</if>
				<if test="item.archiveTime == null ">
					 SYSDATE()
				 </if>	
			FROM DUAL
		</foreach>
    </insert>
</mapper>