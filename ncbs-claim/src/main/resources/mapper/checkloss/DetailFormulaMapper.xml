<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.DetailFormulaMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.other.DetailFormulaDTO" id="DetailFormulaMap">
        <id property="idAhcsDetailFormula" column="ID_ahcs_detail_formula"/>
        <result property="createdBy" column="CREATED_BY"/>
        <result property="createdDate" column="CREATED_DATE"/>
        <result property="updatedBy" column="UPDATED_BY"/>
        <result property="updatedDate" column="UPDATED_DATE"/>
        <result property="policyNo" column="POLICY_NO"/>
        <result property="isUseStandard" column="is_Use_Standard"/>
        <result property="subjectId" column="SUBJECT_ID"/>
        <result property="planCode" column="PLAN_CODE"/>
        <result property="dutyCode" column="DUTY_CODE"/>
        <result property="dutyDetailCode" column="DUTY_DETAIL_CODE"/>
        <result property="formulaGroupCode" column="FORMULA_GROUP_CODE"/>
    </resultMap>

    <insert id="addDetailFormula" parameterType="com.paic.ncbs.claim.model.dto.other.DetailFormulaDTO">
        INSERT INTO CLMS_detail_formula (
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_ahcs_detail_formula,
        POLICY_NO,
        is_Use_Standard,
        SUBJECT_ID,
        PLAN_CODE,
        DUTY_CODE,
        DUTY_DETAIL_CODE,
        FORMULA_GROUP_CODE
        )
        VALUES (
        #{createdBy ,jdbcType=VARCHAR},
        sysdate(),
        #{updatedBy ,jdbcType=VARCHAR},
        sysdate(),
        #{idAhcsDetailFormula ,jdbcType=VARCHAR},
        ifnull(#{policyNo,jdbcType=VARCHAR}, '9999999999999999999'),
        #{isUseStandard ,jdbcType=VARCHAR},
        #{subjectId ,jdbcType=VARCHAR},
        #{planCode ,jdbcType=VARCHAR},
        #{dutyCode ,jdbcType=VARCHAR},
        #{dutyDetailCode ,jdbcType=VARCHAR},
        #{formulaGroupCode ,jdbcType=VARCHAR}
        )
    </insert>

    <insert id="addDetailFormulaList" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
            INSERT INTO CLMS_detail_formula (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_ahcs_detail_formula,
            POLICY_NO,
            is_Use_Standard,
            SUBJECT_ID,
            PLAN_CODE,
            DUTY_CODE,
            DUTY_DETAIL_CODE,
            FORMULA_GROUP_CODE
            )
            VALUES (
            #{item.createdBy ,jdbcType=VARCHAR},
            sysdate(),
            #{item.updatedBy ,jdbcType=VARCHAR},
            sysdate(),
            #{item.idAhcsDetailFormula ,jdbcType=VARCHAR},
            ifnull(#{item.policyNo,jdbcType=VARCHAR}, '9999999999999999999'),
            #{item.isUseStandard ,jdbcType=VARCHAR},
            #{item.subjectId ,jdbcType=VARCHAR},
            #{item.planCode ,jdbcType=VARCHAR},
            #{item.dutyCode ,jdbcType=VARCHAR},
            #{item.dutyDetailCode ,jdbcType=VARCHAR},
            #{item.formulaGroupCode ,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <update id="modifyDetailFormula" parameterType="com.paic.ncbs.claim.model.dto.other.DetailFormulaDTO">
        UPDATE CLMS_detail_formula
        <set>

            <if test="createdBy != null and createdBy != '' ">
                CREATED_BY = #{createdBy},
            </if>

            <if test="createdDate != null ">
                CREATED_DATE = #{createdDate},
            </if>

            <if test="updatedBy != null and updatedBy != '' ">
                UPDATED_BY = #{updatedBy},
            </if>
            UPDATED_DATE=sysdate(),

            <if test="policyNo != null and policyNo != '' ">
                POLICY_NO = #{policyNo},
            </if>

            <if test="isUseStandard != null and isUseStandard != '' ">
                is_Use_Standard = #{isUseStandard},
            </if>

            <if test="subjectId != null and subjectId != '' ">
                SUBJECT_ID = #{subjectId},
            </if>

            <if test="planCode != null and planCode != '' ">
                PLAN_CODE = #{planCode},
            </if>

            <if test="dutyCode != null and dutyCode != '' ">
                DUTY_CODE = #{dutyCode},
            </if>

            <if test="dutyDetailCode != null and dutyDetailCode != '' ">
                DUTY_DETAIL_CODE = #{dutyDetailCode},
            </if>

            <if test="formulaGroupCode != null and formulaGroupCode != '' ">
                FORMULA_GROUP_CODE = #{formulaGroupCode},
            </if>
        </set>
        WHERE ID_ahcs_detail_formula=#{idAhcsDetailFormula}
    </update>

    <delete id="removeDetailFormulaList" parameterType="com.paic.ncbs.claim.model.dto.other.DetailFormulaDTO">
        <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
            DELETE FROM CLMS_detail_formula
            where
            POLICY_NO = ifnull(#{item.policyNo,jdbcType=VARCHAR}, '9999999999999999999')
            and formula_group_code not like 'standard_%'
            <if test="item.subjectId != null and item.subjectId != '' ">
                AND SUBJECT_ID = #{item.subjectId,jdbcType=VARCHAR}
            </if>

            <if test="item.planCode != null and item.planCode != '' ">
                AND PLAN_CODE = #{item.planCode,jdbcType=VARCHAR}
            </if>
            <if test="item.dutyCode != null and item.dutyCode != '' ">
                AND DUTY_CODE = #{item.dutyCode,jdbcType=VARCHAR}
            </if>
            <if test="item.dutyDetailCode != null and item.dutyDetailCode != '' ">
                AND DUTY_DETAIL_CODE = #{item.dutyDetailCode,jdbcType=VARCHAR}
            </if>
        </foreach>
    </delete>

    <delete id="removeDetailFormulaListForPlan" parameterType="com.paic.ncbs.claim.model.dto.other.DetailFormulaDTO">
        <foreach collection="list" index="index" item="item" open="begin" separator=";" close=";end;">
            DELETE FROM CLMS_detail_formula
            where
            POLICY_NO = ifnull(#{item.policyNo,jdbcType=VARCHAR}, '9999999999999999999')
            and formula_group_code not like 'standard_%'
            <if test="item.subjectId != null and item.subjectId != '' ">
                AND SUBJECT_ID = #{item.subjectId,jdbcType=VARCHAR}
            </if>

            <if test="item.planCode != null and item.planCode != '' ">
                AND PLAN_CODE = #{item.planCode,jdbcType=VARCHAR}
            </if>
            <if test="item.dutyCode != null and item.dutyCode != '' ">
                AND DUTY_CODE = #{item.dutyCode,jdbcType=VARCHAR}
            </if>
            <if test="item.dutyDetailCode != null and item.dutyDetailCode != '' ">
                AND DUTY_DETAIL_CODE = #{item.dutyDetailCode,jdbcType=VARCHAR}
            </if>
        </foreach>
    </delete>

    <delete id="removeDetailFormulaByGroupCode">
        DELETE FROM CLMS_detail_formula
        where FORMULA_GROUP_CODE = #{formulaGroupCode}
    </delete>

    <select id="getDetailFormulaById" resultMap="DetailFormulaMap">
        select
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_ahcs_detail_formula,
        POLICY_NO,
        is_Use_Standard,
        SUBJECT_ID,
        PLAN_CODE,
        DUTY_CODE,
        DUTY_DETAIL_CODE,
        FORMULA_GROUP_CODE
        from CLMS_detail_formula
        where ID_ahcs_detail_formula=#{idAhcsDetailFormula}
    </select>

    <select id="getDetailFormula" resultMap="DetailFormulaMap">
        select
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_ahcs_detail_formula,
        POLICY_NO,
        is_Use_Standard,
        SUBJECT_ID,
        PLAN_CODE,
        DUTY_CODE,
        DUTY_DETAIL_CODE,
        FORMULA_GROUP_CODE
        from CLMS_detail_formula
        where
        PLAN_CODE = #{planCode,jdbcType=VARCHAR}
        AND DUTY_CODE = #{dutyCode,jdbcType=VARCHAR}
        <if test="policyNo != null and policyNo != '' ">
            AND POLICY_NO = #{policyNo,jdbcType=VARCHAR}
        </if>
        <if test="subjectId != null and subjectId != '' ">
            AND SUBJECT_ID = #{subjectId,jdbcType=VARCHAR}
        </if>
        <if test="dutyDetailCode != null and dutyDetailCode != '' ">
            AND DUTY_DETAIL_CODE = #{dutyDetailCode,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getDetailFormulaGroupCodeByDuty" resultType="String">
        select DISTINCT FORMULA_GROUP_CODE
        from CLMS_detail_formula
        where PLAN_CODE = #{planCode, jdbcType = VARCHAR}
        <if test="subjectId != null and subjectId != '' ">
            AND SUBJECT_ID = #{subjectId,jdbcType=VARCHAR}
        </if>
        AND DUTY_CODE = #{dutyCode, jdbcType = VARCHAR}
        AND POLICY_NO = #{policyNo, jdbcType = VARCHAR}
    </select>

    <select id="getDetailFormulaGroupCodeByDetail" resultMap="DetailFormulaMap">
        select
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_ahcs_detail_formula,
        POLICY_NO,
        is_Use_Standard,
        SUBJECT_ID,
        PLAN_CODE,
        DUTY_CODE,
        DUTY_DETAIL_CODE,
        FORMULA_GROUP_CODE
        from CLMS_detail_formula
        where PLAN_CODE = #{planCode, jdbcType = VARCHAR}
        <if test="subjectId != null and subjectId != '' ">
            AND SUBJECT_ID = #{subjectId,jdbcType=VARCHAR}
        </if>
        AND DUTY_CODE = #{dutyCode, jdbcType = VARCHAR}
        AND POLICY_NO = #{policyNo, jdbcType = VARCHAR}
        <if test="dutyDetailCode != null and dutyDetailCode != '' ">
            AND DUTY_DETAIL_CODE = #{dutyDetailCode,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getFormulaGroupByCode" resultType="String">
        select distinct FORMULA_GROUP_CODE
        from CLMS_detail_formula t
        where t.formula_group_code like 'FG_D%'
        AND DUTY_DETAIL_CODE in
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getHasSpecialTypeByReportNo" resultType="String">
        select t.formula_content_value
        from CLMS_settle_formula t
        where t.formula_group_code in
        (select a.formula_group_code
        from CLMS_detail_formula a
        where a.policy_no in
        (select t.policy_no
        from CLMS_policy_info t
        where t.report_no = #{reportNo,jdbcType=VARCHAR} ))
    </select>

</mapper>
