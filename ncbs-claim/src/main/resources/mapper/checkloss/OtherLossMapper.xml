<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.checkloss.OtherLossMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.duty.OtherLossDTO" id="otherLossListResult">
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess"/>
        <result column="ACCIDENT_OVERSEAS" property="accidentOverseas"/>
        <result column="ACCIDENT_PROVINCE_CODE" property="provinceCode"/>
        <result column="ACCIDENT_CONTINENT_CODE" property="accidentContinentCode"/>
        <result column="ACCIDENT_CITY_CODE" property="accidentCityCode"/>
        <result column="ACCIDENT_COUNTY_CODE" property="accidentCountyCode"/>
        <result column="ACCIDENT_PLACE" property="accidentPlace"/>
        <result column="ACCIDENT_REASON" property="accidentReason"/>
        <result column="LOSS_OBJ" property="lossObj"/>
        <result column="LOSS_AMOUNT" property="lossAmount"/>
        <result column="TASK_CODE" property="taskCode"/>
        <result column="STATUS" property="status"/>
        <result column="ARCHIVE_TIME" property="archiveTime"/>
        <result column="LOSS_DESCRIBE" property="lossDescribe"/>
        <result column="REPORT_LOSSES_AMOUNT" property="reportLossesAmount"/>
        <result column="INSURED_PROPORTION" property="insuredProportion"/>
        <result column="REMARK" property="remark"/>
        <result column="salvage_value" property="salvageValue" jdbcType="NUMERIC"/>
    </resultMap>

    <insert id="addOtherLoss">
        INSERT INTO CLMS_OTHER_LOSS
        (ID_AHCS_OTHER_LOSS,
        REPORT_NO,
        CASE_TIMES,
        LOSS_AMOUNT,
        REPORT_LOSSES_AMOUNT,
        LOSS_DESCRIBE,
        salvage_value,
        INSURED_PROPORTION,
        REMARK,
        ID_AHCS_CHANNEL_PROCESS,
        ACCIDENT_OVERSEAS,
        ACCIDENT_PROVINCE_CODE,
        ACCIDENT_CONTINENT_CODE,
        ACCIDENT_CITY_CODE,
        ACCIDENT_COUNTY_CODE,
        ACCIDENT_PLACE,
        ACCIDENT_REASON,
        LOSS_OBJ,
        TASK_CODE,
        STATUS,
        ARCHIVE_TIME,
        IS_EFFECTIVE,
        created_by,
        sys_ctime,
        updated_by,
        sys_utime,
        CREATED_DATE,
        UPDATED_DATE
        )
        VALUES
        (
        #{idAhcsOtherLossDTO},
        #{reportNo},
        #{caseTimes},
        #{lossAmount},
        #{reportLossesAmount},
        #{lossDescribe},
        #{salvageValue},
        #{insuredProportion},
        #{remark},
        #{idAhcsChannelProcess},
        #{accidentOverseas},
        #{provinceCode},
        #{accidentContinentCode},
        #{accidentCityCode},
        #{accidentCountyCode},
        #{accidentPlace},
        #{accidentReason},
        #{lossObj},
        #{taskCode},
        #{status},
        <if test="archiveTime != null ">
            #{archiveTime},
        </if>
        <if test="archiveTime == null ">
            SYSDATE(),
        </if>
        'Y',
        #{createdBy},
        SYSDATE(),
        #{updatedBy},
        SYSDATE(),
        SYSDATE(),
        SYSDATE())
    </insert>

    <delete id="removeOtherLoss">
        DELETE CLMS_OTHER_LOSS WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
        <if test="taskCode != null and taskCode != '' ">
            AND TASK_CODE = #{taskCode}
        </if>
        <if test=" channelProcessId != null and channelProcessId !='' ">
            AND ID_AHCS_CHANNEL_PROCESS = #{channelProcessId}
        </if>
    </delete>

    <update id="updateEffective" parameterType="com.paic.ncbs.claim.model.dto.duty.OtherLossDTO">
        UPDATE
        CLMS_OTHER_LOSS
        SET
        updated_by = #{updatedBy},
        sys_utime = SYSDATE(),
        IS_EFFECTIVE = 'N'
        WHERE
        REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        <if test="taskCode != null and taskCode != '' ">
            AND TASK_CODE = #{taskCode}
        </if>
        <if test=" idAhcsChannelProcess != null and idAhcsChannelProcess !='' ">
            AND ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        </if>
        AND IS_EFFECTIVE = 'Y'
    </update>

    <select id="getOtherLoss" resultType="com.paic.ncbs.claim.model.dto.duty.OtherLossDTO">
        SELECT REPORT_NO reportNo,
        CASE_TIMES caseTimes,
        ID_AHCS_CHANNEL_PROCESS idAhcsChannelProcess,
        ACCIDENT_OVERSEAS accidentOverseas,
        ACCIDENT_PROVINCE_CODE provinceCode,
        ACCIDENT_CONTINENT_CODE accidentContinentCode,
        ACCIDENT_CITY_CODE accidentCityCode,
        ACCIDENT_COUNTY_CODE accidentCountyCode,
        ACCIDENT_PLACE accidentPlace,
        ACCIDENT_REASON accidentReason,
        LOSS_OBJ lossObj,
        LOSS_AMOUNT lossAmount,
        TASK_CODE taskCode,
        STATUS status,
        LOSS_DESCRIBE lossDescribe,
        REPORT_LOSSES_AMOUNT reportLossesAmount,
        INSURED_PROPORTION insuredProportion,
        REMARK remark,
        salvage_value salvageValue,
        ARCHIVE_TIME archiveTime,
        created_by createdBy,
        sys_ctime createdDate,
        updated_by updatedBy,
        sys_utime updatedDate
        FROM CLMS_OTHER_LOSS
        WHERE REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        <if test=" taskCode != null and taskCode !='' ">
            AND TASK_CODE = #{taskCode}
        </if>
        AND IS_EFFECTIVE = 'Y'
        <if test=" channelProcessId != null and channelProcessId !='' ">
            AND ID_AHCS_CHANNEL_PROCESS = #{channelProcessId}
        </if>
    </select>

    <select id="getSettleOtherLoss" resultType="com.paic.ncbs.claim.model.vo.settle.SettleOtherLossVO">
        SELECT REPORT_NO reportNo,
        CASE_TIMES caseTimes,
        ID_AHCS_CHANNEL_PROCESS idAhcsChannelProcess,
        ACCIDENT_OVERSEAS accidentOverseas,
        ACCIDENT_PROVINCE_CODE provinceCode,
        ACCIDENT_CONTINENT_CODE accidentContinentCode,
        ACCIDENT_CITY_CODE accidentCityCode,
        ACCIDENT_COUNTY_CODE accidentCountyCode,
        ACCIDENT_PLACE accidentPlace,
        ACCIDENT_REASON accidentReason,
        LOSS_OBJ lossObj,
        LOSS_AMOUNT lossAmount,
        TASK_CODE taskCode,
        STATUS status,
        LOSS_DESCRIBE lossDescribe,
        REPORT_LOSSES_AMOUNT reportLossesAmount,
        INSURED_PROPORTION insuredProportion,
        REMARK remark,
        salvage_value salvageValue
        FROM CLMS_OTHER_LOSS
        WHERE REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        <if test=" taskCode != null and taskCode !='' ">
            AND TASK_CODE = #{taskCode}
        </if>
        <if test=" status != null and status !='' ">
            AND STATUS = #{status}
        </if>
        AND IS_EFFECTIVE = 'Y'
        <if test=" idAhcsChannelProcess != null and idAhcsChannelProcess !='' ">
            AND ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        </if>
        limit 1
    </select>


    <!-- 根据通道号、环节号获取 其他损失信息-->
    <select id="getOtherLossList" parameterType="string" resultMap="otherLossListResult">
        select REPORT_NO,
        ACCIDENT_OVERSEAS,
        ACCIDENT_PROVINCE_CODE,
        ACCIDENT_CONTINENT_CODE,
        ACCIDENT_CITY_CODE,
        ACCIDENT_COUNTY_CODE,
        ACCIDENT_PLACE,
        ACCIDENT_REASON,
        LOSS_OBJ,
        LOSS_AMOUNT,
        TASK_CODE,
        STATUS,
        ARCHIVE_TIME,
        LOSS_DESCRIBE,
        REPORT_LOSSES_AMOUNT,
        INSURED_PROPORTION,
        REMARK,
        salvage_value
        from CLMS_OTHER_LOSS ol
        where ol.ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        and ol.STATUS = '1'
        and ol.TASK_CODE = #{taskCode}
        AND ol.IS_EFFECTIVE = 'Y'
    </select>

    <!-- 新增多条 其他损失信息 -->
    <insert id="addOtherLossList">
        insert into CLMS_OTHER_LOSS
        (created_by,
        sys_ctime,
        updated_by,
        sys_utime,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        ACCIDENT_OVERSEAS,
        ACCIDENT_PROVINCE_CODE,
        ACCIDENT_CONTINENT_CODE,
        ACCIDENT_CITY_CODE,
        ACCIDENT_COUNTY_CODE,
        ACCIDENT_PLACE,
        ACCIDENT_REASON,
        LOSS_OBJ,
        LOSS_AMOUNT,
        TASK_CODE,
        STATUS,
        archive_time,
        Loss_DESCRIBE,
        REPORT_LOSSES_AMOUNT,
        INSURED_PROPORTION,
        REMARK,
        salvage_value)
        <foreach collection="otherLossList" index="index" item="item" open="(" close=")" separator="union all">
            select #{userId},
            SYSDATE(),
            #{userId},
            SYSDATE(),
            #{item.reportNo},
            #{caseTimes},
            #{channelProcessId},
            #{item.accidentOverseas},
            #{item.provinceCode},
            #{item.accidentContinentCode},
            #{item.accidentCityCode},
            #{item.accidentCountyCode},
            #{item.accidentPlace},
            #{item.accidentReason},
            #{item.lossObj},
            #{item.lossAmount},
            #{item.taskCode},
            #{item.status},
            #{item.lossDescribe},
            #{item.reportLossesAmount},
            #{item.insuredProportion},
            #{item.remark},
            #{item.salvageValue}
            <if test="item.archiveTime != null ">
                #{item.archiveTime}
            </if>
            <if test="item.archiveTime == null ">
                SYSDATE()
            </if>
            from DUAL
        </foreach>
    </insert>

    <update id="updateOtherLoss" parameterType="com.paic.ncbs.claim.model.dto.duty.OtherLossDTO">
        update CLMS_OTHER_LOSS
        <trim prefix="set" suffixOverrides=",">
            <if test="updatedBy != null">updated_by=#{updatedBy},</if>
            sys_utime=SYSDATE(),
            <if test="accidentReason != null">ACCIDENT_REASON=#{accidentReason},</if>
            <if test="lossObj != null">LOSS_OBJ=#{lossObj},</if>
            <if test="lossAmount != null">LOSS_AMOUNT=#{lossAmount},</if>
        </trim>
        WHERE REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        AND TASK_CODE = #{taskCode}
        AND IS_EFFECTIVE = 'Y'
    </update>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        INSERT INTO CLMS_OTHER_LOSS (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_AHCS_OTHER_LOSS,
            REPORT_NO,
            CASE_TIMES,
            REPORT_LOSSES_AMOUNT,
            LOSS_DESCRIBE,
            salvage_value,
            INSURED_PROPORTION,
            REMARK,
            ID_AHCS_CHANNEL_PROCESS,
            ACCIDENT_OVERSEAS,
            ACCIDENT_PROVINCE_CODE,
            ACCIDENT_CONTINENT_CODE,
            ACCIDENT_CITY_CODE,
            ACCIDENT_COUNTY_CODE,
            ACCIDENT_PLACE,
            ACCIDENT_REASON,
            LOSS_OBJ,
            LOSS_AMOUNT,
            TASK_CODE,
            STATUS,
            ARCHIVE_TIME,
            IS_EFFECTIVE,
            ID_AHCS_ADDITIONAL_SURVEY
        )
        SELECT
            #{userId},
            NOW(),
            #{userId},
            NOW(),
            MD5(UUID()),
            REPORT_NO,
            #{reopenCaseTimes},
            REPORT_LOSSES_AMOUNT,
            LOSS_DESCRIBE,
            salvage_value,
            INSURED_PROPORTION,
            REMARK,
            #{idClmChannelProcess},
            ACCIDENT_OVERSEAS,
            ACCIDENT_PROVINCE_CODE,
            ACCIDENT_CONTINENT_CODE,
            ACCIDENT_CITY_CODE,
            ACCIDENT_COUNTY_CODE,
            ACCIDENT_PLACE,
            ACCIDENT_REASON,
            LOSS_OBJ,
            LOSS_AMOUNT,
            TASK_CODE,
            STATUS,
            NOW(),
            IS_EFFECTIVE,
            ID_AHCS_ADDITIONAL_SURVEY
        FROM CLMS_OTHER_LOSS
        WHERE REPORT_NO=#{reportNo}
        AND CASE_TIMES=#{caseTimes}
        AND IS_EFFECTIVE='Y'
    </insert>

    <select id="getOtherLossListbyReportNo" resultMap="otherLossListResult">
        select REPORT_NO,
        CASE_TIMES,
        ACCIDENT_OVERSEAS,
        ACCIDENT_PROVINCE_CODE,
        ACCIDENT_CONTINENT_CODE,
        ACCIDENT_CITY_CODE,
        ACCIDENT_COUNTY_CODE,
        ACCIDENT_PLACE,
        ACCIDENT_REASON,
        LOSS_OBJ,
        LOSS_AMOUNT,
        TASK_CODE,
        STATUS,
        ARCHIVE_TIME,
        LOSS_DESCRIBE,
        REPORT_LOSSES_AMOUNT,
        INSURED_PROPORTION,
        REMARK,
        salvage_value
        from CLMS_OTHER_LOSS
        where REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        AND  IS_EFFECTIVE = 'Y'
    </select>
</mapper>