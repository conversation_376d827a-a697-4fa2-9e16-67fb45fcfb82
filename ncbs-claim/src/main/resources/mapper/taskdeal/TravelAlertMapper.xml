<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.taskdeal.TravelAlertMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.taskdeal.TravelAlertDTO" id="travelAlertListResult">
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess"/>
        <result column="ACCIDENT_OVERSEAS" property="accidentOverseas"/>
        <result column="ACCIDENT_PROVINCE_CODE" property="provinceCode"/>
        <result column="ACCIDENT_CITY_CODE" property="accidentCityCode"/>
        <result column="ACCIDENT_COUNTY_CODE" property="accidentCountyCode"/>
        <result column="ACCIDENT_CONTINENT_CODE" property="accidentContinentCode"/>
        <result column="ACCIDENT_PLACE" property="accidentPlace"/>
        <result column="ACCIDENT_TYPE" property="accidentType"/>
        <result column="LOSS_AMOUNT" property="lossAmount"/>
        <result column="ALTER_REASON" property="alterReason"/>
        <result column="TASK_CODE" property="taskCode"/>
        <result column="STATUS" property="status"/>
        <result column="D_ACCIDENT_OVERSEAS" property="dAccidentOverseas"/>
        <result column="DEPART_PLACE_PROVINCE_CODE" property="departPlaceProvinceCode"/>
        <result column="DEPART_PLACE_CITY_CODE" property="departPlaceCityCode"/>
        <result column="DEPART_PLACE_COUNTY_CODE" property="departPlaceCountyCode"/>
        <result column="DEPART_PLACE_PLACE" property="departPlacePlace"/>
        <result column="D_ACCIDENT_CONTINENT_CODE" property="dAccidentContinentCode"/>
        <result column="D_ACCIDENT_CONTINENT_PLACE" property="dAccidentContinentPlace"/>
        <result column="A_ACCIDENT_OVERSEAS" property="aAccidentOverseas"/>
        <result column="ARRIVAL_PLACE_PROVINCE_CODE" property="arrivalPlaceProvinceCode"/>
        <result column="ARRIVAL_PLACE_CITY_CODE" property="arrivalPlaceCityCode"/>
        <result column="ARRIVAL_PLACE_COUNTY_CODE" property="arrivalPlaceCountyCode"/>
        <result column="ARRIVAL_PLACE_PLACE" property="arrivalPlacePlace"/>
        <result column="A_ACCIDENT_CONTINENT_CODE" property="aAccidentContinentCode"/>
        <result column="A_ACCIDENT_CONTINENT_PLACE" property="aAccidentContinentPlace"/>
        <result column="ORDER_NO" property="orderNo"/>
        <result column="ACCIDENT_REASON" property="accidentReason"/>
        <result column="ARCHIVE_TIME" property="archiveTime"/>
    </resultMap>

    <insert id="addTravelAlert">
        INSERT INTO CLMS_TRAVEL_ALTER(
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        ACCIDENT_OVERSEAS,
        ACCIDENT_PROVINCE_CODE,
        ACCIDENT_CITY_CODE,
        ACCIDENT_COUNTY_CODE,
        ACCIDENT_CONTINENT_CODE,
        ACCIDENT_PLACE,
        ACCIDENT_TYPE,
        LOSS_AMOUNT,
        ALTER_REASON,
        TASK_CODE,
        STATUS,
        D_ACCIDENT_OVERSEAS,
        DEPART_PLACE_PROVINCE_CODE,
        DEPART_PLACE_CITY_CODE,
        DEPART_PLACE_COUNTY_CODE,
        DEPART_PLACE_PLACE,
        D_ACCIDENT_CONTINENT_CODE,
        D_ACCIDENT_CONTINENT_PLACE,
        A_ACCIDENT_OVERSEAS,
        ARRIVAL_PLACE_PROVINCE_CODE,
        ARRIVAL_PLACE_CITY_CODE,
        ARRIVAL_PLACE_COUNTY_CODE,
        ARRIVAL_PLACE_PLACE,
        A_ACCIDENT_CONTINENT_CODE,
        A_ACCIDENT_CONTINENT_PLACE,
        ORDER_NO,
        ACCIDENT_REASON,
        ID_AHCS_TRAVEL_ALTER,
        ARCHIVE_TIME)
        <foreach collection="travelAlertList" index="index" item="item" open="(" close=")" separator="union all">
            SELECT
            #{item.createdBy,jdbcType=VARCHAR},
            NOW(),
            #{item.updatedBy,jdbcType=VARCHAR},
            NOW(),
            #{item.reportNo,jdbcType=VARCHAR},
            #{item.caseTimes,jdbcType=NUMERIC},
            #{item.idAhcsChannelProcess,jdbcType=VARCHAR},
            #{item.accidentOverseas,jdbcType=VARCHAR},
            #{item.provinceCode,jdbcType=VARCHAR},
            #{item.accidentCityCode,jdbcType=VARCHAR},
            #{item.accidentCountyCode,jdbcType=VARCHAR},
            #{item.accidentContinentCode,jdbcType=VARCHAR},
            #{item.accidentPlace,jdbcType=VARCHAR},
            #{item.accidentType,jdbcType=VARCHAR},
            #{item.lossAmount,jdbcType=NUMERIC},
            #{item.alterReason,jdbcType=VARCHAR},
            #{item.taskCode,jdbcType=VARCHAR},
            #{item.status,jdbcType=VARCHAR},
            #{item.dAccidentOverseas,jdbcType=VARCHAR},
            #{item.departPlaceProvinceCode,jdbcType=VARCHAR},
            #{item.departPlaceCityCode,jdbcType=VARCHAR},
            #{item.departPlaceCountyCode,jdbcType=VARCHAR},
            #{item.departPlacePlace,jdbcType=VARCHAR},
            #{item.dAccidentContinentCode,jdbcType=VARCHAR},
            #{item.dAccidentContinentPlace,jdbcType=VARCHAR},
            #{item.aAccidentOverseas,jdbcType=VARCHAR},
            #{item.arrivalPlaceProvinceCode,jdbcType=VARCHAR},
            #{item.arrivalPlaceCityCode,jdbcType=VARCHAR},
            #{item.arrivalPlaceCountyCode,jdbcType=VARCHAR},
            #{item.arrivalPlacePlace,jdbcType=VARCHAR},
            #{item.aAccidentContinentCode,jdbcType=VARCHAR},
            #{item.aAccidentContinentPlace,jdbcType=VARCHAR},
            #{item.orderNo,jdbcType=VARCHAR},
            #{item.accidentReason,jdbcType=VARCHAR},
            left(hex(uuid()),32),
            <if test="item.archiveTime != null ">
                #{item.archiveTime,jdbcType=TIMESTAMP}
            </if>
            <if test="item.archiveTime == null ">
                NOW()
            </if>
            FROM DUAL
        </foreach>
    </insert>

    <delete id="removeTravelAlert">
        DELETE FROM  CLMS_TRAVEL_ALTER WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
        <if test="taskCode != null and taskCode != '' ">
            AND TASK_CODE = #{taskCode}
        </if>
        <if test="channelProcessId != null and channelProcessId != '' ">
            AND ID_AHCS_CHANNEL_PROCESS = #{channelProcessId}
        </if>
    </delete>

    <select id="getTravelAlert" resultType="com.paic.ncbs.claim.model.dto.taskdeal.TravelAlertDTO">
        SELECT REPORT_NO reportNo,
        CASE_TIMES caseTimes,
        ID_AHCS_CHANNEL_PROCESS idAhcsChannelProcess,
        ACCIDENT_OVERSEAS accidentOverseas,
        ACCIDENT_PROVINCE_CODE provinceCode,
        ACCIDENT_CITY_CODE accidentCityCode,
        ACCIDENT_COUNTY_CODE accidentCountyCode,
        ACCIDENT_CONTINENT_CODE accidentContinentCode,
        ACCIDENT_PLACE accidentPlace,
        ACCIDENT_TYPE accidentType,
        LOSS_AMOUNT lossAmount,
        ALTER_REASON alterReason,
        (SELECT CP.VALUE_CHINESE_NAME
        FROM CLM_COMMON_PARAMETER CP
        WHERE CP.COLLECTION_CODE = 'AHCS_LOSS_RESON'
        AND CP.VALUE_CODE = ALTER_REASON) alterReasonName,
        (SELECT CP.VALUE_CHINESE_NAME
        FROM CLM_COMMON_PARAMETER CP
        WHERE CP.COLLECTION_CODE = 'AHCS_TRAVEL_ACC_TYPE'
        AND CP.VALUE_CODE = ACCIDENT_TYPE) accidentTypeName,
        TASK_CODE taskCode,
        STATUS status,
        D_ACCIDENT_OVERSEAS dAccidentOverseas,
        DEPART_PLACE_PROVINCE_CODE departPlaceProvinceCode,
        DEPART_PLACE_CITY_CODE departPlaceCityCode,
        DEPART_PLACE_COUNTY_CODE departPlaceCountyCode,
        DEPART_PLACE_PLACE departPlacePlace,
        D_ACCIDENT_CONTINENT_CODE dAccidentContinentCode,
        D_ACCIDENT_CONTINENT_PLACE dAccidentContinentPlace,
        A_ACCIDENT_OVERSEAS aAccidentOverseas,
        ARRIVAL_PLACE_PROVINCE_CODE arrivalPlaceProvinceCode,
        ARRIVAL_PLACE_CITY_CODE arrivalPlaceCityCode,
        ARRIVAL_PLACE_COUNTY_CODE arrivalPlaceCountyCode,
        ARRIVAL_PLACE_PLACE arrivalPlacePlace,
        A_ACCIDENT_CONTINENT_CODE aAccidentContinentCode,
        A_ACCIDENT_CONTINENT_PLACE aAccidentContinentPlace,
        ORDER_NO orderNo,
        ACCIDENT_REASON accidentReason
        FROM CLMS_TRAVEL_ALTER
        WHERE REPORT_NO = #{reportNo} AND CASE_TIMES = #{caseTimes}
        <if test="status != null and status != '' ">
            AND STATUS = #{status}
        </if>
        <if test="taskCode != null and taskCode != '' ">
            AND TASK_CODE = #{taskCode}
        </if>
        <if test="channelProcessId != null and channelProcessId != '' ">
            AND ID_AHCS_CHANNEL_PROCESS = #{channelProcessId}
        </if>
    </select>

    <!-- 根据通道号、环节号获取 旅行变更信息-->
    <select id="getTravelAlertList" parameterType="string" resultMap="travelAlertListResult">
        select REPORT_NO,
        ACCIDENT_OVERSEAS,
        ACCIDENT_PROVINCE_CODE,
        ACCIDENT_CITY_CODE,
        ACCIDENT_COUNTY_CODE,
        ACCIDENT_CONTINENT_CODE,
        ACCIDENT_PLACE,
        ACCIDENT_TYPE,
        LOSS_AMOUNT,
        ALTER_REASON,
        TASK_CODE,
        STATUS,
        D_ACCIDENT_OVERSEAS,
        DEPART_PLACE_PROVINCE_CODE,
        DEPART_PLACE_CITY_CODE,
        DEPART_PLACE_COUNTY_CODE,
        DEPART_PLACE_PLACE,
        D_ACCIDENT_CONTINENT_CODE,
        D_ACCIDENT_CONTINENT_PLACE,
        A_ACCIDENT_OVERSEAS,
        ARRIVAL_PLACE_PROVINCE_CODE,
        ARRIVAL_PLACE_CITY_CODE,
        ARRIVAL_PLACE_COUNTY_CODE,
        ARRIVAL_PLACE_PLACE,
        A_ACCIDENT_CONTINENT_CODE,
        A_ACCIDENT_CONTINENT_PLACE,
        ORDER_NO,
        ACCIDENT_REASON,
        ARCHIVE_TIME
        from CLMS_TRAVEL_ALTER ta
        where ta.ID_AHCS_CHANNEL_PROCESS = #{idAhcsChannelProcess}
        and ta.STATUS = '1'
        and ta.TASK_CODE = #{taskCode}
    </select>

    <!-- 新增多条  旅行变更信息 -->
    <insert id="addTravelAlertList">
        insert into CLMS_TRAVEL_ALTER
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        ACCIDENT_OVERSEAS,
        ACCIDENT_PROVINCE_CODE,
        ACCIDENT_CITY_CODE,
        ACCIDENT_COUNTY_CODE,
        ACCIDENT_CONTINENT_CODE,
        ACCIDENT_PLACE,
        ACCIDENT_TYPE,
        LOSS_AMOUNT,
        ALTER_REASON,
        TASK_CODE,
        STATUS,
        D_ACCIDENT_OVERSEAS,
        DEPART_PLACE_PROVINCE_CODE,
        DEPART_PLACE_CITY_CODE,
        DEPART_PLACE_COUNTY_CODE,
        DEPART_PLACE_PLACE,
        D_ACCIDENT_CONTINENT_CODE,
        D_ACCIDENT_CONTINENT_PLACE,
        A_ACCIDENT_OVERSEAS,
        ARRIVAL_PLACE_PROVINCE_CODE,
        ARRIVAL_PLACE_CITY_CODE,
        ARRIVAL_PLACE_COUNTY_CODE,
        ARRIVAL_PLACE_PLACE,
        A_ACCIDENT_CONTINENT_CODE,
        A_ACCIDENT_CONTINENT_PLACE,
        ORDER_NO,
        ACCIDENT_REASON,
        ARCHIVE_TIME)
        <foreach collection="travelAlertList" index="index" item="item" open="(" close=")" separator="union all">
            select #{userId},
            NOW(),
            #{userId},
            NOW(),
            #{item.reportNo},
            #{caseTimes},
            #{channelProcessId,jdbcType=VARCHAR},
            #{item.accidentOverseas,jdbcType=VARCHAR},
            #{item.provinceCode,jdbcType=VARCHAR},
            #{item.accidentCityCode,jdbcType=VARCHAR},
            #{item.accidentCountyCode,jdbcType=VARCHAR},
            #{item.accidentContinentCode,jdbcType=VARCHAR},
            #{item.accidentPlace,jdbcType=VARCHAR},
            #{item.accidentType,jdbcType=VARCHAR},
            #{item.lossAmount,jdbcType=NUMERIC},
            #{item.alterReason,jdbcType=VARCHAR},
            #{item.taskCode,jdbcType=VARCHAR},
            #{item.status,jdbcType=VARCHAR} ,
            #{item.dAccidentOverseas,jdbcType=VARCHAR},
            #{item.departPlaceProvinceCode,jdbcType=VARCHAR},
            #{item.departPlaceCityCode,jdbcType=VARCHAR},
            #{item.departPlaceCountyCode,jdbcType=VARCHAR},
            #{item.departPlacePlace,jdbcType=VARCHAR},
            #{item.dAccidentContinentCode,jdbcType=VARCHAR},
            #{item.dAccidentContinentPlace,jdbcType=VARCHAR},
            #{item.aAccidentOverseas,jdbcType=VARCHAR},
            #{item.arrivalPlaceProvinceCode,jdbcType=VARCHAR},
            #{item.arrivalPlaceCityCode,jdbcType=VARCHAR},
            #{item.arrivalPlaceCountyCode,jdbcType=VARCHAR},
            #{item.arrivalPlacePlace,jdbcType=VARCHAR},
            #{item.aAccidentContinentCode,jdbcType=VARCHAR},
            #{item.aAccidentContinentPlace,jdbcType=VARCHAR},
            #{item.orderNo,jdbcType=VARCHAR},
            #{item.accidentReason,jdbcType=VARCHAR},
            <if test="item.archiveTime != null ">
                #{item.archiveTime,jdbcType=TIMESTAMP}
            </if>
            <if test="item.archiveTime == null ">
                NOW()
            </if>
            from DUAL
        </foreach>
    </insert>

    <update id="updateTravelAlert" parameterType="com.paic.ncbs.claim.model.dto.taskdeal.TravelAlertDTO">
        update CLMS_TRAVEL_ALTER
        <trim prefix="set" suffixOverrides=",">
            <if test="updatedBy != null">UPDATED_BY=#{updatedBy},</if>
            UPDATED_DATE=NOW(),
            <if test="accidentType != null">ACCIDENT_TYPE=#{accidentType},</if>
            <if test="lossAmount != null">LOSS_AMOUNT=#{lossAmount},</if>
            <if test="alterReason != null">ALTER_REASON=#{alterReason},</if>
        </trim>
        WHERE REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        AND TASK_CODE = #{taskCode}
    </update>

</mapper>