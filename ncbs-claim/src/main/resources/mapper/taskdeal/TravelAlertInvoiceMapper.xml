<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.taskdeal.TravelAlertInvoiceMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.taskdeal.TravelAlertInvoiceDTO" id="TravelAlertInvoiceDTOResult">
        <result column="ID_AHCS_TRAVEL_ALERT_INVOICE" property="idAhcsTravelAlertInvoice"/>
        <result column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="INVOICE_TYPE" property="invoiceType"/>
        <result column="INVOICE_NUMBER" property="invoiceNumber"/>
        <result column="INVOICE_AMOUNT" property="invoiceAmount"/>
        <result column="IS_EFFECTIVE" property="isEffective"/>
    </resultMap>

    <select id="getTravelAlertInvoiceList" resultMap="TravelAlertInvoiceDTOResult">
        SELECT ID_AHCS_TRAVEL_ALERT_INVOICE,
        ID_AHCS_CHANNEL_PROCESS,
        REPORT_NO,
        CASE_TIMES,
        INVOICE_TYPE,
        INVOICE_NUMBER,
        INVOICE_AMOUNT,
        IS_EFFECTIVE
        FROM CLMS_TRAVEL_ALERT_INVOICE T
        WHERE
        T.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
    </select>

    <delete id="deleteTravelAlertInvoice">
        DELETE FROM CLMS_TRAVEL_ALERT_INVOICE T WHERE T.ID_AHCS_TRAVEL_ALERT_INVOICE =
        #{idAhcsTravelAlertInvoice,jdbcType=VARCHAR}
    </delete>

    <delete id="deleTetravelAlertInvoiceForReportNo">
        DELETE FROM CLMS_TRAVEL_ALERT_INVOICE T WHERE T.REPORT_NO = #{reportNo,jdbcType=VARCHAR} AND
        T.ID_AHCS_CHANNEL_PROCESS=#{channelId,jdbcType=VARCHAR}
    </delete>

    <update id="updateTravelAlertInvoiceDTO"
            parameterType="com.paic.ncbs.claim.model.dto.taskdeal.TravelAlertInvoiceDTO">
        UPDATE CLMS_TRAVEL_ALERT_INVOICE T SET
        T.UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        <if test="invoiceType != null">
            T.INVOICE_TYPE = #{invoiceType,jdbcType=VARCHAR},
        </if>
        <if test="invoiceNumber != null">
            T.INVOICE_NUMBER = #{invoiceNumber,jdbcType=VARCHAR},
        </if>
        <if test="invoiceAmount != null">
            T.INVOICE_AMOUNT = #{invoiceAmount,jdbcType=NUMERIC},
        </if>
        T.UPDATED_DATE = NOW()
        WHERE
        T.ID_AHCS_TRAVEL_ALERT_INVOICE = #{idAhcsTravelAlertInvoice,jdbcType=VARCHAR}
    </update>

    <!-- 批量插入沟通明细信息 -->
    <insert id="saveTravelAlertInvoiceList" parameterType="java.util.List">
        INSERT INTO CLMS_TRAVEL_ALERT_INVOICE (
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_CHANNEL_PROCESS,
        REPORT_NO,
        CASE_TIMES,
        INVOICE_TYPE,
        INVOICE_NUMBER,
        INVOICE_AMOUNT,
        ARCHIVE_TIME,
        ID_AHCS_TRAVEL_ALERT_INVOICE)
        <foreach collection="travelAlertInvoiceList" item="travelAlertInvoice" index="index" separator=" union all ">
            SELECT
            #{travelAlertInvoice.createdBy,jdbcType=VARCHAR},
            NOW(),
            #{travelAlertInvoice.updatedBy,jdbcType=VARCHAR},
            NOW(),
            #{travelAlertInvoice.idAhcsChannelProcess,jdbcType=VARCHAR},
            #{travelAlertInvoice.reportNo,jdbcType=VARCHAR},
            #{travelAlertInvoice.caseTimes,jdbcType=NUMERIC},
            #{travelAlertInvoice.invoiceType,jdbcType=VARCHAR},
            #{travelAlertInvoice.invoiceNumber,jdbcType=VARCHAR},
            #{travelAlertInvoice.invoiceAmount,jdbcType=NUMERIC},
            NOW(),
			left(hex(uuid()),32)
        </foreach>
    </insert>

</mapper>