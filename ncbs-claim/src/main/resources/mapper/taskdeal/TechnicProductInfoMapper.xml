<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.taskdeal.TechnicProductInfoMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.taskdeal.TechnicProductInfoEntity">
        <id column="ID_TECHNIC_PRODUCT_INFO" property="idTechnicProductInfo" jdbcType="VARCHAR"/>
        <result column="TECHNIC_PRODUCT_CODE" property="technicProductCode" jdbcType="VARCHAR"/>
        <result column="TECHNIC_PRODUCT_NAME" property="technicProductName" jdbcType="VARCHAR"/>
        <result column="EFFECTIVE_DATE" property="effectiveDate" jdbcType="TIMESTAMP"/>
        <result column="INVALIDATE_DATE" property="invalidateDate" jdbcType="TIMESTAMP"/>
        <result column="STATUS" property="status" jdbcType="VARCHAR"/>
        <result column="PRODUCT_CLASS" property="productClass" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="TARGET_TYPE" property="targetType" jdbcType="VARCHAR"/>
        <result column="NORMAL_TYPE" property="normalType" jdbcType="VARCHAR"/>
        <result column="IS_ALL_PURPOSE_CARD" property="isAllPurposeCard" jdbcType="VARCHAR"/>
        <result column="IS_REMIT_UNDERWRITE" property="isRemitUnderwrite" jdbcType="VARCHAR"/>
        <result column="PRODUCT_SUBCLASS" property="productSubclass" jdbcType="VARCHAR"/>
        <result column="CLAIM_CLASS" property="claimClass" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getTechnicProductCode" resultType="java.lang.String" parameterType="java.lang.String">
        select distinct
        t.technic_product_code
        from
        base_marketproduct_info m,technic_product_info t
        where
        m.id_technic_product_info =t.id_technic_product_info
        and
        m.marketproduct_code = #{productCode,jdbcType=VARCHAR}
    </select>

</mapper>