<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.taskdeal.TaskConflictMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.taskdeal.ClmsTaskConflictDTO" id="taskConflicoList">
        <id column="id_clms_task_conflict" property="idClmsTaskConflict" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
        <result column="task_definition_bpm_key" property="taskDefinitionBpmKey" />
        <result column="plan_operation" property="planOperation" />
        <result column="conflict_task_key" property="conflictTaskKey" />
        <result column="conflict_task_status" property="conflictTaskStatus" />
        <result column="constraint_type" property="constraintType" />
        <result column="conflict_reason" property="conflictReason" />
    </resultMap>

    <select id="findByBpmKeyAndOpr" resultMap="taskConflicoList">

        select * from clms_task_conflict t
        where 1=1
        <if test="bpmKey != null">
            and t.task_definition_bpm_key = #{bpmKey}
        </if>
        <if test="planOperation != null ">
            and t.plan_operation = #{planOperation}
        </if>
    </select>
</mapper>
