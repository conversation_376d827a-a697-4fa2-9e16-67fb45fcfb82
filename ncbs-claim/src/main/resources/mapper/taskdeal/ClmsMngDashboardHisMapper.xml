<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.taskdeal.ClmsMngDashboardHisMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.clms.ClmsMngDashboardHis">
        <id column="id" property="id" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="sys_utime" property="sysUtime" />
        <result column="metrics_day" property="metricsDay" />
        <result column="company_code" property="companyCode" />
        <result column="project_code" property="projectCode" />
        <result column="project_name" property="projectName" />
        <result column="class_code" property="classCode" />
        <result column="class_name" property="className" />
        <result column="statistic_caliber" property="statisticCaliber" />
        <result column="report_peroid_numerator" property="reportPeroidNumerator" />
        <result column="report_peroid_denominator" property="reportPeroidDenominator" />
        <result column="estimate_peroid_numerator" property="estimatePeroidNumerator" />
        <result column="estimate_peroid_denominator" property="estimatePeroidDenominator" />
        <result column="endCase_peroid_numerator" property="endcasePeroidNumerator" />
        <result column="endCase_peroid_denominator" property="endcasePeroidDenominator" />
        <result column="reopen_times" property="reopenTimes" />
        <result column="end_case_numerator" property="endCaseNumerator" />
        <result column="end_case_denominator" property="endCaseDenominator" />
        <result column="estimate_deviation_amount" property="estimateDeviationAmount" />
        <result column="estimate_deviation_numerator" property="estimateDeviationNumerator" />
        <result column="estimate_deviation_denominator" property="estimateDeviationDenominator" />
        <result column="new_estimate" property="newEstimate" />
    </resultMap>
    <insert id="insertByDay">
        insert into clms_mng_dashboard_his (metrics_day, company_code, project_code, project_name, class_code, class_name, statistic_caliber, report_peroid_numerator, report_peroid_denominator, estimate_peroid_numerator, estimate_peroid_denominator, endCase_peroid_numerator, endCase_peroid_denominator, reopen_times, end_case_numerator, end_case_denominator, estimate_deviation_amount, estimate_deviation_numerator, estimate_deviation_denominator, new_estimate)
        select metrics_day, company_code, project_code, project_name, class_code, class_name, statistic_caliber, report_peroid_numerator, report_peroid_denominator, estimate_peroid_numerator, estimate_peroid_denominator, endCase_peroid_numerator, endCase_peroid_denominator, reopen_times, end_case_numerator, end_case_denominator, estimate_deviation_amount, estimate_deviation_numerator, estimate_deviation_denominator, new_estimate from clms_mng_dashboard_intf;
    </insert>
    <delete id="deleteByRepeat">
        delete from clms_mng_dashboard_his where id in(
        select id from(
        select id,count(*) as con
        from clms_mng_dashboard_his cmdh
        group by metrics_day, company_code, project_code, project_name, class_code, class_name, statistic_caliber,
        report_peroid_numerator, report_peroid_denominator, estimate_peroid_numerator, estimate_peroid_denominator,
        endCase_peroid_numerator, endCase_peroid_denominator, reopen_times, end_case_numerator, end_case_denominator,
        estimate_deviation_amount, estimate_deviation_numerator, estimate_deviation_denominator, new_estimate) as a
        where a.con>1)
    </delete>
    <select id="selectByTimeAndUser" resultType="com.paic.ncbs.claim.dao.entity.clms.ClmsMngDashboardHis"
    parameterType="com.paic.ncbs.claim.model.vo.dashboard.ManageDashBoardReqVo">
        select * from clms_mng_dashboard_his
        <where>
            <if test="projectCode!=null">
                and project_code = #{projectCode}
            </if>
            <if test="projectName!=null">
                and project_name = #{projectName}
            </if>
            <if test="companyCode!=null and companyCode!='0'.toString()">
                and company_code = #{companyCode}
            </if>
            <if test="dateTime!=null">
                and metrics_day = #{dateTime}
            </if>
            <if test="statisticCaliber!=null">
                and statistic_caliber = #{statisticCaliber}
            </if>
        </where>
    </select>

</mapper>
