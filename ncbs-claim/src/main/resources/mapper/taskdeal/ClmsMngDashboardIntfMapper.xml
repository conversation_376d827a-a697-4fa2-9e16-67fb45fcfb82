<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.taskdeal.ClmsMngDashboardIntfMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.clms.ClmsMngDashboardIntf">
        <id column="id" property="id" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="sys_utime" property="sysUtime" />
        <result column="metrics_day" property="metricsDay" />
        <result column="company_code" property="companyCode" />
        <result column="project_code" property="projectCode" />
        <result column="project_name" property="projectName" />
        <result column="class_code" property="classCode" />
        <result column="class_name" property="className" />
        <result column="statistic_caliber" property="statisticCaliber" />
        <result column="report_peroid_numerator" property="reportPeroidNumerator" />
        <result column="report_peroid_denominator" property="reportPeroidDenominator" />
        <result column="estimate_peroid_numerator" property="estimatePeroidNumerator" />
        <result column="estimate_peroid_denominator" property="estimatePeroidDenominator" />
        <result column="endCase_peroid_numerator" property="endcasePeroidNumerator" />
        <result column="endCase_peroid_denominator" property="endcasePeroidDenominator" />
        <result column="reopen_times" property="reopenTimes" />
        <result column="end_case_numerator" property="endCaseNumerator" />
        <result column="end_case_denominator" property="endCaseDenominator" />
        <result column="estimate_deviation_amount" property="estimateDeviationAmount" />
        <result column="estimate_deviation_numerator" property="estimateDeviationNumerator" />
        <result column="estimate_deviation_denominator" property="estimateDeviationDenominator" />
        <result column="new_estimate" property="newEstimate" />
    </resultMap>
    <delete id="deleteAll">
        delete from clms_mng_dashboard_intf where sys_ctime  <![CDATA[<]]> SYSDATE();
    </delete>
    <select id="selectByTimeAndUser" resultType="com.paic.ncbs.claim.dao.entity.clms.ClmsMngDashboardIntf"
     parameterType="com.paic.ncbs.claim.model.vo.dashboard.ManageDashBoardReqVo">
        select * from clms_mng_dashboard_intf
        <where>
            <if test="projectCode!=null">
                and project_code = #{projectCode}
            </if>
            <if test="projectName!=null">
                and project_name = #{projectName}
            </if>
            <if test="companyCode!=null and companyCode!='0'.toString()">
                and company_code = #{companyCode}
            </if>
            <if test="statisticCaliber!=null">
                statistic_caliber = #{statisticCaliber}
            </if>
        </where>
    </select>
    <select id="selectByData" resultType="com.paic.ncbs.claim.dao.entity.clms.ClmsMngDashboardHis"
    parameterType="com.paic.ncbs.claim.model.vo.dashboard.ManageDashBoardReqVo">
        select * from clms_mng_dashboard_intf
        <where>
            <if test="projectCode!=null">
                and project_code = #{projectCode}
            </if>
            <if test="projectName!=null">
                and project_name = #{projectName}
            </if>
            <if test="companyCode!=null and companyCode!='0'.toString()">
                and company_code = #{companyCode}
            </if>
            <if test="statisticCaliber!=null">
                and statistic_caliber = #{statisticCaliber}
            </if>
        </where>
    </select>

    <select id="selectByTimeToMounth" resultType="com.paic.ncbs.claim.model.vo.dashboard.ClmsMngDashboardHisVo"
            parameterType="com.paic.ncbs.claim.model.vo.dashboard.ManageDashBoardReqVo">
        SELECT id, sys_ctime, sys_utime, MONTH(metrics_day) as metrics_day, company_code, project_code, project_name,
        class_code, class_name, statistic_caliber,
        sum(report_peroid_numerator) as report_peroid_numerator,
        sum(report_peroid_denominator) as report_peroid_denominator,
        sum(estimate_peroid_numerator) as estimate_peroid_numerator,
        sum(estimate_peroid_denominator) as estimate_peroid_denominator,
        sum(endCase_peroid_numerator) as endCase_peroid_numerator,
        sum(endCase_peroid_denominator) as endCase_peroid_denominator,
        sum(reopen_times) as reopen_times,
        sum(end_case_numerator) as end_case_numerator,
        sum(end_case_denominator) as end_case_denominator,
        sum(estimate_deviation_amount) as estimate_deviation_amount,
        sum(estimate_deviation_numerator) as estimate_deviation_numerator,
        sum(estimate_deviation_denominator) as estimate_deviation_denominator,
        sum(new_estimate) as new_estimate,
        sum(end_case_denominator) as endCaseDenominatorCount
        FROM clms_mng_dashboard_intf
        where statistic_caliber in ('M-01','M-02','M-03','M-04','M-05','M-06','M-07','M-08','M-09','M-10','M-11','M-12')
<!--        AND metrics_day = DATE_FORMAT(DATE_SUB(NOW(),interval 1 day),'%Y%m%d')-->
        <if test="projectName!=null">
            and project_name = #{projectName}
        </if>
        <if test="companyCode!=null and companyCode!='0'.toString()">
            and company_code = #{companyCode}
        </if>
        <if test="projectCode!=null">
            and project_code = #{projectCode}
        </if>
        group by statistic_caliber;
    </select>

</mapper>
