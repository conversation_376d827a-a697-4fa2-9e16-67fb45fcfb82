<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.taskdeal.ThirdCarMapper">
	<resultMap type="com.paic.ncbs.claim.model.dto.taskdeal.ThirdCarDTO" id="result">
		<result column="ID_AHCS_PERSON_ACCIDENT" property="idAhcsPersonAccident" />
		<result column="THIRD_CAR_MARK" property="thirdCarMark" />
		<result column="THIRD_DRIVER_NAME" property="thirdDriverName" />
		<result column="THIRD_CERTIFICATE_NO" property="thirdCertificateNo" />
		<result column="ARCHIVE_TIME"  property="archiveTime"/>
	</resultMap>
	
	<delete id="removeThirdCarList" >
		delete from CLMS_THIRD_CAR where ID_AHCS_PERSON_ACCIDENT=#{idAhcsPersonAccident}
	</delete>
	
	<select id="getThirdCarList" parameterType="string" resultMap="result">
		select bdd.ID_AHCS_PERSON_ACCIDENT,
		       bdd.THIRD_CAR_MARK,
		       bdd.THIRD_DRIVER_NAME,
		       bdd.THIRD_CERTIFICATE_NO,
		       bdd.ARCHIVE_TIME 
		  from CLMS_THIRD_CAR bdd
		 where bdd.ID_AHCS_PERSON_ACCIDENT = #{idAhcsPersonAccident}
	</select>
	
	<!-- 新增多条 三者车辆信息-->
	<insert id="addThirdCarList">
	    insert into CLMS_THIRD_CAR
		  (CREATED_BY,
		   CREATED_DATE,
		   UPDATED_BY,
		   UPDATED_DATE,
		   ID_AHCS_PERSON_ACCIDENT,
		   THIRD_CAR_MARK,
		   THIRD_DRIVER_NAME,
		   THIRD_CERTIFICATE_NO,
		   archive_time)
		<foreach collection="thirdCarList" index="index" item="item" open="(" close=")" separator="union all">
    select #{userId},
		   NOW(),
		   #{userId},
		   NOW(),
		   #{idAhcsPersonAccident,jdbcType=VARCHAR},
		   #{item.thirdCarMark,jdbcType=VARCHAR},
		   #{item.thirdDriverName,jdbcType=VARCHAR} ,
		   #{item.thirdCertificateNo,jdbcType=VARCHAR} ,
		   <if test="item.archiveTime != null ">
				#{item.archiveTime,jdbcType=TIMESTAMP}
			</if>
			<if test="item.archiveTime == null ">
				 NOW()
			 </if> 
	  from DUAL
		</foreach>
	</insert> 
	
</mapper>