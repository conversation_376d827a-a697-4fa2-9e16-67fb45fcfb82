<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.product.ProductMapper">

	<select id="selectTaxRateByProductClass" parameterType="java.lang.String" resultType="java.math.BigDecimal">
		select
			mtc.tax_rate taxRate
		from
			marketproduct_tax_config mtc
		where
			mtc.produc_class_code = #{productClassCode}
	</select>

	<select id="getPlanTaxRateAndProductClass" parameterType="java.lang.String" resultType="java.util.HashMap">
		select IFNULL(m.tax_rate , 0) taxRate,
		       p.product_class productClass
		from plan_info p,
		     marketproduct_tax_config m
		where p.plan_code = #{planCode}
			and m.produc_class_code = p.product_class
			and p.status = '1'
		limit 0,1
	</select>

</mapper>