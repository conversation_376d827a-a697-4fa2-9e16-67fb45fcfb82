<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsMedicinePackageDetailMapper">

    <resultMap type="com.paic.ncbs.claim.dao.autosettle.ClmsMedicinePackageDetailDTO" id="ClmsMedicinePackageDetailMap">
        <id property="id" column="id" />
        <result property="packageId" column="package_id" />
        <result property="medicineName" column="medicine_name" />
        <result property="medicineCategory" column="medicine_category" />
        <result property="indication" column="indication" />
        <result property="validFlag" column="valid_flag" />
        <result property="remark" column="remark" />
        <result property="createdBy" column="created_by" />
        <result property="sysCtime" column="sys_ctime" />
        <result property="updatedBy" column="updated_by" />
        <result property="sysUtime" column="sys_utime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, package_id, medicine_name, medicine_category, indication, valid_flag, remark, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.io.Serializable" resultMap="ClmsMedicinePackageDetailMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_MEDICINE_PACKAGE_DETAIL
        WHERE id = #{id,jdbcType=INTEGER}
    </select>

    <select id="getList" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsMedicinePackageDetailDTO" resultMap="ClmsMedicinePackageDetailMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_MEDICINE_PACKAGE_DETAIL
        WHERE 1=1
        <if test="packageId != null">
            AND package_id = #{packageId,jdbcType=INTEGER}
        </if>
        <if test="medicineName != null and medicineName != ''">
            AND medicine_name LIKE CONCAT('%', #{medicineName}, '%')
        </if>
        <if test="medicineCategory != null and medicineCategory != ''">
            AND medicine_category = #{medicineCategory,jdbcType=VARCHAR}
        </if>
        <if test="validFlag != null">
            AND valid_flag = #{validFlag,jdbcType=BOOLEAN}
        </if>
        <if test="createdBy != null and createdBy != ''">
            AND created_by = #{createdBy,jdbcType=VARCHAR}
        </if>
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByPackageId" parameterType="java.lang.Integer" resultMap="ClmsMedicinePackageDetailMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_MEDICINE_PACKAGE_DETAIL
        WHERE package_id = #{packageId,jdbcType=INTEGER}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByMedicineName" parameterType="java.lang.String" resultMap="ClmsMedicinePackageDetailMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_MEDICINE_PACKAGE_DETAIL
        WHERE medicine_name LIKE CONCAT('%', #{medicineName}, '%')
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByMedicineCategory" parameterType="java.lang.String" resultMap="ClmsMedicinePackageDetailMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_MEDICINE_PACKAGE_DETAIL
        WHERE medicine_category = #{medicineCategory,jdbcType=VARCHAR}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByValidFlag" parameterType="java.lang.Boolean" resultMap="ClmsMedicinePackageDetailMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_MEDICINE_PACKAGE_DETAIL
        WHERE valid_flag = #{validFlag,jdbcType=BOOLEAN}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByPackageIdAndValidFlag" resultMap="ClmsMedicinePackageDetailMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_MEDICINE_PACKAGE_DETAIL
        WHERE package_id = #{packageId,jdbcType=INTEGER}
        AND valid_flag = #{validFlag,jdbcType=BOOLEAN}
        ORDER BY sys_ctime DESC
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.io.Serializable">
        DELETE FROM CLMS_MEDICINE_PACKAGE_DETAIL
        WHERE id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsMedicinePackageDetailDTO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO CLMS_MEDICINE_PACKAGE_DETAIL (
            package_id, medicine_name, medicine_category, indication, valid_flag, remark, 
            created_by, sys_ctime, updated_by, sys_utime
        ) VALUES (
            #{packageId,jdbcType=INTEGER}, #{medicineName,jdbcType=VARCHAR}, #{medicineCategory,jdbcType=VARCHAR}, 
            #{indication,jdbcType=VARCHAR}, #{validFlag,jdbcType=BOOLEAN}, #{remark,jdbcType=VARCHAR}, 
            #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, 
            #{sysUtime,jdbcType=TIMESTAMP}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsMedicinePackageDetailDTO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO CLMS_MEDICINE_PACKAGE_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="packageId != null">package_id,</if>
            <if test="medicineName != null">medicine_name,</if>
            <if test="medicineCategory != null">medicine_category,</if>
            <if test="indication != null">indication,</if>
            <if test="validFlag != null">valid_flag,</if>
            <if test="remark != null">remark,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="sysCtime != null">sys_ctime,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="sysUtime != null">sys_utime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="packageId != null">#{packageId,jdbcType=INTEGER},</if>
            <if test="medicineName != null">#{medicineName,jdbcType=VARCHAR},</if>
            <if test="medicineCategory != null">#{medicineCategory,jdbcType=VARCHAR},</if>
            <if test="indication != null">#{indication,jdbcType=VARCHAR},</if>
            <if test="validFlag != null">#{validFlag,jdbcType=BOOLEAN},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="createdBy != null">#{createdBy,jdbcType=VARCHAR},</if>
            <if test="sysCtime != null">#{sysCtime,jdbcType=TIMESTAMP},</if>
            <if test="updatedBy != null">#{updatedBy,jdbcType=VARCHAR},</if>
            <if test="sysUtime != null">#{sysUtime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsMedicinePackageDetailDTO">
        UPDATE CLMS_MEDICINE_PACKAGE_DETAIL
        <set>
            <if test="packageId != null">package_id = #{packageId,jdbcType=INTEGER},</if>
            <if test="medicineName != null">medicine_name = #{medicineName,jdbcType=VARCHAR},</if>
            <if test="medicineCategory != null">medicine_category = #{medicineCategory,jdbcType=VARCHAR},</if>
            <if test="indication != null">indication = #{indication,jdbcType=VARCHAR},</if>
            <if test="validFlag != null">valid_flag = #{validFlag,jdbcType=BOOLEAN},</if>
            <if test="remark != null">remark = #{remark,jdbcType=VARCHAR},</if>
            <if test="createdBy != null">created_by = #{createdBy,jdbcType=VARCHAR},</if>
            <if test="sysCtime != null">sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy,jdbcType=VARCHAR},</if>
            <if test="sysUtime != null">sys_utime = #{sysUtime,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsMedicinePackageDetailDTO">
        UPDATE CLMS_MEDICINE_PACKAGE_DETAIL
        SET package_id = #{packageId,jdbcType=INTEGER},
            medicine_name = #{medicineName,jdbcType=VARCHAR},
            medicine_category = #{medicineCategory,jdbcType=VARCHAR},
            indication = #{indication,jdbcType=VARCHAR},
            valid_flag = #{validFlag,jdbcType=BOOLEAN},
            remark = #{remark,jdbcType=VARCHAR},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

</mapper>
