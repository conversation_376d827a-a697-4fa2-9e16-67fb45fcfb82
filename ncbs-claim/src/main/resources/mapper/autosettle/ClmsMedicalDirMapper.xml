<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsMedicalDirMapper">

    <resultMap type="com.paic.ncbs.claim.dao.autosettle.ClmsMedicalDirDTO" id="ClmsMedicalDirMap">
        <id property="id" column="id" />
        <result property="dirName" column="dir_name" />
        <result property="parentId" column="parent_id" />
        <result property="dirLevel" column="dir_level" />
        <result property="validFlag" column="valid_flag" />
        <result property="createdBy" column="created_by" />
        <result property="sysCtime" column="sys_ctime" />
        <result property="updatedBy" column="updated_by" />
        <result property="sysUtime" column="sys_utime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, dir_name, parent_id, dir_level, valid_flag, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.io.Serializable" resultMap="ClmsMedicalDirMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_MEDICAL_DIR
        WHERE id = #{id,jdbcType=INTEGER}
    </select>

    <select id="getList" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsMedicalDirDTO" resultMap="ClmsMedicalDirMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_MEDICAL_DIR
        WHERE 1=1
        <if test="dirName != null and dirName != ''">
            AND dir_name LIKE CONCAT('%', #{dirName}, '%')
        </if>
        <if test="parentId != null">
            AND parent_id = #{parentId,jdbcType=INTEGER}
        </if>
        <if test="dirLevel != null">
            AND dir_level = #{dirLevel,jdbcType=TINYINT}
        </if>
        <if test="validFlag != null">
            AND valid_flag = #{validFlag,jdbcType=BOOLEAN}
        </if>
        <if test="createdBy != null and createdBy != ''">
            AND created_by = #{createdBy,jdbcType=VARCHAR}
        </if>
        ORDER BY dir_level ASC, sys_ctime DESC
    </select>

    <select id="selectByDirName" parameterType="java.lang.String" resultMap="ClmsMedicalDirMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_MEDICAL_DIR
        WHERE dir_name LIKE CONCAT('%', #{dirName}, '%')
        ORDER BY dir_level ASC, sys_ctime DESC
    </select>

    <select id="selectByParentId" parameterType="java.lang.Integer" resultMap="ClmsMedicalDirMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_MEDICAL_DIR
        WHERE parent_id = #{parentId,jdbcType=INTEGER}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByDirLevel" parameterType="java.lang.Integer" resultMap="ClmsMedicalDirMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_MEDICAL_DIR
        WHERE dir_level = #{dirLevel,jdbcType=TINYINT}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByValidFlag" parameterType="java.lang.Boolean" resultMap="ClmsMedicalDirMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_MEDICAL_DIR
        WHERE valid_flag = #{validFlag,jdbcType=BOOLEAN}
        ORDER BY dir_level ASC, sys_ctime DESC
    </select>

    <select id="selectRootDirs" resultMap="ClmsMedicalDirMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_MEDICAL_DIR
        WHERE parent_id IS NULL
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByCreateTimeRange" resultMap="ClmsMedicalDirMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_MEDICAL_DIR
        WHERE 1=1
        <if test="startTime != null and startTime != ''">
            AND sys_ctime >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND sys_ctime <= #{endTime}
        </if>
        ORDER BY dir_level ASC, sys_ctime DESC
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.io.Serializable">
        DELETE FROM CLMS_MEDICAL_DIR
        WHERE id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsMedicalDirDTO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO CLMS_MEDICAL_DIR (
            dir_name, parent_id, dir_level, valid_flag, created_by, sys_ctime, updated_by, sys_utime
        ) VALUES (
            #{dirName,jdbcType=VARCHAR}, #{parentId,jdbcType=INTEGER}, #{dirLevel,jdbcType=TINYINT}, 
            #{validFlag,jdbcType=BOOLEAN}, #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, 
            #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsMedicalDirDTO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO CLMS_MEDICAL_DIR
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dirName != null">dir_name,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="dirLevel != null">dir_level,</if>
            <if test="validFlag != null">valid_flag,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="sysCtime != null">sys_ctime,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="sysUtime != null">sys_utime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dirName != null">#{dirName,jdbcType=VARCHAR},</if>
            <if test="parentId != null">#{parentId,jdbcType=INTEGER},</if>
            <if test="dirLevel != null">#{dirLevel,jdbcType=TINYINT},</if>
            <if test="validFlag != null">#{validFlag,jdbcType=BOOLEAN},</if>
            <if test="createdBy != null">#{createdBy,jdbcType=VARCHAR},</if>
            <if test="sysCtime != null">#{sysCtime,jdbcType=TIMESTAMP},</if>
            <if test="updatedBy != null">#{updatedBy,jdbcType=VARCHAR},</if>
            <if test="sysUtime != null">#{sysUtime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsMedicalDirDTO">
        UPDATE CLMS_MEDICAL_DIR
        <set>
            <if test="dirName != null">dir_name = #{dirName,jdbcType=VARCHAR},</if>
            <if test="parentId != null">parent_id = #{parentId,jdbcType=INTEGER},</if>
            <if test="dirLevel != null">dir_level = #{dirLevel,jdbcType=TINYINT},</if>
            <if test="validFlag != null">valid_flag = #{validFlag,jdbcType=BOOLEAN},</if>
            <if test="createdBy != null">created_by = #{createdBy,jdbcType=VARCHAR},</if>
            <if test="sysCtime != null">sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy,jdbcType=VARCHAR},</if>
            <if test="sysUtime != null">sys_utime = #{sysUtime,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsMedicalDirDTO">
        UPDATE CLMS_MEDICAL_DIR
        SET dir_name = #{dirName,jdbcType=VARCHAR},
            parent_id = #{parentId,jdbcType=INTEGER},
            dir_level = #{dirLevel,jdbcType=TINYINT},
            valid_flag = #{validFlag,jdbcType=BOOLEAN},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

</mapper>
