<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsHospitalPackageMapper">

    <resultMap type="com.paic.ncbs.claim.dao.autosettle.ClmsHospitalPackageDTO" id="ClmsHospitalPackageMap">
        <id property="id" column="id" />
        <result property="packageName" column="package_name" />
        <result property="hostitals" column="hostitals" />
        <result property="validFlag" column="valid_flag" />
        <result property="createdBy" column="created_by" />
        <result property="sysCtime" column="sys_ctime" />
        <result property="updatedBy" column="updated_by" />
        <result property="sysUtime" column="sys_utime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, package_name, hostitals, valid_flag, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.io.Serializable" resultMap="ClmsHospitalPackageMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_HOSPITAL_PACKAGE
        WHERE id = #{id,jdbcType=INTEGER}
    </select>

    <select id="getList" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsHospitalPackageDTO" resultMap="ClmsHospitalPackageMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_HOSPITAL_PACKAGE
        WHERE 1=1
        <if test="packageName != null and packageName != ''">
            AND package_name LIKE CONCAT('%', #{packageName}, '%')
        </if>
        <if test="validFlag != null">
            AND valid_flag = #{validFlag,jdbcType=BOOLEAN}
        </if>
        <if test="createdBy != null and createdBy != ''">
            AND created_by = #{createdBy,jdbcType=VARCHAR}
        </if>
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByPackageName" parameterType="java.lang.String" resultMap="ClmsHospitalPackageMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_HOSPITAL_PACKAGE
        WHERE package_name = #{packageName,jdbcType=VARCHAR}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByValidFlag" parameterType="java.lang.Boolean" resultMap="ClmsHospitalPackageMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_HOSPITAL_PACKAGE
        WHERE valid_flag = #{validFlag,jdbcType=BOOLEAN}
        ORDER BY sys_ctime DESC
    </select>

    <select id="selectByCreateTimeRange" resultMap="ClmsHospitalPackageMap">
        SELECT 
        <include refid="Base_Column_List" />
        FROM CLMS_HOSPITAL_PACKAGE
        WHERE 1=1
        <if test="startTime != null and startTime != ''">
            AND sys_ctime >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND sys_ctime <= #{endTime}
        </if>
        ORDER BY sys_ctime DESC
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.io.Serializable">
        DELETE FROM CLMS_HOSPITAL_PACKAGE
        WHERE id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsHospitalPackageDTO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO CLMS_HOSPITAL_PACKAGE (
            package_name, hostitals, valid_flag, created_by, sys_ctime, updated_by, sys_utime
        ) VALUES (
            #{packageName,jdbcType=VARCHAR}, #{hostitals,jdbcType=VARCHAR}, #{validFlag,jdbcType=BOOLEAN}, 
            #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, 
            #{sysUtime,jdbcType=TIMESTAMP}
        )
    </insert>

    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsHospitalPackageDTO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO CLMS_HOSPITAL_PACKAGE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="packageName != null">package_name,</if>
            <if test="hostitals != null">hostitals,</if>
            <if test="validFlag != null">valid_flag,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="sysCtime != null">sys_ctime,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="sysUtime != null">sys_utime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="packageName != null">#{packageName,jdbcType=VARCHAR},</if>
            <if test="hostitals != null">#{hostitals,jdbcType=VARCHAR},</if>
            <if test="validFlag != null">#{validFlag,jdbcType=BOOLEAN},</if>
            <if test="createdBy != null">#{createdBy,jdbcType=VARCHAR},</if>
            <if test="sysCtime != null">#{sysCtime,jdbcType=TIMESTAMP},</if>
            <if test="updatedBy != null">#{updatedBy,jdbcType=VARCHAR},</if>
            <if test="sysUtime != null">#{sysUtime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsHospitalPackageDTO">
        UPDATE CLMS_HOSPITAL_PACKAGE
        <set>
            <if test="packageName != null">package_name = #{packageName,jdbcType=VARCHAR},</if>
            <if test="hostitals != null">hostitals = #{hostitals,jdbcType=VARCHAR},</if>
            <if test="validFlag != null">valid_flag = #{validFlag,jdbcType=BOOLEAN},</if>
            <if test="createdBy != null">created_by = #{createdBy,jdbcType=VARCHAR},</if>
            <if test="sysCtime != null">sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy,jdbcType=VARCHAR},</if>
            <if test="sysUtime != null">sys_utime = #{sysUtime,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsHospitalPackageDTO">
        UPDATE CLMS_HOSPITAL_PACKAGE
        SET package_name = #{packageName,jdbcType=VARCHAR},
            hostitals = #{hostitals,jdbcType=VARCHAR},
            valid_flag = #{validFlag,jdbcType=BOOLEAN},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

</mapper>
