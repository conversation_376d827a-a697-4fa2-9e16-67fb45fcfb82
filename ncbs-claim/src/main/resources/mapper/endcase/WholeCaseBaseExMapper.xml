<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseExMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseExEntity">
        <id column="ID_CLM_WHOLE_CASE_BASE_EX" property="idClmWholeCaseBaseEx" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="ID_CLM_WHOLE_CASE_BASE" property="idClmWholeCaseBase" jdbcType="VARCHAR"/>
        <result column="REPORT_NO" property="reportNo" jdbcType="VARCHAR"/>
        <result column="CASE_TIMES" property="caseTimes" jdbcType="DECIMAL"/>
        <result column="IS_INJURY_DIRECT_CASE" property="isInjuryDirectCase" jdbcType="VARCHAR"/>
        <result column="ONE_BUTTON_SERVICE_TYPE" property="oneButtonServiceType" jdbcType="VARCHAR"/>
        <result column="TRAFFIC_POLICE_TEAM" property="trafficPoliceTeam" jdbcType="VARCHAR"/>
        <result column="FIRST_TRAFFIC_POLICE" property="firstTrafficPolice" jdbcType="VARCHAR"/>
        <result column="SECOND_TRAFFIC_POLICE" property="secondTrafficPolice" jdbcType="VARCHAR"/>
        <result column="IS_SELF_HELP" property="isSelfHelp" jdbcType="VARCHAR"/>
        <result column="IS_LAWSUIT" property="isLawsuit" jdbcType="VARCHAR"/>
        <result column="COURT_CASE_NO" property="courtCaseNo" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID_CLM_WHOLE_CASE_BASE_EX, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ID_CLM_WHOLE_CASE_BASE,
        REPORT_NO, CASE_TIMES, IS_INJURY_DIRECT_CASE, ONE_BUTTON_SERVICE_TYPE, TRAFFIC_POLICE_TEAM,
        FIRST_TRAFFIC_POLICE, SECOND_TRAFFIC_POLICE, IS_SELF_HELP
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLM_WHOLE_CASE_BASE_EX
        where ID_CLM_WHOLE_CASE_BASE_EX = #{idClmWholeCaseBaseEx,jdbcType=VARCHAR}
    </select>
    <select id="selectByReportNo" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLM_WHOLE_CASE_BASE_EX
        where REPORT_NO = #{report,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLM_WHOLE_CASE_BASE_EX
        where ID_CLM_WHOLE_CASE_BASE_EX = #{idClmWholeCaseBaseEx,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseExEntity">
        insert into CLM_WHOLE_CASE_BASE_EX (ID_CLM_WHOLE_CASE_BASE_EX, CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        ID_CLM_WHOLE_CASE_BASE, REPORT_NO, CASE_TIMES,
        IS_INJURY_DIRECT_CASE, ONE_BUTTON_SERVICE_TYPE,
        TRAFFIC_POLICE_TEAM, FIRST_TRAFFIC_POLICE,
        SECOND_TRAFFIC_POLICE, IS_SELF_HELP,ARCHIVE_DATE)
        values (#{idClmWholeCaseBaseEx,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP},
        #{idClmWholeCaseBase,jdbcType=VARCHAR}, #{reportNo,jdbcType=VARCHAR}, #{caseTimes,jdbcType=DECIMAL},
        #{isInjuryDirectCase,jdbcType=VARCHAR}, #{oneButtonServiceType,jdbcType=VARCHAR},
        #{trafficPoliceTeam,jdbcType=VARCHAR}, #{firstTrafficPolice,jdbcType=VARCHAR},
        #{secondTrafficPolice,jdbcType=VARCHAR}, #{isSelfHelp,jdbcType=VARCHAR},now())
    </insert>
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseExEntity">
        insert into CLM_WHOLE_CASE_BASE_EX
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idClmWholeCaseBaseEx != null">
                ID_CLM_WHOLE_CASE_BASE_EX,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDate != null">
                CREATED_DATE,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE,
            </if>
            <if test="idClmWholeCaseBase != null">
                ID_CLM_WHOLE_CASE_BASE,
            </if>
            <if test="reportNo != null">
                REPORT_NO,
            </if>
            <if test="caseTimes != null">
                CASE_TIMES,
            </if>
            <if test="isInjuryDirectCase != null">
                IS_INJURY_DIRECT_CASE,
            </if>
            <if test="oneButtonServiceType != null">
                ONE_BUTTON_SERVICE_TYPE,
            </if>
            <if test="trafficPoliceTeam != null">
                TRAFFIC_POLICE_TEAM,
            </if>
            <if test="firstTrafficPolice != null">
                FIRST_TRAFFIC_POLICE,
            </if>
            <if test="secondTrafficPolice != null">
                SECOND_TRAFFIC_POLICE,
            </if>
            <if test="isSelfHelp != null">
                IS_SELF_HELP,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idClmWholeCaseBaseEx != null">
                #{idClmWholeCaseBaseEx,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idClmWholeCaseBase != null">
                #{idClmWholeCaseBase,jdbcType=VARCHAR},
            </if>
            <if test="reportNo != null">
                #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="caseTimes != null">
                #{caseTimes,jdbcType=DECIMAL},
            </if>
            <if test="isInjuryDirectCase != null">
                #{isInjuryDirectCase,jdbcType=VARCHAR},
            </if>
            <if test="oneButtonServiceType != null">
                #{oneButtonServiceType,jdbcType=VARCHAR},
            </if>
            <if test="trafficPoliceTeam != null">
                #{trafficPoliceTeam,jdbcType=VARCHAR},
            </if>
            <if test="firstTrafficPolice != null">
                #{firstTrafficPolice,jdbcType=VARCHAR},
            </if>
            <if test="secondTrafficPolice != null">
                #{secondTrafficPolice,jdbcType=VARCHAR},
            </if>
            <if test="isSelfHelp != null">
                #{isSelfHelp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseExEntity">
        update CLM_WHOLE_CASE_BASE_EX
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="idClmWholeCaseBase != null">
                ID_CLM_WHOLE_CASE_BASE = #{idClmWholeCaseBase,jdbcType=VARCHAR},
            </if>
            <if test="reportNo != null">
                REPORT_NO = #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="caseTimes != null">
                CASE_TIMES = #{caseTimes,jdbcType=DECIMAL},
            </if>
            <if test="isInjuryDirectCase != null">
                IS_INJURY_DIRECT_CASE = #{isInjuryDirectCase,jdbcType=VARCHAR},
            </if>
            <if test="oneButtonServiceType != null">
                ONE_BUTTON_SERVICE_TYPE = #{oneButtonServiceType,jdbcType=VARCHAR},
            </if>
            <if test="trafficPoliceTeam != null">
                TRAFFIC_POLICE_TEAM = #{trafficPoliceTeam,jdbcType=VARCHAR},
            </if>
            <if test="firstTrafficPolice != null">
                FIRST_TRAFFIC_POLICE = #{firstTrafficPolice,jdbcType=VARCHAR},
            </if>
            <if test="secondTrafficPolice != null">
                SECOND_TRAFFIC_POLICE = #{secondTrafficPolice,jdbcType=VARCHAR},
            </if>
            <if test="isSelfHelp != null">
                IS_SELF_HELP = #{isSelfHelp,jdbcType=VARCHAR},
            </if>
        </set>
        where ID_CLM_WHOLE_CASE_BASE_EX = #{idClmWholeCaseBaseEx,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseExEntity">
        update CLM_WHOLE_CASE_BASE_EX
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        ID_CLM_WHOLE_CASE_BASE = #{idClmWholeCaseBase,jdbcType=VARCHAR},
        REPORT_NO = #{reportNo,jdbcType=VARCHAR},
        CASE_TIMES = #{caseTimes,jdbcType=DECIMAL},
        IS_INJURY_DIRECT_CASE = #{isInjuryDirectCase,jdbcType=VARCHAR},
        ONE_BUTTON_SERVICE_TYPE = #{oneButtonServiceType,jdbcType=VARCHAR},
        TRAFFIC_POLICE_TEAM = #{trafficPoliceTeam,jdbcType=VARCHAR},
        FIRST_TRAFFIC_POLICE = #{firstTrafficPolice,jdbcType=VARCHAR},
        SECOND_TRAFFIC_POLICE = #{secondTrafficPolice,jdbcType=VARCHAR},
        IS_SELF_HELP = #{isSelfHelp,jdbcType=VARCHAR}
        where ID_CLM_WHOLE_CASE_BASE_EX = #{idClmWholeCaseBaseEx,jdbcType=VARCHAR}
    </update>

    <update id="updateCaseLawsuitStatus">
        update clm_whole_case_base_ex
        set
        <if test="isLawsuit != null">
            IS_LAWSUIT = #{isLawsuit},
        </if>
        <if test="courtCaseNo != null and courtCaseNo != ''">
            COURT_CASE_NO = #{courtCaseNo}
        </if>
        where REPORT_NO = #{reportNo}
        and CASE_TIMES = #{caseTimes}
    </update>


    <insert id="insertList" parameterType="java.util.List">
        insert into CLM_WHOLE_CASE_BASE_EX (ID_CLM_WHOLE_CASE_BASE_EX, CREATED_BY,
        CREATED_DATE, UPDATED_BY, UPDATED_DATE,
        ID_CLM_WHOLE_CASE_BASE, REPORT_NO, CASE_TIMES,
        IS_INJURY_DIRECT_CASE, ONE_BUTTON_SERVICE_TYPE,
        TRAFFIC_POLICE_TEAM, FIRST_TRAFFIC_POLICE,
        SECOND_TRAFFIC_POLICE, IS_SELF_HELP,ARCHIVE_DATE)
        values
        <foreach collection="list" separator="," index="index" item="item">
            (#{item.idClmWholeCaseBaseEx,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdDate,jdbcType=TIMESTAMP}, #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedDate,jdbcType=TIMESTAMP},
            #{item.idClmWholeCaseBase,jdbcType=VARCHAR}, #{item.reportNo,jdbcType=VARCHAR}, #{item.caseTimes,jdbcType=DECIMAL},
            #{item.isInjuryDirectCase,jdbcType=VARCHAR}, #{item.oneButtonServiceType,jdbcType=VARCHAR},
            #{item.trafficPoliceTeam,jdbcType=VARCHAR}, #{item.firstTrafficPolice,jdbcType=VARCHAR},
            #{item.secondTrafficPolice,jdbcType=VARCHAR}, #{item.isSelfHelp,jdbcType=VARCHAR},now())
        </foreach>
    </insert>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        INSERT INTO CLM_WHOLE_CASE_BASE_EX (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_CLM_WHOLE_CASE_BASE_EX,
            ID_CLM_WHOLE_CASE_BASE,
            REPORT_NO,
            CASE_TIMES,
            IS_INJURY_DIRECT_CASE,
            ONE_BUTTON_SERVICE_TYPE,
            TRAFFIC_POLICE_TEAM,
            FIRST_TRAFFIC_POLICE,
            SECOND_TRAFFIC_POLICE,
            IS_SELF_HELP,
            DOCUMENT_RECALL_STATUS,
            CLAIM_SERVICE_WAY,
            SPECIAL_CASE_TYPE,
            ARCHIVE_DATE,
            REPORT_FLAG,
            IS_FORCE_POLICY_QUICK_FINISH
        )
        SELECT
            #{userId},
            NOW(),
            #{userId},
            NOW(),
            MD5(UUID()),
            #{idClmWholeCaseBase},
            REPORT_NO,
            #{reopenCaseTimes},
            IS_INJURY_DIRECT_CASE,
            ONE_BUTTON_SERVICE_TYPE,
            TRAFFIC_POLICE_TEAM,
            FIRST_TRAFFIC_POLICE,
            SECOND_TRAFFIC_POLICE,
            IS_SELF_HELP,
            DOCUMENT_RECALL_STATUS,
            CLAIM_SERVICE_WAY,
            SPECIAL_CASE_TYPE,
            NOW(),
            REPORT_FLAG,
            IS_FORCE_POLICY_QUICK_FINISH
        FROM CLM_WHOLE_CASE_BASE_EX
        WHERE REPORT_NO=#{reportNo}
        AND CASE_TIMES=#{caseTimes}
    </insert>
</mapper>