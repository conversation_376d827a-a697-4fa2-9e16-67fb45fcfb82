<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
		    
<mapper namespace="com.paic.ncbs.claim.dao.mapper.endcase.CaseRegisterApplyMapper">

	<resultMap type="com.paic.ncbs.claim.model.dto.endcase.CaseRegisterApplyDTO" id="caseRegistApplyDTO">
		<result column="CREATED_BY" property="createdBy"/>
		<result column="CREATED_DATE" property="createdDate"/>
		<result column="UPDATED_BY" property="updatedBy"/>
		<result column="UPDATED_DATE" property="updatedDate"/>
		<result column="ID_AHCS_CASE_REGISTER_APPLY" property="idAhcsCaseRegisterApply"/>
		<result column="REPORT_NO" property="reportNo"/>
		<result column="CASE_TIMES" property="caseTimes"/>
		<result column="APPLY_UM" property="applyUm"/>
		<result column="APPLY_DATE" property="applyDate"/>
		<result column="APPLY_TIMES" property="applyTimes"/>
		<result column="AUDIT_UM" property="auditUm"/>
		<result column="AUDIT_OPINION" property="auditOpinion"/>
		<result column="AUDIT_DATE" property="auditDate"/>
		<result column="STATUS" property="status"/>
		<result column="AUDIT_REMARK" property="auditRemark"/>
		<result column="REGISTER_AMOUNT" property="registerAmount"/>

	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.vo.endcase.CaseRegisterApplyVO" id="caseRegistApplyVO">
		<result column="ID_AHCS_CASE_REGISTER_APPLY" property="idAhcsCaseRegisterApply"/>
		<result column="REPORT_NO" property="reportNo"/>
		<result column="CASE_TIMES" property="caseTimes"/>
		<result column="APPLY_UM" property="applyUm"/>
		<result column="APPLY_DATE" property="applyDate"/>
		<result column="APPLY_TIMES" property="applyTimes"/>
		<result column="AUDIT_UM" property="auditUm"/>
		<result column="AUDIT_OPINION" property="auditOpinion"/>
		<result column="AUDIT_DATE" property="auditDate"/>
		<result column="STATUS" property="status"/>
		<result column="AUDIT_REMARK" property="auditRemark"/>
		<result column="APPLYOR_NAME" property="applyorName"/>
		<result column="AUDITOR_NAME" property="auditorName"/>
	</resultMap>


<!--	<insert id="addCaseRegisterApplyDTO" parameterType="com.paic.icore.ahcs.report.register.dto.CaseRegisterApplyDTO">-->
<!--		INSERT INTO CLMS_CASE_REGISTER_APPLY(-->
<!--			CREATED_BY,-->
<!--			CREATED_DATE,-->
<!--			UPDATED_BY,-->
<!--			UPDATED_DATE,-->
<!--			ID_AHCS_CASE_REGISTER_APPLY,-->
<!--			REPORT_NO,-->
<!--			CASE_TIMES,-->
<!--			APPLY_UM,-->
<!--			APPLY_DATE,-->
<!--			APPLY_TIMES,-->
<!--			AUDIT_UM,-->
<!--			AUDIT_OPINION,-->
<!--			AUDIT_DATE,-->
<!--			STATUS,-->
<!--		    AUDIT_REMARK,-->
<!--			ARCHIVE_TIME-->
<!--		)VALUES(-->
<!--			#{createdBy, jdbcType = VARCHAR},-->
<!--			SYSDATE,-->
<!--			#{updatedBy, jdbcType = VARCHAR},-->
<!--			SYSDATE,-->
<!--			<if test="idAhcsCaseRegisterApply==null or idAhcsCaseRegisterApply==''">-->
<!--				SYS_GUID(),-->
<!--			</if>-->
<!--			<if test="idAhcsCaseRegisterApply!=null and idAhcsCaseRegisterApply!=''">-->
<!--				#{idAhcsCaseRegisterApply, jdbcType = VARCHAR},-->
<!--			</if>-->
<!--			#{reportNo, jdbcType=VARCHAR},-->
<!--			#{caseTimes, jdbcType=INTEGER},-->
<!--			#{applyUm, jdbcType=VARCHAR},-->
<!--            SYSDATE,-->
<!--			#{applyTimes, jdbcType=INTEGER},-->
<!--			#{auditUm, jdbcType=VARCHAR},-->
<!--			#{auditOpinion, jdbcType=VARCHAR},-->
<!--			#{auditDate, jdbcType=TIMESTAMP},-->
<!--			#{status, jdbcType=VARCHAR},-->
<!--			#{auditRemark, jdbcType=VARCHAR},-->
<!--			IFNULL((select cp.ARCHIVE_TIME -->
<!--				  from CLMS_case_process cp-->
<!--					where cp.report_no = #{reportNo,jdbcType=VARCHAR}-->
<!--			              and cp.case_times = #{caseTimes,jdbcType=INTEGER}-->
<!--			              and ROWNUM = 1),sysdate)-->
<!--		)-->
<!--	</insert>-->

	<insert id="addCaseRegisterApplyDTO" parameterType="com.paic.ncbs.claim.model.dto.endcase.CaseRegisterApplyDTO">
		INSERT INTO CLMS_CASE_REGISTER_APPLY(
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_CASE_REGISTER_APPLY,
			REPORT_NO,
			CASE_TIMES,
			APPLY_UM,
			APPLY_DATE,
			APPLY_TIMES,
			AUDIT_UM,
			AUDIT_OPINION,
			AUDIT_DATE,
			STATUS,
			AUDIT_REMARK,
			ARCHIVE_TIME,
			REGISTER_AMOUNT
		)VALUES(
			#{createdBy, jdbcType = VARCHAR},
			SYSDATE(),
			#{updatedBy, jdbcType = VARCHAR},
			SYSDATE(),
			<if test="idAhcsCaseRegisterApply==null or idAhcsCaseRegisterApply==''">
				replace(uuid(),'-',''),
			</if>
			<if test="idAhcsCaseRegisterApply!=null and idAhcsCaseRegisterApply!=''">
				#{idAhcsCaseRegisterApply, jdbcType = VARCHAR},
			</if>
			#{reportNo, jdbcType=VARCHAR},
			#{caseTimes, jdbcType=INTEGER},
			#{applyUm, jdbcType=VARCHAR},
			SYSDATE(),
			#{applyTimes, jdbcType=INTEGER},
			#{auditUm, jdbcType=VARCHAR},
			#{auditOpinion, jdbcType=VARCHAR},
			#{auditDate, jdbcType=TIMESTAMP},
			#{status, jdbcType=VARCHAR},
			#{auditRemark, jdbcType=VARCHAR},
			now(),
			#{registerAmount, jdbcType=DECIMAL}
			)
	</insert>

	<select id="getLastestRegisterApplyDTO" resultMap="caseRegistApplyDTO">
		select CREATED_BY,
			   CREATED_DATE,
			   UPDATED_BY,
			   UPDATED_DATE,
			   ID_AHCS_CASE_REGISTER_APPLY,
			   REPORT_NO,
			   CASE_TIMES,
			   APPLY_UM,
			   APPLY_DATE,
			   APPLY_TIMES,
			   AUDIT_UM,
			   AUDIT_OPINION,
			   AUDIT_DATE,
			   STATUS,
			   AUDIT_REMARK,
		       REGISTER_AMOUNT
		from CLMS_CASE_REGISTER_APPLY
		where REPORT_NO = #{reportNo, jdbcType=VARCHAR}
		  and CASE_TIMES = #{caseTimes, jdbcType=INTEGER}
		order by APPLY_DATE desc
		limit 1
	</select>

	<select id="getLastestRegisterApplyVOList" resultMap="caseRegistApplyVO">
		SELECT  ID_AHCS_CASE_REGISTER_APPLY,
				REPORT_NO,
				CASE_TIMES,
				APPLY_UM,
				APPLY_DATE,
				APPLY_TIMES,
				AUDIT_UM,
				AUDIT_OPINION,
				AUDIT_DATE,
				STATUS,
				AUDIT_REMARK
		 from CLMS_CASE_REGISTER_APPLY
		where REPORT_NO = #{reportNo, jdbcType=VARCHAR}
		  and CASE_TIMES = #{caseTimes, jdbcType=INTEGER}
		order by APPLY_DATE asc
	</select>

	<update id="saveRegisterAuditInfo" parameterType="com.paic.ncbs.claim.model.vo.endcase.CaseRegisterApplyVO">
		update CLMS_CASE_REGISTER_APPLY
		set UPDATED_DATE= now(),
			UPDATED_BY=#{auditUm, jdbcType=VARCHAR},
		    AUDIT_UM=#{auditUm, jdbcType=VARCHAR},
			AUDIT_OPINION=#{auditOpinion, jdbcType=VARCHAR},
			AUDIT_DATE=now(),
			STATUS=#{status, jdbcType=VARCHAR},
			AUDIT_REMARK=#{auditRemark, jdbcType=VARCHAR}
		where ID_AHCS_CASE_REGISTER_APPLY=#{idAhcsCaseRegisterApply, jdbcType=VARCHAR}
	</update>

	<update id="modifyRegisterAuditStatus" >
		update CLMS_CASE_REGISTER_APPLY
		set UPDATED_DATE=now(),
		UPDATED_BY=#{auditUm, jdbcType=VARCHAR},
		STATUS=#{status, jdbcType=VARCHAR}
		where ID_AHCS_CASE_REGISTER_APPLY=#{idAhcsCaseRegisterApply, jdbcType=VARCHAR}
	</update>

	<insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
		INSERT INTO CLMS_CASE_REGISTER_APPLY (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_CASE_REGISTER_APPLY,
			REPORT_NO,
			CASE_TIMES,
			APPLY_UM,
			APPLY_DATE,
			APPLY_TIMES,
			AUDIT_UM,
			AUDIT_OPINION,
			AUDIT_DATE,
			STATUS,
			AUDIT_REMARK,
			ARCHIVE_TIME,
			REGISTER_AMOUNT
		)
		SELECT
			#{userId},
			NOW(),
			#{userId},
			NOW(),
			MD5(UUID()),
			REPORT_NO,
			#{reopenCaseTimes},
			APPLY_UM,
			APPLY_DATE,
			APPLY_TIMES,
			AUDIT_UM,
			AUDIT_OPINION,
			AUDIT_DATE,
			STATUS,
			AUDIT_REMARK,
			NOW(),
			REGISTER_AMOUNT
		FROM CLMS_CASE_REGISTER_APPLY
		WHERE REPORT_NO=#{reportNo}
		AND CASE_TIMES=#{caseTimes}
	</insert>
	<select id="getNoRegisterData" parameterType="java.lang.Integer" resultType="java.lang.String">
		select wcb.REPORT_NO from clm_whole_case_base wcb
		join CLMS_REPORT_CUSTOMER rc on wcb.REPORT_NO = rc.REPORT_NO
		where wcb.IS_REGISTER is null and wcb.created_date <![CDATA[<]]> SUBDATE(now(),#{configDays})
		and rc.CLIENT_CLUSTER not in('200','020')
	</select>
</mapper>