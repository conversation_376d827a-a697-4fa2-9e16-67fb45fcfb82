<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.paic.ncbs.claim.dao.mapper.endcase.EndCaseMapper">
	<resultMap type="com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO" id="wholeCaseInfo">
		<result property="createdBy" column="CREATED_BY"></result>
		<result property="createdDate" column="CREATED_DATE"/>
		<result property="updatedBy" column="UPDATED_BY"/>
		<result property="updatedDate" column="UPDATED_DATE"/>
		<result property="wholeCaseBaseId" column="ID_CLM_WHOLE_CASE_BASE"/>
		<result property="reportNo" column="REPORT_NO"/>
		<result property="caseTimes" column="CASE_TIMES"/>
		<result property="indemnityConclusion" column="INDEMNITY_CONCLUSION"/>
		<result property="indemnityModel" column="INDEMNITY_MODEL"/>
		<result property="wholeCaseStatus" column="WHOLE_CASE_STATUS"/>
		<result property="endCaseDate" column="END_CASE_DATE"/>
		<result property="settleEndDate" column="SETTLE_END_DATE"/>
		<result property="caseCancelReason" column="CASE_CANCEL_REASON"/>
		<result property="caseFinisherUm" column="CASE_FINISHER_UM"/>
		<result property="settlerUm" column="SETTLER_UM"/>
		<result property="settleStartDate" column="SETTLE_START_DATE"/>
		<result property="cancelReasonCode" column="CANCEL_REASON_CODE"/>
	</resultMap>
	
	<resultMap type="com.paic.ncbs.claim.model.dto.settle.PolicyInfoDTO" id="casePolicyInfo">
		<result property="policyNo" column="policy_no"/>
		<result property="policyCerNo" column="policy_cer_no"/>
	</resultMap>
	
	<!-- 通过报案号和赔付次数获取整案表记录 -->
	<select id="getWholeCaseBaseDTO" resultMap="wholeCaseInfo">
		select  CREATED_BY,
				CREATED_DATE,
				UPDATED_BY,
				UPDATED_DATE,
				ID_CLM_WHOLE_CASE_BASE,
	    		REPORT_NO,
	    		CASE_TIMES,
	    		INDEMNITY_CONCLUSION,
	    		INDEMNITY_MODEL,
	    		WHOLE_CASE_STATUS,
	    		END_CASE_DATE,
	    		SETTLE_END_DATE,
	    		CASE_CANCEL_REASON,
	    		CASE_FINISHER_UM,
	    		SETTLER_UM,
	    		SETTLE_START_DATE,
	    		CANCEL_REASON_CODE
	    from CLM_WHOLE_CASE_BASE a
		where a.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		  and a.CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
	</select>
	
	<!-- 通过报案号和赔付次数更新整案表记录 -->
	<update id="modifyWholeCase" parameterType="com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO">
		update CLM_WHOLE_CASE_BASE a
		   set
				a.UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
				a.UPDATED_DATE = sysdate(),
				a.END_CASE_DATE = #{endCaseDate,jdbcType=TIMESTAMP}
			<if test="indemnityConclusion != null">
				,a.INDEMNITY_CONCLUSION = #{indemnityConclusion,jdbcType=VARCHAR}
			</if>
			<if test="indemnityModel != null">
				,a.INDEMNITY_MODEL = #{indemnityModel,jdbcType=VARCHAR}
			</if>
			<if test="wholeCaseStatus != null">
				,a.WHOLE_CASE_STATUS = #{wholeCaseStatus,jdbcType=VARCHAR}
			</if>
			<if test="settleEndDate != null">
				,a.SETTLE_END_DATE = #{settleEndDate,jdbcType=DATE}
			</if>
			<if test="caseCancelReason != null">
				,a.CASE_CANCEL_REASON = #{caseCancelReason,jdbcType=VARCHAR}
			</if>
			<if test="caseFinisherUm != null">
				,a.CASE_FINISHER_UM = #{caseFinisherUm,jdbcType=VARCHAR}
			</if>
			<if test="settlerUm != null">
				,a.SETTLER_UM = #{settlerUm,jdbcType=VARCHAR}
			</if>
			<if test="documentFullDate != null">
				,a.document_full_date = #{documentFullDate,jdbcType=VARCHAR}
			</if>
			<if test="receiveVoucherUm != null">
				,a.receive_voucher_um = #{receiveVoucherUm,jdbcType=VARCHAR}
			</if>
			<if test="settleStartDate != null">
				,a.SETTLE_START_DATE = #{settleStartDate,jdbcType=DATE}
			</if>
			<if test="cancelReasonCode != null">
				,a.CANCEL_REASON_CODE = #{cancelReasonCode,jdbcType=VARCHAR}
			</if>

			<if test="registNo != null">
				,a.regist_no = #{registNo,jdbcType=VARCHAR}
			</if>

			<if test="endCaseNo != null">
				,a.end_case_no = #{endCaseNo,jdbcType=VARCHAR}
			</if>
		where a.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		  and a.CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
	</update>

	<!-- 通过报案号和赔付次数更新整案表记录 -->
	<update id="modifyWholeCaseByAuto" parameterType="com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO">
		update CLM_WHOLE_CASE_BASE a
		set
		a.UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
		a.UPDATED_DATE = sysdate(),
		a.END_CASE_DATE = #{endCaseDate,jdbcType=TIMESTAMP}
		<if test="indemnityConclusion != null">
			,a.INDEMNITY_CONCLUSION = #{indemnityConclusion,jdbcType=VARCHAR}
		</if>
		<if test="indemnityModel != null">
			,a.INDEMNITY_MODEL = #{indemnityModel,jdbcType=VARCHAR}
		</if>
		<if test="wholeCaseStatus != null">
			,a.WHOLE_CASE_STATUS = #{wholeCaseStatus,jdbcType=VARCHAR}
		</if>
		<if test="settleEndDate != null">
			,a.SETTLE_END_DATE = #{settleEndDate,jdbcType=DATE}
		</if>
		<if test="caseCancelReason != null">
			,a.CASE_CANCEL_REASON = #{caseCancelReason,jdbcType=VARCHAR}
		</if>
		<if test="caseFinisherUm != null">
			,a.CASE_FINISHER_UM = #{caseFinisherUm,jdbcType=VARCHAR}
		</if>
		<if test="settlerUm != null">
			,a.SETTLER_UM = #{settlerUm,jdbcType=VARCHAR}
		</if>
		<if test="documentFullDate != null">
			,a.document_full_date = #{documentFullDate,jdbcType=VARCHAR}
		</if>
		<if test="receiveVoucherUm != null">
			,a.receive_voucher_um = #{receiveVoucherUm,jdbcType=VARCHAR}
		</if>
		<if test="settleStartDate != null">
			,a.SETTLE_START_DATE = #{settleStartDate,jdbcType=DATE}
		</if>
		<if test="cancelReasonCode != null">
			,a.CANCEL_REASON_CODE = #{cancelReasonCode,jdbcType=VARCHAR}
		</if>

		<if test="registNo != null">
			,a.regist_no = #{registNo,jdbcType=VARCHAR}
		</if>

		<if test="endCaseNo != null">
			,a.end_case_no = #{endCaseNo,jdbcType=VARCHAR}
		</if>
		<if test="isRegister != null">
			,a.is_register = #{isRegister,jdbcType=VARCHAR}
		</if>
		<if test="registerDate != null">
			,a.register_date = #{registerDate,jdbcType=TIMESTAMP}
		</if>
		<if test="registerUm != null">
			,a.register_um = #{registerUm,jdbcType=VARCHAR}
		</if>
		where a.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
		and a.CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
	</update>
	
	<!-- 获取案件下的保单号和电子保单号 -->
	<select id="getCasePolicy" parameterType="java.lang.String" resultMap="casePolicyInfo">
		select b.policy_no,
			   b.policy_cer_no 
	      from clm_case_base a, CLMS_policy_info b
		 where a.case_no=b.case_no
		   and a.report_no = #{reportNo, jdbcType=VARCHAR}
		   and a.case_times= #{caseTimes, jdbcType=INTEGER}
		   and b.product_code is null
	</select>
	
	<!--没有产品代码的保单案件结案时，校验该保单下已结案、赔付结论为赔付、赔付金额大于等于0,且结案日期在系统当前日期前30日内的案件累计数量-->
	<select id="getSendMailPolicy" parameterType="java.lang.String" resultType="java.lang.Integer">
		select count(1) from (select distinct b.report_no, b.case_times from CLMS_policy_info a, clm_case_base b
	      where a.case_no=b.case_no
	        <if test="policyCerNo != null">
				and a.policy_cer_no=#{policyCerNo, jdbcType=VARCHAR}
			</if>
			<if test="policyCerNo==null">
				and a.policy_no=#{policy_no, jdbcType=VARCHAR}
			</if>
	      and b.end_case_date >= add_months(sysdate(), -1)
	      and b.case_status='0'
	      and b.indemnity_conclusion='1'
	      and exists (select d.report_no from clm_policy_pay d where d.report_no=b.report_no and d.policy_pay >= 0))t
	</select>
	
	<!-- 根据报案号和赔付次数更新保单赔案表的案件状态 -->
	<update id="updateCaseBase">
		 update clm_case_base cb set cb.case_status = #{caseStatus}, UPDATED_DATE=sysdate()
		 where cb.report_no = #{reportNo} and cb.case_times = #{caseTimes}
	</update>
	
	<!-- 获取30天内没有配置理算公式的案件超过5个的保单-->
	<select id="getNoSettleFormulaPolicyList" parameterType="java.lang.Integer" resultType="java.lang.String">
		select b.policy_no
        from clm_case_base a, CLMS_policy_info b
     where  a.case_no = b.case_no
	      and a.end_case_date >= sysdate()-#{searchDay, jdbcType=INTEGER}
        and b.updated_date>=sysdate()-#{searchDay, jdbcType=INTEGER}
        and a.case_status='0'
	      and a.indemnity_conclusion='1'
	      and b.product_code is null
        and exists (select 1 from clm_policy_pay d where d.report_no=a.report_no and d.policy_pay >= 0 and d.case_times=a.case_times
        and d.case_no=a.case_no)
        group by b.policy_no
        having count(b.report_no)>=5
	</select>
	
	<!-- 获取结案金额-->
	<select id="getEndAmountByReportNo" resultType="java.math.BigDecimal">
		select sum(ifnull(p.policy_sum_pay, 0))
		  from clm_policy_pay p
		 where p.report_no = #{reportNo}
		   and p.case_times = #{caseTimes}
	</select>

	<!-- 获取当天结案的案件 -->
	<select id="findDayEndCase" resultMap="wholeCaseInfo">
		select report_no, case_times
		from CLM_WHOLE_CASE_BASE a
		where END_CASE_DATE > trunc(sysdate())
		and migrate_from in ('na', 'oa')
	</select>
	<select id="getAccidentDate" resultType="java.util.Date" >
		select ra.accident_date
		from clm_report_accident ra
		where ra.report_no = #{reportNo}
	</select>

	<select id="getWholeDocumentFullDate" resultType="com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO">
		select t.register_date registerDate,
		t.document_full_date documentFullDate,
		t.end_case_date endCaseDate,
		(select t1.created_date
			from CLMS_case_process t1
			where t1.report_no=t.report_no
			and t1.case_times=t.case_times
			limit 1) createdDate
		from clm_whole_case_base t
		where t.report_no = #{reportNo, jdbcType=VARCHAR}
		and t.case_times = #{caseTimes, jdbcType=INTEGER}
	</select>
	<update id="updateWholeDocumentFullDate" parameterType="com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO">
		update clm_whole_case_base t
		set t.updated_date=sysdate()
		<if test="documentFullDate != null">
			,t.document_full_date = #{documentFullDate,jdbcType=TIMESTAMP}
		</if>
		<if test="registerDate != null">
			,t.register_date = #{registerDate,jdbcType=TIMESTAMP}
		</if>
		where t.report_no = #{reportNo, jdbcType=VARCHAR}
		and t.case_times = #{caseTimes, jdbcType=INTEGER}
	</update>

</mapper>