<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.endcase.CaseBaseMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity">
        <id column="ID_CLM_CASE_BASE" property="idClmCaseBase" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="REPORT_NO" property="reportNo" jdbcType="VARCHAR"/>
        <result column="CASE_TIMES" property="caseTimes" jdbcType="DECIMAL"/>
        <result column="POLICY_NO" property="policyNo" jdbcType="VARCHAR"/>
        <result column="CASE_NO" property="caseNo" jdbcType="VARCHAR"/>
        <result column="REGIST_NO" property="registNo" jdbcType="VARCHAR"/>
        <result column="REGISTER_DATE" property="registerDate" jdbcType="TIMESTAMP"/>
        <result column="SETTLE_DATE" property="settleDate" jdbcType="TIMESTAMP"/>
        <result column="END_CASE_DATE" property="endCaseDate" jdbcType="TIMESTAMP"/>
        <result column="CASE_STATUS" property="caseStatus" jdbcType="VARCHAR"/>
        <result column="INDEMNITY_CONCLUSION" property="indemnityConclusion" jdbcType="VARCHAR"/>
        <result column="CASE_FINISHER_UM" property="caseFinisherUm" jdbcType="VARCHAR"/>
        <result column="REGISTER_UM" property="registerUm" jdbcType="VARCHAR"/>
        <result column="SETTLER_UM" property="settlerUm" jdbcType="VARCHAR"/>
        <result column="MIGRATE_FROM" property="migrateFrom" jdbcType="VARCHAR"/>
        <result column="DEPARTMENT_CODE" property="departmentCode" jdbcType="VARCHAR"/>
        <result column="DOCUMENT_GROUP_ID" property="documentGroupId" jdbcType="VARCHAR"/>
        <result column="risk_group_no" property="riskGroupNo" jdbcType="VARCHAR"/>
        <result column="risk_group_name" property="riskGroupName" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID_CLM_CASE_BASE, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, REPORT_NO,
        CASE_TIMES, POLICY_NO, CASE_NO, REGISTER_DATE, SETTLE_DATE, END_CASE_DATE, CASE_STATUS,
        INDEMNITY_CONCLUSION, CASE_FINISHER_UM, REGISTER_UM, SETTLER_UM, MIGRATE_FROM, DEPARTMENT_CODE,
        DOCUMENT_GROUP_ID,REGIST_NO,risk_group_no, risk_group_name
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLM_CASE_BASE
        where ID_CLM_CASE_BASE = #{idClmCaseBase,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from CLM_CASE_BASE
        where ID_CLM_CASE_BASE = #{idClmCaseBase,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity">
        insert into CLM_CASE_BASE (ID_CLM_CASE_BASE, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, REPORT_NO,
        CASE_TIMES, POLICY_NO, CASE_NO,REGIST_NO,
        REGISTER_DATE, SETTLE_DATE, END_CASE_DATE,
        CASE_STATUS, INDEMNITY_CONCLUSION, CASE_FINISHER_UM,
        REGISTER_UM, SETTLER_UM, MIGRATE_FROM,
        DEPARTMENT_CODE, DOCUMENT_GROUP_ID,ARCHIVE_DATE,risk_group_no,risk_group_name)
        values (#{idClmCaseBase,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP},
        #{updatedBy,jdbcType=VARCHAR}, #{updatedDate,jdbcType=TIMESTAMP}, #{reportNo,jdbcType=VARCHAR},
        #{caseTimes,jdbcType=DECIMAL}, #{policyNo,jdbcType=VARCHAR}, #{caseNo,jdbcType=VARCHAR},#{registNo,jdbcType=VARCHAR},
        #{registerDate,jdbcType=TIMESTAMP}, #{settleDate,jdbcType=TIMESTAMP}, #{endCaseDate,jdbcType=TIMESTAMP},
        #{caseStatus,jdbcType=VARCHAR}, #{indemnityConclusion,jdbcType=VARCHAR}, #{caseFinisherUm,jdbcType=VARCHAR},
        #{registerUm,jdbcType=VARCHAR}, #{settlerUm,jdbcType=VARCHAR}, #{migrateFrom,jdbcType=VARCHAR},
        #{departmentCode,jdbcType=VARCHAR}, #{documentGroupId,jdbcType=VARCHAR},now(),#{riskGroupNo},#{riskGroupName})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity">
        update CLM_CASE_BASE
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportNo != null">
                REPORT_NO = #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="caseTimes != null">
                CASE_TIMES = #{caseTimes,jdbcType=DECIMAL},
            </if>
            <if test="policyNo != null">
                POLICY_NO = #{policyNo,jdbcType=VARCHAR},
            </if>
            <if test="caseNo != null">
                CASE_NO = #{caseNo,jdbcType=VARCHAR},
            </if>
            <if test="registNo != null">
                REGIST_NO = #{registNo,jdbcType=VARCHAR},
            </if>
            <if test="registerDate != null">
                REGISTER_DATE = #{registerDate,jdbcType=TIMESTAMP},
            </if>
            <if test="settleDate != null">
                SETTLE_DATE = #{settleDate,jdbcType=TIMESTAMP},
            </if>
            <if test="endCaseDate != null">
                END_CASE_DATE = #{endCaseDate,jdbcType=TIMESTAMP},
            </if>
            <if test="caseStatus != null">
                CASE_STATUS = #{caseStatus,jdbcType=VARCHAR},
            </if>
            <if test="indemnityConclusion != null">
                INDEMNITY_CONCLUSION = #{indemnityConclusion,jdbcType=VARCHAR},
            </if>
            <if test="caseFinisherUm != null">
                CASE_FINISHER_UM = #{caseFinisherUm,jdbcType=VARCHAR},
            </if>
            <if test="registerUm != null">
                REGISTER_UM = #{registerUm,jdbcType=VARCHAR},
            </if>
            <if test="settlerUm != null">
                SETTLER_UM = #{settlerUm,jdbcType=VARCHAR},
            </if>
            <if test="migrateFrom != null">
                MIGRATE_FROM = #{migrateFrom,jdbcType=VARCHAR},
            </if>
            <if test="departmentCode != null">
                DEPARTMENT_CODE = #{departmentCode,jdbcType=VARCHAR},
            </if>
            <if test="documentGroupId != null">
                DOCUMENT_GROUP_ID = #{documentGroupId,jdbcType=VARCHAR},
            </if>
        </set>
        where ID_CLM_CASE_BASE = #{idClmCaseBase,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity">
        update CLM_CASE_BASE
        set CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
        REPORT_NO = #{reportNo,jdbcType=VARCHAR},
        CASE_TIMES = #{caseTimes,jdbcType=DECIMAL},
        POLICY_NO = #{policyNo,jdbcType=VARCHAR},
        CASE_NO = #{caseNo,jdbcType=VARCHAR},
        REGIST_NO = #{registNo,jdbcType=VARCHAR},
        REGISTER_DATE = #{registerDate,jdbcType=TIMESTAMP},
        SETTLE_DATE = #{settleDate,jdbcType=TIMESTAMP},
        END_CASE_DATE = #{endCaseDate,jdbcType=TIMESTAMP},
        CASE_STATUS = #{caseStatus,jdbcType=VARCHAR},
        INDEMNITY_CONCLUSION = #{indemnityConclusion,jdbcType=VARCHAR},
        CASE_FINISHER_UM = #{caseFinisherUm,jdbcType=VARCHAR},
        REGISTER_UM = #{registerUm,jdbcType=VARCHAR},
        SETTLER_UM = #{settlerUm,jdbcType=VARCHAR},
        MIGRATE_FROM = #{migrateFrom,jdbcType=VARCHAR},
        DEPARTMENT_CODE = #{departmentCode,jdbcType=VARCHAR},
        DOCUMENT_GROUP_ID = #{documentGroupId,jdbcType=VARCHAR}
        where ID_CLM_CASE_BASE = #{idClmCaseBase,jdbcType=VARCHAR}
    </update>
    <select id="getCaseBaseInfoByReportNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from clm_case_base
        where report_no=#{reportNo}
    </select>
    <select id="getCaseBaseInfoByReportNoAndPolicytNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from clm_case_base
        where report_no = #{reportNo,jdbcType=VARCHAR}
        and policy_No = #{policyNo,jdbcType=VARCHAR}
    </select>
    <select id="getCaseBaseInfoByReportNoAndCasetimes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from clm_case_base
        where report_no=#{reportNo}
        and case_times=#{casetimes}
    </select>

    <!-- 通过报案号和赔付次数更新案件结案信息 -->
    <update id="batchModifyCaseBaseDTO" parameterType="com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO">
        update clm_case_base a
        set a.UPDATED_DATE=sysdate()
        <if test="updatedBy!=null">
            ,a.UPDATED_BY=#{updatedBy,jdbcType=VARCHAR}
        </if>
        <if test="registerDate!=null">
            ,a.REGISTER_DATE=#{registerDate,jdbcType=TIMESTAMP}
        </if>
        <if test="endCaseDate!=null">
            ,a.END_CASE_DATE=#{endCaseDate,jdbcType=TIMESTAMP}
        </if>
        <if test="wholeCaseStatus!=null">
            ,a.CASE_STATUS=#{wholeCaseStatus,jdbcType=VARCHAR}
        </if>
        <if test="indemnityConclusion!=null">
            ,a.INDEMNITY_CONCLUSION=#{indemnityConclusion,jdbcType=VARCHAR}
        </if>
        <if test="caseFinisherUm!=null">
            ,a.CASE_FINISHER_UM=#{caseFinisherUm,jdbcType=VARCHAR}
        </if>
        <if test="registerUm!=null">
            ,a.REGISTER_UM=#{registerUm,jdbcType=VARCHAR}
        </if>
        where a.REPORT_NO=#{reportNo, jdbcType=VARCHAR}
        and a.CASE_TIMES=#{caseTimes, jdbcType=VARCHAR}
    </update>



    <select id="getCaseBaseList" resultType="com.paic.ncbs.claim.model.dto.endcase.CaseBaseDTO">
        select a.ID_CLM_CASE_BASE idClmCaseBase,
        a.REPORT_NO reportNo,
        a.CASE_TIMES caseTimes,
        a.POLICY_NO policyNo,
        a.CASE_NO caseNo,
        a.REGIST_NO registNo,
        a.REGISTER_DATE registerDate,
        a.SETTLE_DATE settleDate,
        a.END_CASE_DATE endCaseDate,
        a.CASE_STATUS caseStatus,
        a.INDEMNITY_CONCLUSION indemnityConclusion,
        a.CASE_FINISHER_UM caseFinisherUm,
        a.REGISTER_UM registerUm,
        a.SETTLER_UM settlerUm,
        a.MIGRATE_FROM migrateFrom,
        a.DEPARTMENT_CODE departmentCode,
        a.DOCUMENT_GROUP_ID documentGroupId,
        a.risk_group_no riskGroupNo,
        a.risk_group_name riskGroupName
        from clm_case_base a
        where  a.REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        and  a.CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
    </select>

    <insert id="insertList" parameterType="java.util.List">
        insert into CLM_CASE_BASE (ID_CLM_CASE_BASE, CREATED_BY, CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, REPORT_NO,
        CASE_TIMES, POLICY_NO, CASE_NO,REGIST_NO,
        REGISTER_DATE, SETTLE_DATE, END_CASE_DATE,
        CASE_STATUS, INDEMNITY_CONCLUSION, CASE_FINISHER_UM,
        REGISTER_UM, SETTLER_UM, MIGRATE_FROM,
        DEPARTMENT_CODE, DOCUMENT_GROUP_ID,ARCHIVE_DATE, risk_group_no, risk_group_name)
        values
        <foreach collection="list" separator="," index="index" item="item">
            (#{item.idClmCaseBase,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR}, #{item.createdDate,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedDate,jdbcType=TIMESTAMP}, #{item.reportNo,jdbcType=VARCHAR},
            #{item.caseTimes,jdbcType=DECIMAL}, #{item.policyNo,jdbcType=VARCHAR}, #{item.caseNo,jdbcType=VARCHAR},#{item.registNo,jdbcType=VARCHAR},
            #{item.registerDate,jdbcType=TIMESTAMP}, #{item.settleDate,jdbcType=TIMESTAMP}, #{item.endCaseDate,jdbcType=TIMESTAMP},
            #{item.caseStatus,jdbcType=VARCHAR}, #{item.indemnityConclusion,jdbcType=VARCHAR}, #{item.caseFinisherUm,jdbcType=VARCHAR},
            #{item.registerUm,jdbcType=VARCHAR}, #{item.settlerUm,jdbcType=VARCHAR}, #{item.migrateFrom,jdbcType=VARCHAR},
            #{item.departmentCode,jdbcType=VARCHAR}, #{item.documentGroupId,jdbcType=VARCHAR},now(), #{item.riskGroupNo,jdbcType=VARCHAR},#{item.riskGroupName,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        INSERT INTO CLM_CASE_BASE (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_CLM_CASE_BASE,
            REPORT_NO,
            CASE_TIMES,
            POLICY_NO,
            CASE_NO,
            CASE_STATUS,
            REGISTER_DATE,
            REGISTER_UM,
            INDEMNITY_CONCLUSION,
            MIGRATE_FROM,
            DEPARTMENT_CODE,
            DOCUMENT_GROUP_ID,
            ID_POLICY_INFO,
            ARCHIVE_DATE,
            REGIST_NO
        )
        SELECT
            #{userId},
            NOW(),
            #{userId},
            NOW(),
            MD5(UUID()),
            REPORT_NO,
            #{reopenCaseTimes},
            POLICY_NO,
            CASE_NO,
            #{caseStatus},
            REGISTER_DATE,
            REGISTER_UM,
            #{indemnityConclusion},
            MIGRATE_FROM,
            DEPARTMENT_CODE,
            DOCUMENT_GROUP_ID,
            ID_POLICY_INFO,
            NOW(),
            REGIST_NO
        FROM CLM_CASE_BASE
        WHERE REPORT_NO=#{reportNo}
        AND CASE_TIMES=#{caseTimes}
    </insert>

    <select id="getCaseBaseInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from clm_case_base
        where report_no=#{reportNo}
        and policy_no=#{policyNo}
        and case_times=#{caseTimes}
    </select>

    <update id="updateRiskGroup">
        update clm_case_base
        set risk_group_no = #{riskGroupNo,jdbcType=VARCHAR},
        risk_group_name = #{riskGroupName,jdbcType=VARCHAR}
        where id_clm_case_base = #{idClmCaseBase,jdbcType=VARCHAR}
    </update>

    <update id="updateEsUpdatedDate">
        update clm_case_base a
        set a.es_updated_date=sysdate()
        where a.REPORT_NO=#{reportNo, jdbcType=VARCHAR}
        and a.CASE_TIMES=#{caseTimes, jdbcType=INTEGER}
    </update>

</mapper>