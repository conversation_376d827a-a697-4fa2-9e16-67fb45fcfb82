<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.endcase.CaseClassDTO" id="result1">
        <id column="ID_AHCS_CASE_CLASS" property="caseClassId"/>
        <result column="CREATED_BY" property="createdBy"/>
        <result column="CREATED_DATE" property="createdDate"/>
        <result column="UPDATED_BY" property="updatedBy"/>
        <result column="UPDATED_DATE" property="updatedDate"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="CASE_SUB_CLASS" property="caseSubClass"/>
        <result column="CLASS_NAME" property="caseSubClassName"/>
        <result column="TASK_ID" property="taskId"/>
        <result column="STATUS" property="status"/>
        <result column="LOSS_OBJECT_NO" property="lossObjectNo"/>
        <result column="ARCHIVE_TIME" property="archiveTime"/>
        <result column="ID_AHCS_ADDITIONAL_SURVEY" property="idAhcsAdditionalSurvey"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.endcase.CaseClassDTO" id="resultCaseClass">
        <result column="CASE_SUB_CLASS" property="caseSubClass"/>
        <result column="CLASS_NAME" property="caseSubClassName"/>
        <result column="LOSS_RESULT_DATE" property="lossResultDate"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.endcase.CaseClassDTO" id="result2">
        <result column="PARENT_CODE" property="caseSubClass"/>
        <result column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess"/>
        <result column="ID_AHCS_ADDITIONAL_SURVEY" property="idAhcsAdditionalSurvey"/>
    </resultMap>


    <select id="getCaseClassDTOList" resultMap="result1">
        select cc.REPORT_NO,
        cc.CASE_SUB_CLASS,
        cc.STATUS,
        cc.TASK_ID
        from CLMS_case_class cc
        where cc.REPORT_NO = #{reportNo}
        and cc.CASE_TIMES = #{caseTimes}
        and cc.STATUS='1'
        <if test="taskId != null and taskId != '' ">
            and cc.TASK_ID = #{taskId}
        </if>
        AND cc.IS_EFFECTIVE = 'Y'
        and cc.TASK_ID =
        (select * from
        (select t.TASK_ID from CLMS_case_class t where
        t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        and t.STATUS='1'
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        AND t.IS_EFFECTIVE = 'Y'
        order by t.CREATED_DATE desc)
        as temp limit 1
        )
    </select>

    <select id="getByAdditionalSurveyId" resultMap="result1">
        select c.*
        from CLMS_case_class c
        where c.report_no = #{reportNo,jdbcType=VARCHAR}
        and c.case_times = #{caseTimes,jdbcType=NUMERIC}
        AND c.IS_EFFECTIVE = 'Y'
        and c.TASK_ID = #{taskId,jdbcType=VARCHAR}
        AND c.ID_AHCS_ADDITIONAL_SURVEY = #{idAhcsAdditionalSurvey,jdbcType=VARCHAR}
    </select>


    <select id="getCaseClassListByTasksBak" resultMap="result1">
        select cc.REPORT_NO,
        cc.CASE_SUB_CLASS,
        cc.STATUS,
        cc.TASK_ID
        from CLMS_case_class cc
        where cc.REPORT_NO = #{reportNo}
        and cc.CASE_TIMES = #{caseTimes}
        AND cc.IS_EFFECTIVE = 'Y'
        and cc.TASK_ID =
        (select * from
        (select t.TASK_ID from CLMS_case_class t where
        t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        and t.TASK_ID in
        <foreach collection="tacheCodeList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND t.IS_EFFECTIVE = 'Y'
        order by t.CREATED_DATE desc)
        as temp limit 1
        )
    </select>


    <select id="getCaseClassListByTasks" resultMap="result1">
        select cc.CREATED_BY,
        cc.CREATED_DATE,
        cc.UPDATED_BY,
        cc.UPDATED_DATE,
        cc.REPORT_NO,
        cc.CASE_SUB_CLASS,
        cc.STATUS,
        cc.TASK_ID,
        cc.ARCHIVE_TIME
        from CLMS_case_class cc
        where cc.REPORT_NO = #{reportNo}
        and cc.CASE_TIMES = #{caseTimes}
        and cc.TASK_ID in
        <foreach collection="tacheCodeList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND cc.IS_EFFECTIVE = 'Y'
    </select>
    <insert id="saveCaseClassList">
        insert into CLMS_CASE_CLASS
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        CASE_SUB_CLASS,
        TASK_ID,
        STATUS,
        LOSS_OBJECT_NO,
        ID_AHCS_ADDITIONAL_SURVEY,
        ARCHIVE_TIME,
        ID_AHCS_CASE_CLASS,
        IS_EFFECTIVE)
        <foreach collection="caseClassList" index="index" item="item" open="(" close=")" separator="union all">
            select #{userId},
            #{item.createdDate},
            #{userId},
            #{item.updatedDate},
            #{item.reportNo},
            #{caseTimes},
            #{item.caseSubClass,jdbcType=VARCHAR} ,
            #{item.taskId,jdbcType=VARCHAR} ,
            #{item.status,jdbcType=VARCHAR} ,
            #{item.lossObjectNo,jdbcType=VARCHAR},
            #{item.idAhcsAdditionalSurvey,jdbcType=VARCHAR},
            <if test="item.archiveTime != null ">
                #{item.archiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.archiveTime == null ">
                sysdate(),
            </if>
            left(hex(uuid()),32),
            'Y'
        </foreach>
    </insert>

    <insert id="addCaseClassList">
        insert into CLMS_CASE_CLASS
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        CASE_SUB_CLASS,
        TASK_ID,
        STATUS,
        LOSS_OBJECT_NO,
        ID_AHCS_ADDITIONAL_SURVEY,
        ARCHIVE_TIME,
		ID_AHCS_CASE_CLASS,
        IS_EFFECTIVE)
        <foreach collection="caseClassList" index="index" item="item" open="(" close=")" separator="union all">
            select #{userId},
            now(),
            #{userId},
            now(),
            #{item.reportNo},
            #{caseTimes},
            #{item.caseSubClass,jdbcType=VARCHAR} ,
            #{item.taskId,jdbcType=VARCHAR} ,
            #{item.status,jdbcType=VARCHAR} ,
            #{item.lossObjectNo,jdbcType=VARCHAR},
            #{item.idAhcsAdditionalSurvey,jdbcType=VARCHAR},
            <if test="item.archiveTime != null ">
                #{item.archiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.archiveTime == null ">
                sysdate(),
            </if>
            left(hex(uuid()),32),
            'Y'
        </foreach>
    </insert>

    <insert id="addCaseClassLists">
        insert into CLMS_CASE_CLASS
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        CASE_SUB_CLASS,
        TASK_ID,
        STATUS,
        ARCHIVE_TIME)
        <foreach collection="caseClassList" index="index" item="item" open="(" close=")" separator="union all">
            select #{item.createdBy},
            #{item.createdDate,jdbcType=TIMESTAMP},
            #{item.updatedBy},
            #{item.updatedDate,jdbcType=TIMESTAMP},
            #{item.reportNo},
            #{caseTimes},
            #{item.caseSubClass,jdbcType=VARCHAR} ,
            #{item.taskId,jdbcType=VARCHAR} ,
            #{item.status,jdbcType=VARCHAR} ,
            <if test="item.archiveTime != null ">
                #{item.archiveTime,jdbcType=TIMESTAMP}
            </if>
            <if test="item.archiveTime == null ">
                sysdate()
            </if>
        </foreach>
    </insert>

    <insert id="saveCaseClass" parameterType="com.paic.ncbs.claim.model.dto.endcase.CaseClassDTO">
        insert into CLMS_CASE_CLASS
        (CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        CASE_SUB_CLASS,
        TASK_ID,
        STATUS,
        ID_AHCS_CASE_CLASS,
        archive_time
        )
        values
        (#{createdBy},
        now(),
        #{updatedBy},
        now(),
        #{reportNo},
        #{caseTimes},
        #{caseSubClass,jdbcType=VARCHAR},
        #{taskId,jdbcType=VARCHAR},
        #{status,jdbcType=VARCHAR},
        left(hex(uuid()),32),
        <if test="archiveTime != null ">
            #{archiveTime,jdbcType=TIMESTAMP}
        </if>
        <if test="archiveTime == null ">
            sysdate()
        </if>
        )
    </insert>

    <delete id="removeCaseClass">
        delete from CLMS_CASE_CLASS where REPORT_NO=#{reportNo} and CASE_TIMES=#{caseTimes}
        <if test="taskId != null and taskId != '' ">
            and TASK_ID = #{taskId}
        </if>
        <if test="caseSubClass != null and caseSubClass != '' ">
            and CASE_SUB_CLASS = #{caseSubClass}
        </if>
    </delete>

    <update id="updateEffective" parameterType="com.paic.ncbs.claim.model.dto.endcase.CaseClassDTO">
        UPDATE
        CLMS_CASE_CLASS
        SET
        UPDATED_BY = #{updatedBy},
        UPDATED_DATE = now(),
        IS_EFFECTIVE = 'N'
        WHERE
        REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        <if test="taskId != null and taskId != '' ">
            and TASK_ID = #{taskId}
        </if>
        <if test="lossObjectNo != null and lossObjectNo != '' ">
            and LOSS_OBJECT_NO = #{lossObjectNo}
        </if>
        <if test="idAhcsAdditionalSurvey != null and idAhcsAdditionalSurvey != '' ">
            and ID_AHCS_ADDITIONAL_SURVEY = #{idAhcsAdditionalSurvey}
        </if>
        AND IS_EFFECTIVE = 'Y'
    </update>
    <update id="updatePersonEffective">
        UPDATE
        CLMS_CASE_CLASS
        SET
        UPDATED_BY = #{updatedBy},
        UPDATED_DATE = now(),
        IS_EFFECTIVE = 'N'
        WHERE
        REPORT_NO = #{reportNo}
        AND CASE_TIMES = #{caseTimes}
        and case_sub_class = 'IAT_10_01';
    </update>

    <select id="getCaseClassList" resultType="string">
        select t.CASE_SUB_CLASS
        from CLMS_case_class t
        where t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        <if test=" idAhcsAdditionalSurvey != null and idAhcsAdditionalSurvey !='' ">
            AND ID_AHCS_ADDITIONAL_SURVEY = #{idAhcsAdditionalSurvey}
        </if>
        AND t.IS_EFFECTIVE = 'Y'
        and t.task_id =
        (select * from
        (select t1.task_id from CLMS_case_class t1 where
        t1.REPORT_NO = #{reportNo}
        and t1.CASE_TIMES = #{caseTimes}
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        <if test=" idAhcsAdditionalSurvey != null and idAhcsAdditionalSurvey !='' ">
            AND ID_AHCS_ADDITIONAL_SURVEY = #{idAhcsAdditionalSurvey}
        </if>
        AND t1.IS_EFFECTIVE = 'Y'
        order by t1.created_date desc)
        as temp limit 1
        )
    </select>

    <select id="getCaseClassListByAddSurveyId" resultType="string">
        select t.CASE_SUB_CLASS
        from CLMS_case_class t
        where t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        <if test="idAhcsAdditionalSurvey != null and idAhcsAdditionalSurvey != '' ">
            and t.idAhcsAdditionalSurvey = #{idAhcsAdditionalSurvey}
        </if>
        AND t.IS_EFFECTIVE = 'Y'
        and t.task_id =
        (select * from
        (select t1.task_id from CLMS_case_class t1 where
        t1.REPORT_NO = #{reportNo}
        and t1.CASE_TIMES = #{caseTimes}
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        <if test="idAhcsAdditionalSurvey != null and idAhcsAdditionalSurvey != '' ">
            and t.idAhcsAdditionalSurvey = #{idAhcsAdditionalSurvey}
        </if>
        AND t1.IS_EFFECTIVE = 'Y'
        order by t1.created_date desc)
        as temp limit 1
        )
    </select>

    <select id="getCaseClassNameList" resultType="string">
        select t2.class_name
        from CLMS_case_class t1, CLMS_case_class_define t2
        where t1.case_sub_class = t2.class_code
        and t1.report_no = #{reportNo}
        and t1.case_times = #{caseTimes}
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        <if test="status != null and status != '' ">
            and t1.STATUS = #{status}
        </if>
        AND t1.IS_EFFECTIVE = 'Y'
        and t1.task_id =
        (select * from
        (select t.task_id from CLMS_case_class t where
        t.report_no = #{reportNo}
        and t.case_times = #{caseTimes}
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        <if test="status != null and status != '' ">
            and t.STATUS = #{status}
        </if>
        AND t.IS_EFFECTIVE = 'Y'
        order by t.created_date desc)
        as temp limit 1
        )
    </select>

    <select id="getBigCaseClassList" resultMap="result2">
        select distinct t2.parent_code
        from CLMS_case_class t1,
        CLMS_case_class_define t2
        where t1.case_sub_class = t2.class_code
        and t1.report_no = #{reportNo}
        and t1.case_times = #{caseTimes}
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        <if test="status != null and status != '' ">
            and t1.STATUS = #{status}
        </if>
        <if test=" idAhcsAdditionalSurvey != null and idAhcsAdditionalSurvey !='' ">
            AND ID_AHCS_ADDITIONAL_SURVEY = #{idAhcsAdditionalSurvey}
        </if>
        AND t1.IS_EFFECTIVE = 'Y'
        and t1.TASK_ID =
        (select * from
        (select t.TASK_ID from CLMS_case_class t where
        t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        <if test="status != null and status != '' ">
            and t.STATUS = #{status}
        </if>
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        <if test=" idAhcsAdditionalSurvey != null and idAhcsAdditionalSurvey !='' ">
            AND ID_AHCS_ADDITIONAL_SURVEY = #{idAhcsAdditionalSurvey}
        </if>
        AND t.IS_EFFECTIVE = 'Y'
        order by t.CREATED_DATE desc)
        as temp limit 1
        )
    </select>

    <select id="getInsuredApplyStatus" resultType="java.lang.String">
        select gc.insured_apply_status
        from CLMS_case_class cc, CLMS_group_case_class gc
        where cc.case_sub_class = gc.case_class
        and cc.report_no = #{reportNo}
        and cc.case_times = #{caseTimes}
        <if test="taskId != null and taskId != '' ">
            and cc.TASK_ID = #{taskId}
        </if>
        AND cc.IS_EFFECTIVE = 'Y'
        and cc.task_id =
        (select * from
        (select t.task_id from CLMS_case_class t where
        t.report_no = #{reportNo}
        and t.case_times = #{caseTimes}
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        AND t.IS_EFFECTIVE = 'Y'
        order by t.created_date desc)
        as temp limit 1
        ) limit 1
    </select>


    <select id="getNewCaseClassList" resultMap="result1">
        SELECT A.REPORT_NO,A.CASE_SUB_CLASS,B.CLASS_NAME, B.PARENT_CODE parentCode
        FROM CLMS_CASE_CLASS A,CLMS_CASE_CLASS_DEFINE B
        WHERE B.CLASS_CODE=A.CASE_SUB_CLASS
        AND A.REPORT_NO = #{reportNo}
        AND A.CASE_TIMES = #{caseTimes}
        AND A.IS_EFFECTIVE = 'Y'
        <if test="lossObjectNo != null and lossObjectNo != '' ">
            AND A.LOSS_OBJECT_NO = #{lossObjectNo}
        </if>
        and A.task_id =
        (select * from
        (select t.task_id from CLMS_case_class t where
        t.report_no = #{reportNo}
        and t.case_times = #{caseTimes}
        AND t.IS_EFFECTIVE = 'Y'
        <if test="lossObjectNo != null and lossObjectNo != '' ">
            AND t.LOSS_OBJECT_NO = #{lossObjectNo}
        </if>
        order by t.created_date desc)
        as temp limit 1
        )
    </select>


    <select id="getAllCaseClassList" resultMap="result1">
        SELECT A.REPORT_NO,A.CASE_SUB_CLASS,B.CLASS_NAME, B.PARENT_CODE parentCode
        FROM CLMS_CASE_CLASS A,CLMS_CASE_CLASS_DEFINE B
        WHERE B.CLASS_CODE=A.CASE_SUB_CLASS
        AND A.REPORT_NO = #{reportNo}
        AND A.CASE_TIMES = #{caseTimes}
        <if test="taskId != null and taskId != '' ">
            AND A.TASK_ID = #{taskId}
        </if>
        AND A.IS_EFFECTIVE = 'Y'
    </select>

    <select id="getCaseClassListByRct" resultMap="result1">
        select cc.REPORT_NO,
        cc.CASE_SUB_CLASS,
        cc.STATUS,
        cc.TASK_ID
        from CLMS_case_class cc
        where cc.REPORT_NO = #{reportNo}
        and cc.CASE_TIMES = #{caseTimes}
        and cc.STATUS='1'
        and cc.TASK_ID = #{taskId}
        AND cc.IS_EFFECTIVE = 'Y'
    </select>


    <select id="getCaseClassCodeList" resultType="string">
        select cc.CASE_SUB_CLASS
        from CLMS_case_class cc
        where cc.REPORT_NO = #{reportNo}
        and cc.CASE_TIMES = #{caseTimes}
        and cc.STATUS='1'
        <if test="taskId != null and taskId != '' ">
            and cc.TASK_ID = #{taskId}
        </if>
        AND cc.IS_EFFECTIVE = 'Y'
        and cc.TASK_ID =
        (select t.TASK_ID from CLMS_case_class t where
        t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        and t.STATUS='1'
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        AND t.IS_EFFECTIVE = 'Y'
        order by t.CREATED_DATE desc
        LIMIT 1
        )

    </select>


    <select id="getCaseClassCodeListByTasks" resultType="string">
        select cc.CASE_SUB_CLASS
        from CLMS_case_class cc
        where cc.REPORT_NO = #{reportNo}
        and cc.CASE_TIMES = #{caseTimes}
        and cc.TASK_ID in
        <foreach collection="tacheCodeList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND cc.IS_EFFECTIVE = 'Y'
        and cc.TASK_ID =
        (select * from
        (select t.TASK_ID from CLMS_case_class t where
        t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        and t.TASK_ID in
        <foreach collection="tacheCodeList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND t.IS_EFFECTIVE = 'Y'
        order by t.CREATED_DATE desc)
        as temp limit 1
        )
    </select>


    <select id="getCurrentTaskCodeFromCaseClass" resultType="String">
        SELECT a.TASK_ID
        FROM (SELECT T.TASK_ID
        FROM CLMS_CASE_CLASS T
        WHERE T.REPORT_NO = #{reportNo}
        AND T.CASE_TIMES = #{caseTimes}
        <if test="status != null and status != '' ">
            and T.STATUS = #{status}
        </if>
        and T.TASK_ID != 'additionalSurveyProcessing'
        AND T.IS_EFFECTIVE = 'Y'
        order by t.CREATED_DATE desc) a
        limit 1
    </select>

    <select id="getCaseClassListByTaches" resultMap="result1">
        select cc.REPORT_NO,
        cc.CASE_SUB_CLASS,
        cc.STATUS,
        cc.TASK_ID,
        cc.ARCHIVE_TIME
        from CLMS_case_class cc
        where cc.REPORT_NO = #{reportNo}
        and cc.CASE_TIMES = #{caseTimes}
        and cc.STATUS='1'
        AND cc.IS_EFFECTIVE = 'Y'
        and cc.TASK_ID =
        (select * from
        (select t.TASK_ID from CLMS_case_class t where
        t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        and t.STATUS='1'
        and t.TASK_ID in
        <foreach collection="tacheCodeList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND t.IS_EFFECTIVE = 'Y'
        order by t.CREATED_DATE desc)
        as temp limit 1
        )
    </select>


    <select id="getCaseClassTache" resultType="string">
        select cc.TASK_ID
        from CLMS_case_class cc
        where cc.REPORT_NO = #{reportNo}
        and cc.CASE_TIMES = #{caseTimes}
        and cc.STATUS='1'
        AND cc.IS_EFFECTIVE = 'Y'
        and cc.TASK_ID =
        (select * from
        (select t.TASK_ID from CLMS_case_class t where
        t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        and t.STATUS='1'
        and t.TASK_ID in
        <foreach collection="tacheCodeList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND t.IS_EFFECTIVE = 'Y'
        order by t.CREATED_DATE desc)
        as temp limit 1
        )
        limit 1
    </select>


    <select id="getNonCasualtyCaseType" resultType="java.lang.String">
        SELECT group_concat(tt.class_code) caseTypes
        FROM CLMS_CASE_CLASS T,
        CLMS_CASE_CLASS_DEFINE TT
        WHERE TT.CLASS_CODE = T.CASE_SUB_CLASS and tt.parent_code = '2'
        AND T.REPORT_NO = #{reportNo}
        AND T.CASE_TIMES = #{caseTimes}
        AND T.IS_EFFECTIVE = 'Y'
        and t.task_id = (select * from
        (select t1.task_id from CLMS_case_class t1 where
        t1.REPORT_NO = #{reportNo}
        and t1.CASE_TIMES = #{caseTimes}
        AND t1.IS_EFFECTIVE = 'Y'
        order by t1.created_date desc)
        as temp limit 1
        ) order by t.created_date desc
    </select>

    <select id="getTravelChangeType" resultType="java.lang.String">
        select replace(t.change_type,'|',',') changeType from CLMS_REPORT_ACCIDENT_TRAVEL t where t.report_no =
        #{reportNo}
    </select>


    <select id="getCaseTacheClassCodeList" resultType="string">
        SELECT A.CASE_SUB_CLASS
        FROM CLMS_CASE_CLASS A
        WHERE A.REPORT_NO = #{reportNo}
        AND A.CASE_TIMES = #{caseTimes}
        <if test="status != null and status != '' ">
            and A.STATUS = #{status}
        </if>
        AND A.IS_EFFECTIVE = 'Y'
        and A.task_id =
        (select * from
        (select t.task_id from CLMS_case_class t
        where t.report_no = #{reportNo}
        and t.case_times = #{caseTimes}
        <if test="status != null and status != '' ">
            and t.status = #{status}
        </if>
        AND t.IS_EFFECTIVE = 'Y'
        order by t.created_date desc)
        as temp limit 1
        )
    </select>


    <select id="getlossResultList" resultMap="resultCaseClass">
        SELECT A.CASE_SUB_CLASS,
        (select d.CLASS_NAME FROM CLMS_CASE_CLASS_DEFINE d where d.CLASS_CODE = A.CASE_SUB_CLASS) CLASS_NAME,
        date_format(A.UPDATED_DATE, '%Y%m%d%H') LOSS_RESULT_DATE
        FROM CLMS_CASE_CLASS A
        WHERE A.REPORT_NO = #{reportNo}
        and A.CASE_TIMES = #{caseTimes}
        <if test="status != null and status != '' ">
            and A.STATUS = #{status}
        </if>
        AND A.IS_EFFECTIVE = 'Y'
        and A.task_id =
        (select * from
        (select t.task_id from CLMS_case_class t
        where t.report_no = #{reportNo}
        and t.case_times = #{caseTimes}
        <if test="status != null and status != '' ">
            and t.status = #{status}
        </if>
        AND t.IS_EFFECTIVE = 'Y'
        order by t.created_date desc)
        as temp limit 1
        )
    </select>

    <select id="getBigCaseClassName" resultType="String">
        select distinct t3.class_name
        from CLMS_case_class t1,
        CLMS_case_class_define t2,
        CLMS_case_class_define t3
        where t1.case_sub_class = t2.class_code
        and t2.parent_code=t3.class_code
        and t1.report_no = #{reportNo}
        and t1.case_times = #{caseTimes}
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        <if test="status != null and status != '' ">
            and t1.STATUS = #{status}
        </if>
        AND t1.IS_EFFECTIVE = 'Y'
        and t1.TASK_ID =
        (select * from
        (select t.TASK_ID from CLMS_case_class t where
        t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        <if test="status != null and status != '' ">
            and t.STATUS = #{status}
        </if>
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        AND t.IS_EFFECTIVE = 'Y'
        order by t.CREATED_DATE desc)
        as temp limit 1
        )
    </select>

    <select id="getCaseClassCodeByReportNo" resultType="string">
        select cc.CASE_SUB_CLASS
        from CLMS_case_class cc
        where cc.REPORT_NO = #{reportNo}
        and cc.CASE_TIMES = #{caseTimes}
        and cc.IS_EFFECTIVE = 'Y'
        and cc.TASK_ID =
        (select r.TASK_ID
        from (select t.TASK_ID
        from CLMS_case_class t
        where t.REPORT_NO = #{reportNo}
        and t.CASE_TIMES = #{caseTimes}
        and t.IS_EFFECTIVE = 'Y'
        and t.TASK_ID not in
        <foreach collection="tacheCodeList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by t.CREATED_DATE desc) r
        limit 1
        )
    </select>


    <select id="getCaseSubClassList" resultType="java.lang.String">
        select distinct CASE_SUB_CLASS from CLMS_CASE_CLASS where REPORT_NO =#{reportNo} and CASE_TIMES =#{caseTimes}
        <if test="taskId != null and taskId != '' ">
            and TASK_ID =#{taskId}
        </if>
        and IS_EFFECTIVE ='Y'
    </select>

    <!-- 获取报案跟踪环节最新的环节（去掉增派查勘） -->
    <select id="getTotalAmount" resultType="com.paic.ncbs.claim.model.vo.other.ResultAmountVO">
        select sum(tc.duty_amount) dutyAmount
        from CLMS_POLICY_INFO ta
        inner join CLMS_POLICY_PLAN tb on ta.id_ahcs_policy_info =
        tb.id_ahcs_policy_info
        inner join CLMS_POLICY_DUTY tc on tb.id_ahcs_policy_plan =
        tc.id_ahcs_policy_plan
        where ta.report_no = #{reportNo,jdbcType=VARCHAR}
    </select>

    <select id="getReportTrackTaskCode" resultType="String">
        SELECT TASK_ID
        FROM (SELECT T.TASK_ID
        FROM CLMS_CASE_CLASS T
        WHERE T.REPORT_NO = #{reportNo}
        AND T.CASE_TIMES = #{caseTimes}
        <if test="status != null and status != '' ">
            and T.STATUS = #{status}
        </if>
        and T.TASK_ID != #{taskId}
        AND T.IS_EFFECTIVE = 'Y'
        order by t.CREATED_DATE desc) as temp
        limit 1
    </select>
    <select id="getCaseClassNameListCode" resultType="string">
        select t1.case_sub_class
        from CLMS_case_class t1, CLMS_case_class_define t2
        where t1.case_sub_class = t2.class_code
        and t1.report_no = #{reportNo}
        and t1.case_times = #{caseTimes}
        <if test="taskId != null and taskId != '' ">
            and t1.TASK_ID = #{taskId}
        </if>
        <if test="status != null and status != '' ">
            and t1.STATUS = #{status}
        </if>
        AND t1.IS_EFFECTIVE = 'Y'
        and t1.task_id =
        (select * from
        (select t.task_id from CLMS_case_class t where
        t.report_no = #{reportNo}
        and t.case_times = #{caseTimes}
        <if test="taskId != null and taskId != '' ">
            and t.TASK_ID = #{taskId}
        </if>
        <if test="status != null and status != '' ">
            and t.STATUS = #{status}
        </if>
        AND t.IS_EFFECTIVE = 'Y'
        order by t.created_date desc)
        as temp limit 1
        )
    </select>
    <select id="getCaseClassParentAll" resultType="java.lang.String">
        select distinct t2.parent_code
        from CLMS_case_class t1,
        CLMS_case_class_define t2
        where t1.case_sub_class = t2.class_code
        and t1.report_no =#{reportNo}
        and t1.case_times =#{caseTimes}
        AND t1.IS_EFFECTIVE = 'Y'
        and t1.STATUS=#{status}
        and t1.TASK_ID =#{taskId}
    </select>
</mapper>