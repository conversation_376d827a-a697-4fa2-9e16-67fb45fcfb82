<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.endcase.CaseClassDefineMapper">
	<resultMap type="com.paic.ncbs.claim.model.dto.endcase.CaseClassDefineDTO" id="result2">
		<id column="ID_AHCS_CASE_CLASS_DEFINE" property="caseClassDefineId"/>
		<result column="CLASS_CODE" property="classCode"/>
		<result column="CLASS_NAME" property="className"/>
		<result column="PARENT_CODE" property="parentCode"/>
		<collection property="subCaseClassDefines" ofType="com.paic.ncbs.claim.model.dto.endcase.SubCaseClassDefineDTO" column="CLASS_CODE" select="getSubCaseClass">
		</collection>
	</resultMap>
	
	<resultMap type="com.paic.ncbs.claim.model.dto.endcase.SubCaseClassDefineDTO" id="result1">
		<id column="ID_AHCS_CASE_CLASS_DEFINE" property="subCaseClassDefineId"/>
		<result column="CLASS_CODE" property="classCode"/>
		<result column="CLASS_NAME" property="className"/>
		<result column="PARENT_CODE" property="parentCode"/>
	</resultMap>
	
	<select id="getCaseClassDefines" resultMap="result2">
		select t.ID_AHCS_CASE_CLASS_DEFINE,
		       t.CLASS_CODE,
		       t.CLASS_NAME,
		       t.PARENT_CODE
		  from CLMS_case_class_define t
		 where t.PARENT_CODE = '0'
		 ORDER BY t.DISPLAY_NO
	</select>
	
	<select id="getSubCaseClass" parameterType="string" resultMap="result1">
		select t.ID_AHCS_CASE_CLASS_DEFINE,
		       t.CLASS_CODE,
		       t.CLASS_NAME,
		       t.PARENT_CODE
		  from CLMS_case_class_define t
		 where t.PARENT_CODE = #{classCode}
		 	   ORDER BY t.DISPLAY_NO
	</select>
	

	<select id="getSubCaseClassName" resultMap="result1">
		select ccd.CLASS_CODE,
		       ccd.CLASS_NAME
		  from CLMS_case_class_define ccd
		 where ccd.PARENT_CODE = '1'
		 ORDER BY ccd.DISPLAY_NO
	</select>
	
	<select id="getIdCaseClassDefine" parameterType="string" resultType="string">
		select t.ID_AHCS_CASE_CLASS_DEFINE
		  from CLMS_case_class_define t
		 where t.PARENT_CODE = '1' 
		       and t.CLASS_CODE = #{classCode}
	</select>
	
	<select id="getBigCaseClassBySub" resultType="string">
		select 
				distinct t2.class_name 
		from CLMS_case_class_define t1 ,
		clms_case_class_define t2
		where t1.class_code in 
		<foreach collection="subCaseClassList" index="index" item="item" open="(" separator="," close=")">
			 #{item}
		</foreach> 
		and t1.parent_code=t2.class_code
	</select>
	

	<select id="getCaseClassParentCodeByReport" resultType="string">
	    select t.parent_code 
	      from CLMS_case_class_define t
	     where t.class_code in(
                   select b.case_sub_class 
                     from CLMS_case_class b
                    where b.report_no = #{reportNo} 
                          and b.case_times = #{caseTimes}
						  AND b.IS_EFFECTIVE = 'Y'
                          and b.task_id =       
		                  (select r.task_id from 
		                      (select t.task_id from CLMS_case_class t
		                        where t.report_no = #{reportNo}
						              and t.case_times =  #{caseTimes}
							          and t.task_id in
							          <foreach collection="tacheCodeList" index="index" item="tacheCode" open="(" separator="," close=")">
									      #{tacheCode}
								      </foreach>
									  AND t.IS_EFFECTIVE = 'Y'
				                      order by t.created_date desc) r
				               as temp limit 1
		                )  
		            ) 
	</select>
	
</mapper>