<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.endcase.ClaimRejectionApprovalRecordEntityMapper">
  <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.ClaimRejectionApprovalRecordEntity">
    <id column="ID_AHCS_CASE_REGISTER_APPLY" jdbcType="VARCHAR" property="idAhcsCaseRegisterApply" />
    <result column="INITIATOR_UM" jdbcType="VARCHAR" property="initiatorUm" />
    <result column="APPLY_DATE" jdbcType="TIMESTAMP" property="applyDate" />
    <result column="AUDIT_UM" jdbcType="VARCHAR" property="auditUm" />
    <result column="AUDIT_DATE" jdbcType="TIMESTAMP" property="auditDate" />
    <result column="AUDIT_OPINION" jdbcType="VARCHAR" property="auditOpinion" />
    <result column="AUDIT_REMARK" jdbcType="VARCHAR" property="auditRemark" />
    <result column="REPORT_NO" jdbcType="VARCHAR" property="reportNo" />
    <result column="CASE_TIMES" jdbcType="DECIMAL" property="caseTimes" />
    <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
    <result column="UPDATED_DATE" jdbcType="TIMESTAMP" property="updatedDate" />
    <result column="ID_AHCS_VERIFY_CONCLUSION" jdbcType="VARCHAR" property="ahcsVerifyConclusionId" />
    <result column="AUDITING_COMMONT" jdbcType="VARCHAR" property="auditingCommont" />
    <result column="CONLUSION_CAUSE_CODE" jdbcType="VARCHAR" property="conclusionCauseCode" />
    <result column="CONLUSION_CAUSE_DESC" jdbcType="VARCHAR" property="conclusionCauseDesc" />
  </resultMap>

  <sql id="Base_Column_List">
    ID_AHCS_CASE_REGISTER_APPLY, INITIATOR_UM, APPLY_DATE, AUDIT_UM, AUDIT_DATE, AUDIT_OPINION, 
    AUDIT_REMARK, REPORT_NO, CASE_TIMES, UPDATED_BY, UPDATED_DATE, ID_AHCS_VERIFY_CONCLUSION
  </sql>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from clms_claim_rejection_approval_record
    where ID_AHCS_CASE_REGISTER_APPLY = #{idAhcsCaseRegisterApply,jdbcType=VARCHAR}
  </delete>

  <insert id="insert" keyColumn="ID_AHCS_CASE_REGISTER_APPLY" keyProperty="idAhcsCaseRegisterApply"
          parameterType="com.paic.ncbs.claim.dao.entity.ClaimRejectionApprovalRecordEntity" useGeneratedKeys="true">
    insert into clms_claim_rejection_approval_record (INITIATOR_UM, APPLY_DATE, AUDIT_UM, 
      AUDIT_DATE, AUDIT_OPINION, AUDIT_REMARK, 
      REPORT_NO, CASE_TIMES, UPDATED_BY, 
      UPDATED_DATE,ID_AHCS_CASE_REGISTER_APPLY,
      ID_AHCS_VERIFY_CONCLUSION)
    values (#{initiatorUm,jdbcType=VARCHAR}, now(), #{auditUm,jdbcType=VARCHAR},
      #{auditDate,jdbcType=TIMESTAMP}, #{auditOpinion,jdbcType=VARCHAR}, #{auditRemark,jdbcType=VARCHAR}, 
      #{reportNo,jdbcType=VARCHAR}, #{caseTimes,jdbcType=DECIMAL}, #{updatedBy,jdbcType=VARCHAR}, 
      now(),REPLACE(UUID(),'-',''),
      #{ahcsVerifyConclusionId})
  </insert>

  <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.ClaimRejectionApprovalRecordEntity">
    update clms_claim_rejection_approval_record
    <set>
      <if test="auditOpinion != null">
        AUDIT_OPINION = #{auditOpinion,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedDate != null">
        UPDATED_DATE = now(),
      </if>
      <if test=" auditUm != null">
        AUDIT_UM = #{auditUm,jdbcType=VARCHAR},
      </if>
      AUDIT_DATE =now() ,
      <if test=" auditRemark != null">
        AUDIT_REMARK = #{auditRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where ID_AHCS_CASE_REGISTER_APPLY = #{idAhcsCaseRegisterApply,jdbcType=VARCHAR}
  </update>

  <select id="selectPendingRecord" resultMap="BaseResultMap">
    SELECT r.ID_AHCS_CASE_REGISTER_APPLY,
           r.INITIATOR_UM,
           r.APPLY_DATE,
           r.AUDIT_UM,
           r.AUDIT_DATE,
           r.AUDIT_OPINION,
           r.AUDIT_REMARK,
           r.REPORT_NO,
           r.CASE_TIMES,
           r.UPDATED_BY,
           r.UPDATED_DATE,
           r.ID_AHCS_VERIFY_CONCLUSION,
           v.AUDITING_COMMONT,
           v.CONLUSION_CAUSE_CODE,
           v.CONLUSION_CAUSE_DESC
    FROM clms_claim_rejection_approval_record r
    LEFT JOIN clms_verify_conclusion v ON r.ID_AHCS_VERIFY_CONCLUSION = v.ID_AHCS_VERIFY_CONCLUSION
    WHERE r.REPORT_NO = #{reportNo} AND r.CASE_TIMES = #{caseTimes} AND AUDIT_OPINION IS NULL
    LIMIT 1
  </select>

  <select id="selectAgreedRecord" resultMap="BaseResultMap">
    SELECT r.ID_AHCS_CASE_REGISTER_APPLY,
    r.INITIATOR_UM,
    r.APPLY_DATE,
    r.AUDIT_UM,
    r.AUDIT_DATE,
    r.AUDIT_OPINION,
    r.AUDIT_REMARK,
    r.REPORT_NO,
    r.CASE_TIMES,
    r.UPDATED_BY,
    r.UPDATED_DATE,
    r.ID_AHCS_VERIFY_CONCLUSION,
    v.AUDITING_COMMONT,
    v.CONLUSION_CAUSE_CODE,
    v.CONLUSION_CAUSE_DESC
    FROM clms_claim_rejection_approval_record r
    LEFT JOIN clms_verify_conclusion v ON r.ID_AHCS_VERIFY_CONCLUSION = v.ID_AHCS_VERIFY_CONCLUSION
    WHERE r.REPORT_NO = #{reportNo} AND r.CASE_TIMES = #{caseTimes} AND AUDIT_OPINION = '1'
    LIMIT 1
  </select>

  <select id="selectByReportNo" resultMap="BaseResultMap">
    SELECT r.ID_AHCS_CASE_REGISTER_APPLY,
           r.INITIATOR_UM,
           r.APPLY_DATE,
           r.AUDIT_UM,
           r.AUDIT_DATE,
           r.AUDIT_OPINION,
           r.AUDIT_REMARK,
           r.REPORT_NO,
           r.CASE_TIMES,
           r.UPDATED_BY,
           r.UPDATED_DATE,
           r.ID_AHCS_VERIFY_CONCLUSION,
           v.AUDITING_COMMONT,
           v.CONLUSION_CAUSE_CODE,
           v.CONLUSION_CAUSE_DESC
    FROM clms_claim_rejection_approval_record r
    LEFT JOIN clms_verify_conclusion v ON r.ID_AHCS_VERIFY_CONCLUSION = v.ID_AHCS_VERIFY_CONCLUSION
    WHERE r.REPORT_NO = #{reportNo} AND r.CASE_TIMES = #{caseTimes}
    ORDER BY APPLY_DATE
  </select>

</mapper>