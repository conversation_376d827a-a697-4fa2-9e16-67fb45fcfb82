<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.endcase.CaseProcessMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO" id="caseProcessDTO">
        <result property="createdBy" column="CREATED_BY"/>
        <result property="createdDate" column="CREATED_DATE"/>
        <result property="updatedBy" column="UPDATED_BY"/>
        <result property="updatedDate" column="UPDATED_DATE"/>
        <result property="idAhcsCaseProcess" column="ID_AHCS_CASE_PROCESS"/>
        <result property="reportNo" column="REPORT_NO"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="caseNo" column="CASE_NO"/>
        <result property="processStatus" column="PROCESS_STATUS"/>
        <result property="caseStatus" column="CASE_STATUS" />
        <result property="processStatusName" column="PROCESS_STATUS_NAME"/>
        <result property="endCaseDate" column="END_CASE_DATE"/>
        <result property="commissionCompany" column="COMMISSION_COMPANY"/>
        <result property="commissionDate" column="COMMISSION_DATE"/>
        <result property="companyCode" column="COMPANY_CODE"/>
        <result property="commissionUm" column="COMMISSION_UM"/>
        <result property="companyName" column="department_abbr_name"/>
        <result property="registerDeptCode" column="registerDeptCode"/>
        <result property="archiveTime" column="ARCHIVE_TIME"/>
        <result property="isNewProcess" column="IS_NEW_PROCESS"/>
        <result property="privilegeGroupName" column="privilege_group_name"/>
        <result property="coInsureRatio" column="COINSURE_RATIO"/>
        <result column="reportCase" property="reportCase"/>
    </resultMap>

    <insert id="addCaseProcess" parameterType="com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO">
		INSERT INTO CLMS_case_process(
		CREATED_BY,
		CREATED_DATE,
		UPDATED_BY,
		UPDATED_DATE,
		ID_AHCS_CASE_PROCESS,
		REPORT_NO,
		CASE_TIMES,
		PROCESS_STATUS,
		COMMISSION_COMPANY,
		COMMISSION_DATE,
		COMPANY_CODE,
		COMMISSION_UM,
        IS_NEW_PROCESS,
		ARCHIVE_TIME
		)VALUES(
		'system',
		now(),
		'system',
		now(),
		replace(uuid(),'-',''),
		#{reportNo},
		#{caseTimes},
		#{processStatus},
		#{commissionCompany,jdbcType=VARCHAR},
		#{commissionDate,jdbcType=TIMESTAMP},
		#{companyCode,jdbcType=VARCHAR},
		#{commissionUm,jdbcType=VARCHAR},
        #{isNewProcess,jdbcType=VARCHAR},
		now()
		)
	</insert>

    <update id="updateCaseProcessByReportNo" parameterType="com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO">
        update CLMS_case_process cp
        <trim prefix="set" suffixOverrides=",">
            cp.UPDATED_DATE = sysdate(),
            <if test="processStatus != null ">
                cp.PROCESS_STATUS =#{processStatus},
            </if>
            <if test="commissionCompany != null ">
                cp.COMMISSION_COMPANY = #{commissionCompany },
            </if>
            <if test="commissionUm != null ">
                cp.COMMISSION_UM = #{commissionUm },
            </if>
            <if test="commissionDate != null ">
                cp.COMMISSION_DATE = #{commissionDate,jdbcType=TIMESTAMP},
            </if>
            <if test="companyCode != null ">
                cp.company_code = #{companyCode},
            </if>
 		   <if test="caseStatus != null ">
				cp.case_status = #{caseStatus},
		   </if>           
        </trim>
        where cp.REPORT_NO = #{reportNo}
        and cp.CASE_TIMES = #{caseTimes}
    </update>

    <select id="getCaseProcess" resultType="int">
		select count(ID_AHCS_CASE_PROCESS) from CLMS_case_process cp
		where cp.REPORT_NO = #{reportNo}
		and cp.CASE_TIMES = #{caseTimes}
	</select>


    <select id="getCheckLossCaseProcess" resultType="int">
		select count(ID_AHCS_CASE_PROCESS) from CLMS_case_process cp
		where cp.REPORT_NO = #{reportNo}
		and cp.CASE_TIMES = #{caseTimes}
		and (cp.PROCESS_STATUS='04' or cp.PROCESS_STATUS='05')
	</select>

    <select id="getCaseProcessDTO" resultMap="caseProcessDTO">
        SELECT cp.UPDATED_BY,cp.CREATED_BY,cp.UPDATED_DATE,cp.CREATED_DATE,cp.ID_AHCS_CASE_PROCESS,
        cp.report_no,
        cp.case_times,
        cp.PROCESS_STATUS,
        cp.COMMISSION_COMPANY,
        cp.COMMISSION_DATE,
        cp.COMPANY_CODE,
        cp.CASE_STATUS,
        cp.privilege_group_name,
        (select c.value_chinese_name from clm_common_parameter c where cp.process_status = c.value_code and c.collection_code = 'CASE_PROCESS') as PROCESS_STATUS_NAME,
        (SELECT DD.DEPARTMENT_ABBR_NAME FROM DEPARTMENT_DEFINE DD WHERE DD.DEPARTMENT_CODE = ifnull(cp.commission_company,CP.COMPANY_CODE) limit 1 ) department_abbr_name,
        (select case_no from clm_case_base cb where cb.report_no = cp.report_no and cb.case_times = cp.case_times limit 1) case_no,
        (select policy_no from clm_case_base cb where cb.report_no = cp.report_no and cb.case_times = cp.case_times limit 1) policy_no,
        (select end_case_date from clm_case_base cb where cb.report_no = cp.report_no and cb.case_times = cp.case_times limit 1) end_case_date,
        (select co.reinsure_scale from CLMS_coinsure co where co.accept_insurance_flag = '1'
        and (co.REINSURE_COMPANY_CODE = '3005'  or exists(select d.department_level from department_define d where d.department_code = co.REINSURE_COMPANY_CODE))
        and co.id_ahcs_policy_info = (select id_ahcs_policy_info from CLMS_policy_info pii where pii.case_no = b.case_no limit 1) and co.coinsurance_type = '0') COINSURE_RATIO
        FROM CLMS_case_process cp left join clm_case_base b on cp.report_no = b.report_no and cp.case_times = b.case_times
        where  cp.case_times = #{caseTimes}
        <if test="reportNo != null and reportNo != '' ">
            and cp.report_no =#{reportNo}
        </if>
        <if test="caseNo != null and caseNo != '' ">
            and b.case_no = #{caseNo, jdbcType=VARCHAR}
        </if>
        limit 1;
    </select>
    <select id="caseProcessDTOCondition" resultMap="caseProcessDTO">
        SELECT cp.UPDATED_BY,cp.CREATED_BY,cp.UPDATED_DATE,cp.CREATED_DATE,cp.ID_AHCS_CASE_PROCESS,
        cp.report_no,
        cp.case_times,
        cp.PROCESS_STATUS,
        cp.COMMISSION_COMPANY,
        cp.COMMISSION_DATE,
        cp.COMPANY_CODE,
        cp.privilege_group_name,
        (select c.value_chinese_name from clm_common_parameter c where cp.process_status = c.value_code and c.collection_code = 'CASE_PROCESS') as PROCESS_STATUS_NAME,
        (SELECT DD.DEPARTMENT_ABBR_NAME FROM DEPARTMENT_DEFINE DD WHERE DD.DEPARTMENT_CODE = cp.commission_company) department_abbr_name,
        (select case_no from clm_case_base cb where cb.report_no = cp.report_no and cb.case_times = cp.case_times limit 1) case_no,
        (select policy_no from clm_case_base cb where cb.report_no = cp.report_no and cb.case_times = cp.case_times limit 1) policy_no,
        (select end_case_date from clm_case_base cb where cb.report_no = cp.report_no and cb.case_times = cp.case_times limit 1) end_case_date,
        (select co.reinsure_scale from CLMS_coinsure co where co.accept_insurance_flag = '1'
        and (co.REINSURE_COMPANY_CODE = '3005'  or exists(select d.department_level from department_define d where d.department_code = co.REINSURE_COMPANY_CODE))
        and co.id_ahcs_policy_info = (select id_ahcs_policy_info from CLMS_policy_info pii where pii.case_no = b.case_no limit 1) and co.coinsurance_type = '0') COINSURE_RATIO
        FROM CLMS_case_process cp left join clm_case_base b on cp.report_no = b.report_no and cp.case_times = b.case_times
        where  cp.case_times = #{caseTimes}
        <if test="policyNo != null and policyNo != '' ">
            and b.policy_no = #{policyNo, jdbcType=VARCHAR}
        </if>
        <if test="caseNo != null and caseNo != '' ">
            and b.case_no = #{caseNo, jdbcType=VARCHAR}
        </if>
        limit 1
    </select>
    <select id="getCompanyCodeAndName" resultMap="caseProcessDTO">
		 select cp.COMPANY_CODE,
           (select dd.department_abbr_name from department_define dd
		       where cp.COMPANY_CODE = dd.department_code) as department_abbr_name
		 from CLMS_case_process cp
		where cp.REPORT_NO = #{reportNo}
		  and cp.CASE_TIMES = #{caseTimes}
	</select>


    <select id="getCaseCompanyCode" resultMap="caseProcessDTO">
	   select cp.ARCHIVE_TIME,
		      cp.COMPANY_CODE,
		      cp.IS_NEW_PROCESS,
		      cp.COMMISSION_UM
		 from CLMS_CASE_PROCESS cp
		where cp.REPORT_NO = #{reportNo}
		  and cp.CASE_TIMES = #{caseTimes}
		  limit 1
	</select>


    <select id="getCaseCommissionInfo" resultMap="caseProcessDTO">
	   select cp.REPORT_NO,
              cp.CASE_TIMES,
	          cp.ARCHIVE_TIME,
	          cp.COMPANY_CODE,
		      cp.COMMISSION_UM,
		      cp.COMMISSION_COMPANY,
		      cp.PROCESS_STATUS,
		      cp.COMMISSION_DATE
		 from CLMS_CASE_PROCESS cp
		where cp.REPORT_NO = #{reportNo}
		  and cp.CASE_TIMES = #{caseTimes}
		  limit 1
	</select>

    <select id="getCaseByReportNo" resultMap="caseProcessDTO">
	   select cp.report_no,
              cp.case_times,
		      cp.process_status
		 from CLMS_CASE_PROCESS cp
		where cp.report_no = #{reportNo}
        order by cp.case_times asc
	</select>


    <insert id="addCaseProcessDTO" parameterType="com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO">
		INSERT INTO CLMS_case_process(
		CREATED_BY,
		CREATED_DATE,
		UPDATED_BY,
		UPDATED_DATE,
		ID_AHCS_CASE_PROCESS,
		REPORT_NO,
		CASE_TIMES,
		PROCESS_STATUS,
		COMMISSION_COMPANY,
		COMMISSION_DATE,
		COMPANY_CODE,
		COMMISSION_UM,
        IS_NEW_PROCESS,
		ARCHIVE_TIME
		)VALUES(
		#{userId},
		#{createdDate,jdbcType=TIMESTAMP},
		#{userId},
		#{updatedDate,jdbcType=TIMESTAMP},
		replace(uuid(),'-',''),
		#{reportNo},
		#{caseTimes},
		#{processStatus},
		#{commissionCompany,jdbcType=VARCHAR},
		#{commissionDate,jdbcType=TIMESTAMP},
		#{companyCode,jdbcType=VARCHAR},
		#{commissionUm,jdbcType=VARCHAR},
        #{isNewProcess,jdbcType=VARCHAR},
		#{archiveTime,jdbcType=TIMESTAMP}
		)
	</insert>


    <update id="updateRegisterDept" parameterType="com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO">
        update CLMS_case_process cp
        <trim prefix="set" suffixOverrides=",">
            cp.UPDATED_DATE=now(),
            <if test="registerDeptCode != null ">
                cp.register_dept_code=#{registerDeptCode, jdbcType=VARCHAR},
            </if>
        </trim>
        where cp.report_no=#{reportNo, jdbcType=VARCHAR}
        and cp.case_times=#{caseTimes, jdbcType=INTEGER}
    </update>


    <select id="getCaseProcessStatus" resultType="string">
	   select cp.PROCESS_STATUS
		 from CLMS_case_process cp
		where cp.REPORT_NO = #{reportNo}
			  and cp.CASE_TIMES = #{caseTimes}
			  limit 1;
	</select>

    <select id="getCaseProcessStatusNew" resultType="string">
	   select cp.PROCESS_STATUS
		 from CLMS_case_process cp
		where cp.REPORT_NO = #{reportNo}
	    order by cp.CREATED_DATE desc
			  limit 1;
	</select>


    <select id="getAllEndCase" resultMap="caseProcessDTO">
	   select t.report_no,
	   		  t.case_times
	   from CLMS_case_process t
	   where t.process_status='06'
        limit 1000
	</select>

    <select id="getEndCaseNoPriv" resultMap="caseProcessDTO">
	   select t.report_no,
	   		  t.case_times
	   from CLMS_case_process t
	   where t.process_status='06'
	         AND t.PRIVILEGE_GROUP_NAME IS NULL
	        limit 1000
	</select>

    <update id="updatePrivilegeGroupName" parameterType="com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO">
		update CLMS_case_process cp
			SET  cp.UPDATED_DATE=now(),
				cp.PRIVILEGE_GROUP_NAME=#{privilegeGroupName, jdbcType=VARCHAR}
	    where cp.report_no=#{reportNo, jdbcType=VARCHAR}
           and cp.case_times=#{caseTimes, jdbcType=INTEGER}
	</update>

    <select id="getReportDate" resultType="java.util.Date">
		select report_date from clm_report_info t
		where t.report_no=#{reportNo, jdbcType=VARCHAR}
		limit 1
	</select>


    <select id="getArchiveTimeByReportNo" resultType="java.util.Date">
		select cp.ARCHIVE_TIME
		  from CLMS_case_process cp
		where cp.report_no = #{reportNo, jdbcType=VARCHAR}
              and cp.case_times = #{caseTimes, jdbcType=INTEGER}
              limit 1
	</select>


    <select id="getReportProcessStatus" resultMap="caseProcessDTO">
        SELECT
	        cp.PROCESS_STATUS,
	        (select c.value_chinese_name from clm_common_parameter c where c.value_code= cp.process_status and c.collection_code = 'CASE_PROCESS') as PROCESS_STATUS_NAME
        FROM CLMS_case_process cp
        where cp.report_no = #{reportNo}
              and cp.case_times = #{caseTimes}
              limit 1
	</select>


    <select id="getCaseProcessStatusMc" parameterType="string" resultMap="caseProcessDTO">
        SELECT
        concat(cp.REPORT_NO,'_',cp.CASE_TIMES) as reportCase,
        cp.REPORT_NO,
        cp.CASE_TIMES,
        cp.PROCESS_STATUS,
        (select c.value_chinese_name from clm_common_parameter c where c.value_code = cp.process_status and c.collection_code = 'CASE_PROCESS') as PROCESS_STATUS_NAME
        FROM CLMS_case_process cp
        where cp.report_no =#{reportNo}
        <if test="taskDefinitionKey == 'AHCS_MULTI_CLAIM_APPLY_VERIFY' or taskDefinitionKey == 'AHCS_MULTI_CLAIM_APPLY_HQ_VERIFY' ">
            and cp.CASE_TIMES = (ifnull((select c.case_times from CLMS_case_process c where c.REPORT_NO = cp.report_no and c.case_times=cp.case_times limit 1), cp.CASE_TIMES-1))
        </if>
        <if test="taskDefinitionKey == 'AHCS_CHASE_VERIFY' ">
            and cp.CASE_TIMES = (select (case when ca.VERIFY_OPTIONS='1' then ca.CASE_TIMES when ca.VERIFY_OPTIONS = '2' then ca.CASE_TIMES-1 end ) from CLMS_chase_apply ca where ca.ID_AHCS_CHASE_APPLY = #{businessKey,jdbcType=VARCHAR} limit 1)
        </if>
        limit 1
    </select>


    <select id="getFixCaseList" resultMap="caseProcessDTO">
		select  a.REPORT_NO,
				a.CASE_TIMES,
				b.ID_AHCS_CASE_PROCESS,
				b.COMPANY_CODE
		  from CLMS_fix_history_data a,
		  	   CLMS_case_process b
		 where a.report_no=b.report_no
		   and a.case_times=b.case_times
		   and a.batch_no=#{batchNo, jdbcType=VARCHAR}
		   and b.commission_company is null
		   and a.status='0'
	</select>


    <update id="batchModifyCaseProcessDTO">
        <foreach collection="paramList" item="item" open="begin" close=";end;" separator=";">
            update CLMS_case_process cp
            <trim prefix="set" suffixOverrides=",">
                cp.UPDATED_DATE = now(),
                <if test="item.processStatus != null ">
                    cp.PROCESS_STATUS =#{item.processStatus},
                </if>
                <if test="item.commissionCompany != null ">
                    cp.COMMISSION_COMPANY = #{item.commissionCompany },
                </if>
                <if test="item.commissionUm != null ">
                    cp.COMMISSION_UM = #{item.commissionUm },
                </if>
                <if test="item.commissionDate != null ">
                    cp.COMMISSION_DATE = #{item.commissionDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.companyCode != null ">
                    cp.company_code = #{item.companyCode},
                </if>
            </trim>
            where cp.REPORT_NO = #{item.reportNo}
            and cp.CASE_TIMES = #{item.caseTimes}
        </foreach>
    </update>

    <select id="getHisCaseList" resultMap="caseProcessDTO">
		select  a.REPORT_NO,
				a.CASE_TIMES
		 from CLMS_fix_history_data a where
		   	 a.batch_no=#{batchNo, jdbcType=VARCHAR}
			 and a.status='0'
	</select>

    <update id="updateFixHistoryData">
			update CLMS_fix_history_data a  set
					a.UPDATED_DATE = now(),
					a.STATUS ='1'
			where a.REPORT_NO = #{reportNo}
			and a.CASE_TIMES = #{caseTimes}
			and a.batch_no=#{batchNo, jdbcType=VARCHAR}
	</update>

    <select id="getListByReportNoAndCaseTimesList" parameterType="java.util.List" resultMap="caseProcessDTO">
        select
        cp.company_code,
        ( select d.department_abbr_name from department_define d where cp.COMPANY_CODE = d.department_code )
        department_abbr_name,
        cp.report_no,
        cp.case_times
        from
        CLMS_case_process cp
        where
        cp.company_code is not null
        and
        (cp.report_no,cp.case_times) in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            (#{item.reportNo},#{item.caseTimes})
        </foreach>
    </select>


    <update id="updateClaimCaseTypeById">
		update CLMS_case_process cp set updated_date=now(),cp.claim_case_type = #{claimCaseType, jdbcType=VARCHAR} where cp.ID_AHCS_CASE_PROCESS = #{idAhcsCaseProcess, jdbcType=VARCHAR}
	</update>

    <select id="requestAllCase" parameterType="com.paic.ncbs.claim.model.vo.endcase.WorkLoadQueryVO"
            resultType="com.paic.ncbs.claim.model.vo.endcase.CaseLoadVO">
        select
        c.report_type reportType,
        c.report_no reportNo,
        c.report_date reportDate,
        x.case_times caseTimes
        from
        CLM_REPORT_INFO c,
        CLM_WHOLE_CASE_BASE x
        where
        c.report_date
        between
        str_to_date(concat(#{beginDateStr,jdbcType=VARCHAR},' 00:00:00'), '%Y-%m-%d %H:%i:%s')
        and
        str_to_date(concat(#{endDateStr,jdbcType=VARCHAR},' 23:59:59'), '%Y-%m-%d %H:%i:%s')
        and c.report_no = x.report_no
        and x.case_type !='06'
        <if test="reportType != null">
            and c.report_type = #{reportType,jdbcType=VARCHAR}
        </if>
        AND EXISTS (SELECT 1
        FROM CLMS_CASE_PROCESS acp
        WHERE acp.REPORT_NO = c.REPORT_NO)

    </select>


    <select id="getCompanyCodeByReportNo" resultType="string">
	   select cp.COMPANY_CODE
		 from CLMS_CASE_PROCESS cp
		where cp.REPORT_NO = #{reportNo}
		  and cp.COMPANY_CODE is not null
		  limit 1
	</select>

    <select id="getIsNewProcess" resultType="string">
        select cp.IS_NEW_PROCESS
        from CLMS_case_process cp
        where cp.REPORT_NO = #{reportNo}
        and cp.CASE_TIMES = #{caseTimes}
        limit 1
    </select>

    <update id="updateClaimCaseType" parameterType="com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO">
		update CLMS_case_process t
		set t.updated_date = now(),
		t.updated_by = #{updatedBy, jdbcType=VARCHAR},
		t.claim_case_type = #{claimCaseType, jdbcType=VARCHAR}
		where t.report_no = #{reportNo, jdbcType=VARCHAR}
		and t.case_times = #{caseTimes, jdbcType=INTEGER}
	</update>

    <update id="updateCaseStatus">
		update CLMS_case_process t
		set t.updated_date = now(),
		t.updated_by = 'SYSTEM',
		t.case_status = #{caseStatus, jdbcType=VARCHAR}
		where t.report_no = #{reportNo, jdbcType=VARCHAR}
		and t.case_times = #{caseTimes, jdbcType=INTEGER}
	</update>


    <select id="getTrialDepartment" resultType="String">
		select t.department_code
		from clm_department_switch t
		where t.system_flag = 'AHCS'
		and t.department_code = #{deptCode, jdbcType=VARCHAR}
	</select>

    <select id="queryArchiveTimeByReportNoAndCaseTimes" resultType="Date">
        select ARCHIVE_TIME
        from CLMS_case_process t
        where t.REPORT_NO=#{reportNo ,jdbcType=VARCHAR}
        and t.CASE_TIMES=#{caseTimes ,jdbcType=NUMERIC}
    </select>


    <select id="getAllTrialDepartment" resultType="String">
        select t.department_code
        from clm_department_switch t
        where t.system_flag = 'AHCS'
        and t.department_code in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>


<!--    <select id="getTrialDepartmentList" resultType="DepartmentDTO">-->
<!--        select t.department_code code,-->
<!--             (select tt.department_abbr_name from department_define tt where tt.department_code = t.department_code limit 1) name,-->
<!--             concat(t.department_code,'-' , (select tt.department_abbr_name from department_define tt where tt.department_code = t.department_code limit 1)) showName-->
<!--        from clm_department_switch t where t.system_flag = 'AHCS' order by t.department_code-->
<!--    </select>-->


    <select id="getEndCaseDateByCaseProcess" resultType="java.util.Date">
	   select ifnull((select ifnull(b.end_case_date, cp.updated_date)
	             from clm_whole_case_base b where b.report_no=cp.report_no and b.case_times=cp.case_times limit 1),cp.updated_date) END_CASE_DATE
		 from CLMS_case_process cp
		where cp.REPORT_NO = #{reportNo}
			  and cp.CASE_TIMES = #{caseTimes}
			  and cp.process_status in('06','07','08')
			  limit 1
	</select>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.CaseReopenCopyDTO">
        INSERT INTO CLMS_CASE_PROCESS (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_AHCS_CASE_PROCESS,
            REPORT_NO,
            CASE_TIMES,
            PROCESS_STATUS,
            COMMISSION_COMPANY,
            COMMISSION_DATE,
            COMPANY_CODE,
            REGISTER_DEPT_CODE,
            COMMISSION_UM,
            PRIVILEGE_GROUP_NAME,
            ARCHIVE_TIME,
            CLAIM_CASE_TYPE,
            IS_NEW_PROCESS
        )
        SELECT
            #{userId},
            NOW(),
            #{userId},
            NOW(),
            MD5(UUID()),
            REPORT_NO,
            #{reopenCaseTimes},
            #{processStatus},
            COMMISSION_COMPANY,
            COMMISSION_DATE,
            COMPANY_CODE,
            REGISTER_DEPT_CODE,
            COMMISSION_UM,
            PRIVILEGE_GROUP_NAME,
            NOW(),
            CLAIM_CASE_TYPE,
            IS_NEW_PROCESS
        FROM CLMS_CASE_PROCESS
        WHERE REPORT_NO=#{reportNo}
        AND CASE_TIMES=#{caseTimes}
    </insert>

    <select id="getCaseProcessInfo"  resultType="com.paic.ncbs.claim.model.dto.endcase.CaseProcessDTO">
        select REPORT_NO reportNo, CASE_TIMES caseTimes, PROCESS_STATUS processStatus from clms_case_process
        where REPORT_NO=#{reportNo}
        and case_times=#{caseTimes}
    </select>
</mapper>