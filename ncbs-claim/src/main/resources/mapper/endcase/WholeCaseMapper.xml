<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseMapper">

    <resultMap type="com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO" id="wholeCaseVO">
        <result property="reportNo" column="report_no"/>
        <result property="caseTimes" column="case_times"/>
        <result property="reportDate" column="report_date"/>
        <result property="accidentDate" column="accident_date"/>
        <result property="name" column="name"/>
        <result property="clientType" column="client_type"/>
        <result property="clientTypeName" column="client_type_name"/>
        <result property="processStatusName" column="processStatusName"/>
        <result property="processStatus" column="process_status"/>
        <result property="indemnityConclusion" column="indemnity_conclusion"/>
        <result property="indemnityModel" column="indemnity_model"/>
        <result property="isRegister" column="IS_REGISTER"/>
        <result property="endCaseFlag" column="whole_case_status"/>
        <result property="endCaseDate" column="end_case_date"/>
        <result property="verifyUm" column="VERIFY_UM"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.policy.PolicyHistoryDTO" id="policyHistoryDTO">
        <result property="reportNo" column="report_no"/>
        <result property="policyNo" column="policy_no"/>
        <result property="policyCerNo" column="POLICY_CER_NO"/>
        <result property="reportDate" column="report_date"/>
        <result property="accidentTime" column="accident_time"/>
        <result property="accidentPlace" column="accident_place"/>
        <result property="reporterName" column="reporter_name"/>
        <result property="reporterCallNo" column="reporter_call_no"/>
        <result property="endCaseDate" column="end_case_date"/>
        <result property="verifyUm" column="verify_um"/>
        <result property="policySumPay" column="policy_sum_pay"/>
        <result property="processStatus" column="process_status"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.vo.endcase.CaseParamVO" id="caseParamMap">
        <result property="reportNo" column="report_no"/>
        <result property="caseTimes" column="case_times"/>
    </resultMap>

    <select id="getCaseTimesByReportNo" resultType="java.lang.Integer">
    	select max(case_Times)
		as case_times from
		clm_whole_case_base where report_no = #{reportNo,jdbcType=VARCHAR}
    </select>

    <select id="getWholeCaseListByReportNo" resultMap="wholeCaseVO">
        select
        t.report_no,
        t.case_times,
        t.IS_REGISTER,
        t.indemnity_model,
        t.VERIFY_UM
        from clm_whole_case_base t
        where 1=1
        <if test="reportNo != null and reportNo != '' ">
            and t.report_no = #{reportNo,jdbcType=VARCHAR}
        </if>
        <if test="caseTimes != null and caseTimes != '' ">
            and t.case_Times = #{caseTimes,jdbcType=NUMERIC}
        </if>
    </select>

    <select id="getCaseTypeByReportNo" resultType="java.lang.String">
    	SELECT CASE_TYPE caseType FROM CLM_WHOLE_CASE_BASE WHERE  REPORT_NO=#{reportNo} AND CASE_TIMES= #{caseTimes}
    </select>

    <update id="modifyDocumentStatus">
        UPDATE CLM_WHOLE_CASE_BASE_EX SET UPDATED_DATE=NOW(),UPDATED_BY=#{userId},
        DOCUMENT_RECALL_STATUS=#{status} WHERE REPORT_NO=#{reportNo} AND CASE_TIMES=#{caseTimes}
    </update>

    <update id="modifyIsSelfHelp">
        UPDATE CLM_WHOLE_CASE_BASE_EX SET UPDATED_DATE=NOW(),UPDATED_BY=#{userId},
        IS_SELF_HELP=#{isSelfHelp} WHERE REPORT_NO=#{reportNo} AND CASE_TIMES=#{caseTimes}
    </update>

    <select id="isSelfCase" resultType="java.lang.String">
      select wc.report_no
        from clm_whole_case_base wc
       where wc.case_type = '05'
		 and wc.report_no = #{reportNo}
		 limit 1
    </select>

    <select id="isPostponeAwait" resultType="int">
      select count(*)
	  from clm_whole_case_base_ex ex
	 where ex.report_no = #{reportNo}
	   and ex.case_times = #{caseTimes}
	   and ex.document_recall_status = '01'
    </select>

    <select id="getWhleCaseVoByReportNo" resultMap="wholeCaseVO"
            parameterType="com.paic.ncbs.claim.model.vo.endcase.CaseParamVO">
		select t.report_no,
   		t.case_times,
        t.indemnity_conclusion,
        t.indemnity_model,
        t.whole_case_status,
        t.end_case_date
		from clm_whole_case_base t
		where t.report_no = #{reportNo}
		and t.case_times = #{caseTimes}
    </select>

    <select id="getReportRegisterTel" parameterType="String" resultType="String">
    	SELECT T.REPORTER_REGISTER_TEL FROM CLM_REPORT_INFO T WHERE T.REPORT_NO = #{reportNo}
    </select>

    <select id="getClientNo" resultType="String">
        select h.client_no from CLMS_report_customer h  where h.report_no=#{reportNo,jdbcType=VARCHAR} limit 1
    </select>

    <select id="getPolicyHistory" parameterType="String" resultMap="policyHistoryDTO">
        select cb.report_no || '_' || cb.case_times  report_no,
               pi.policy_no,
               pi.policy_cer_no,
               ri.report_date,
               (select pa.accident_time
                from CLMS_person_accident pa
                where pa.report_no = cb.report_no
                  and pa.case_times = 1
                  and pa.task_id = 'report1'
                  AND PA.IS_EFFECTIVE = 'Y')         accident_time,
               (select pa.accident_place
                from CLMS_person_accident pa
                where pa.report_no = cb.report_no
                  and pa.case_times = 1
                  and pa.task_id = 'report1'
                  AND PA.IS_EFFECTIVE = 'Y')         accident_place,
               ri.reporter_name,
               ri.reporter_call_no,
               cb.end_case_date,
               cb.verify_um,
               (select pp.policy_sum_pay
                from clm_policy_pay pp
                where pp.report_no =
                      cb.report_no
                  and pp.case_times = cb.case_times
                  and pp.policy_no = pi.policy_no
                  and pp.case_no = pi.case_no)       policy_sum_pay,
               (select case cp.process_status
                           when '01' then '待立案'
                           when '02' then '待收单'
                           when '03' then '待审核'
                           when '04' then '审核中'
                           when '05' then '待结案'
                           when '06' then '已结案'
                           when '07' then '零结案'
                           when '08' then '已注销'
                           when '09' then '补录账户'
                           end process_status
                from CLMS_case_process cp
                where cp.report_no = cb.report_no
                  and cp.case_times = cb.case_times) process_status
        from CLMS_report_customer rc,
             clm_report_info ri,
             CLMS_policy_info pi,
             clm_whole_case_base cb
        where rc.client_no = #{clientNo}
          and rc.report_no = ri.report_no
          and rc.report_no = pi.report_no
          and rc.report_no = cb.report_no
        order by ri.report_date desc, cb.report_no, pi.policy_no
    </select>

    <select id="getCustomerAccidentCount" resultType="java.lang.Integer">
        select h.accident_count
        from CLMS_customer_report_record h
        where h.client_no=#{clientNo,jdbcType=VARCHAR}
        limit 1
    </select>

    <select id="getReportListByCaseNo" resultMap="caseParamMap">
        select PP.report_no, PP.case_times from clm_case_base pp where pp.case_no = #{caseNo,jdbcType=VARCHAR}
    </select>

    <select id="getCaseWithCondition" resultMap="caseParamMap">
        select a.report_no,
        a.case_times
        from CLMS_case_process a,clm_whole_case_base b
        where a.report_no=b.report_no
        and a.case_times=b.case_times
        and b.case_type!='06'
        <if test="beginReportDate!='' and endReportDate!=''">
            and b.CREATED_DATE <![CDATA[>]]> STR_TO_DATE(#{beginReportDate, jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%s')
            and b.CREATED_DATE <![CDATA[<]]> STR_TO_DATE(#{endReportDate, jdbcType=VARCHAR}, '%Y-%m-%d %H:%i:%s')
        </if>
        <if test="endCaseDateBegin!=null and endCaseDateEnd!=null">
            and b.END_CASE_DATE <![CDATA[>]]> #{endCaseDateBegin, jdbcType=TIMESTAMP}
            and b.END_CASE_DATE <![CDATA[<]]> #{endCaseDateEnd, jdbcType=TIMESTAMP}
        </if>
        and a.company_code in
        <foreach collection="departments" item="item" separator="," open="(" close=")">
            #{item.code,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>