<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.endcase.CaseSupervisionInfoMapper">
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.model.dto.endcase.CaseSupervisionInfoDTO">
        <id column="ID_AHCS_CASE_SUPERVISION_INFO" jdbcType="VARCHAR" property="idAhcsCaseSupervisionInfo" />
        <result column="REPORT_NO" jdbcType="VARCHAR" property="reportNo" />
        <result column="CASE_TIMES" jdbcType="DECIMAL" property="caseTimes" />
        <result column="NAME" jdbcType="VARCHAR" property="name" />
        <result column="BIRTHDAY" jdbcType="TIMESTAMP" property="birthday" />
        <result column="CERTIFICATE_NO" jdbcType="VARCHAR" property="certificateNo" />
        <result column="CERTIFICATE_TYPE" jdbcType="VARCHAR" property="certificateType" />
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
        <result column="CREATED_DATE" jdbcType="TIMESTAMP" property="createdDate" />
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
        <result column="UPDATED_DATE" jdbcType="TIMESTAMP" property="updatedDate" />
        <result column="ARCHIVE_TIME" jdbcType="TIMESTAMP" property="archiveTime" />
    </resultMap>

    <sql id="Base_Column_List">
        ID_AHCS_CASE_SUPERVISION_INFO, REPORT_NO, CASE_TIMES, NAME, BIRTHDAY, CERTIFICATE_NO,
        CERTIFICATE_TYPE, CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ARCHIVE_TIME
    </sql>

    <insert id="insert" parameterType="com.paic.ncbs.claim.model.dto.endcase.CaseSupervisionInfoDTO">
        insert into
            CLMS_CASE_SUPERVISION_INFO
            (REPORT_NO, CASE_TIMES, NAME, BIRTHDAY, CERTIFICATE_NO, CERTIFICATE_TYPE, CREATED_BY, UPDATED_BY )
        values
             (#{reportNo,jdbcType=VARCHAR},
              #{caseTimes,jdbcType=DECIMAL},
              #{name,jdbcType=VARCHAR},
              #{birthday,jdbcType=TIMESTAMP},
              #{certificateNo,jdbcType=VARCHAR},
              #{certificateType,jdbcType=VARCHAR},
              #{createdBy,jdbcType=VARCHAR},
              #{updatedBy,jdbcType=VARCHAR})
    </insert>



</mapper>