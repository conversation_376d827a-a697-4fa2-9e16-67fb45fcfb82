<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
		    
<mapper namespace="com.paic.ncbs.claim.dao.mapper.endcase.ClmsPayModifyRecordMapper">

	<resultMap type="com.paic.ncbs.claim.model.dto.endcase.ClmsPayModifyRecordDTO" id="clmsPayModifyRecordDTO">
		<result column="CREATED_BY" property="createdBy"/>
		<result column="CREATED_DATE" property="createdDate"/>
		<result column="UPDATED_BY" property="updatedBy"/>
		<result column="UPDATED_DATE" property="updatedDate"/>
		<result column="ID_CLMS_PAY_MODIFY_RECORD" property="idClmsPayModifyRecord"/>
		<result column="ID_CLM_PAYMENT_ITEM" property="idClmPaymentItem"/>
		<result column="APPLY_UM" property="applyUm"/>
		<result column="APPLY_DATE" property="applyDate"/>
		<result column="AUDIT_UM" property="auditUm"/>
		<result column="AUDIT_OPINION" property="auditOpinion"/>
		<result column="AUDIT_DATE" property="auditDate"/>
		<result column="AUDIT_REMARK" property="auditRemark"/>
	</resultMap>
	

	<insert id="addClmsPayModifyRecordDTO" parameterType="com.paic.ncbs.claim.model.dto.endcase.ClmsPayModifyRecordDTO">
		INSERT INTO CLMS_PAY_MODIFY_RECORD(
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_CLMS_PAY_MODIFY_RECORD,
			ID_CLM_PAYMENT_ITEM,
			APPLY_UM,
			APPLY_DATE,
			AUDIT_UM,
			AUDIT_OPINION,
			AUDIT_DATE,
			AUDIT_REMARK
		)VALUES(
			#{createdBy, jdbcType = VARCHAR},
			SYSDATE(),
			#{updatedBy, jdbcType = VARCHAR},
			SYSDATE(),
			#{idClmsPayModifyRecord, jdbcType = VARCHAR},
			#{idClmPaymentItem, jdbcType=VARCHAR},
			#{applyUm, jdbcType=VARCHAR},
			SYSDATE(),
			#{auditUm, jdbcType=VARCHAR},
			#{auditOpinion, jdbcType=VARCHAR},
			#{auditDate, jdbcType=TIMESTAMP},
			#{auditRemark, jdbcType=VARCHAR}
			)
	</insert>

	<select id="getClmsPayModifyRecordList" resultMap="clmsPayModifyRecordDTO">
		SELECT  ID_CLMS_PAY_MODIFY_RECORD,
				ID_CLM_PAYMENT_ITEM,
				APPLY_UM,
				APPLY_DATE,
				AUDIT_UM,
				(case when AUDIT_OPINION='1' then '同意' when AUDIT_OPINION='2' then '不同意' else '' end) AUDIT_OPINION,
				AUDIT_DATE,
				AUDIT_REMARK
		 from CLMS_PAY_MODIFY_RECORD
		where ID_CLM_PAYMENT_ITEM = #{idClmPaymentItem, jdbcType=VARCHAR}
		order by APPLY_DATE
	</select>

</mapper>