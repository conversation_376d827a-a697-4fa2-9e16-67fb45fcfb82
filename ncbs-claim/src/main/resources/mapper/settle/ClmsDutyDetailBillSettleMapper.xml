<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.ClmsDutyDetailBillSettleMapper">
    <insert id="batchSaveData">
        insert into clms_duty_detail_bill_settle_info(id,report_no,case_times,policy_no,product_code,plan_code,duty_code,duty_detail_code,bill_no,bill_date,formula_amount,auto_settle_amount,settle_amount,remit_amount,reasonable_amount,pay_proportion,is_deleted,approval_status,settle_type,
        times_limit,day_limit,month_limit,year_limit,remark,created_by,created_date,updated_by,updated_date,hospital_code,hospital_name,remaind_day_deductible,expend_day_deductible)
        values
        <foreach collection="list" item="dto" separator=",">
            (
            #{dto.id},
            #{dto.reportNo},
            #{dto.caseTimes},
            #{dto.policyNo},
            #{dto.productCode},
            #{dto.planCode},
            #{dto.dutyCode},
            #{dto.dutyDetailCode},
            #{dto.billNo},
            #{dto.billDate},
            #{dto.formulaAmount},
            #{dto.autoSettleAmount},
            #{dto.settleAmount},
            #{dto.remitAmount},
            #{dto.reasonableAmount},
            #{dto.payProportion},
            #{dto.isDeleted},
            #{dto.approvalStatus},
            #{dto.settleType},
            #{dto.timesLimit},
            #{dto.dayLimit},
            #{dto.monthLimit},
            #{dto.yearLimit},
            #{dto.remark},
            #{dto.createdBy},
            now(),
            #{dto.updatedBy},
            now(),
            #{dto.hospitalCode},
            #{dto.hospitalName},
            #{dto.remaindDayDeductible},
            #{dto.expendDayDeductible}
            )
        </foreach>
    </insert>

    <update id="updateClmsDutyDetailBillSettle"
            parameterType="com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO">
        update clms_duty_detail_bill_settle_info
        <set>
            <if test="approvalStatus != null and approvalStatus != ''">
                approval_status = #{approvalStatus},
            </if>
            <if test="isDeleted != null and isDeleted != ''">
                is_deleted = #{isDeleted},
            </if>
            updated_date = now()
        </set>
        where report_no = #{reportNo}
        and case_times = #{caseTimes}
        and is_deleted = '0'
    </update>

    <select id="getClmsDutyDetailBillSettleList"
            parameterType="com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO"
            resultType="com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO">
        select id,
               report_no reportNo,
               case_times caseTimes,
               policy_no policyNo,
               product_code productCode,
               duty_code dutyCode,
               duty_detail_code dutyDetailCode,
               bill_no billNo,
               bill_date billDate
        from clms_duty_detail_bill_settle_info
        where report_no = #{reportNo}
          and case_times = #{caseTimes}
          and is_deleted = '0'
    </select>

    <select id="getClmsDutyDetailBillSettleListCase"
            parameterType="com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO"
            resultType="com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO">
        select id,
        report_no reportNo,
        case_times caseTimes,
        policy_no policyNo,
        product_code productCode,
        duty_code dutyCode,
        duty_detail_code dutyDetailCode,
        bill_no billNo,
        bill_date billDate,
        hospital_code hospitalCode,
        hospital_name hospitalName,
        settle_amount settleAmount,
        auto_settle_amount autoSettleAmount
        from clms_duty_detail_bill_settle_info
        where report_no = #{reportNo}
        and case_times = #{caseTimes}
        and is_deleted = '0'
        and (settle_amount > 0 or auto_settle_amount > 0)
    </select>

    <select id="getClmsPayBillSettleList"
            parameterType="com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO"
            resultType="com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO">
        select t1.id,
        t1.report_no reportNo,
        t1.case_times caseTimes,
        t1.policy_no policyNo,
        t1.product_code productCode,
        t1.duty_code dutyCode,
        t1.duty_detail_code dutyDetailCode,
        t1.bill_no billNo,
        t1.bill_date billDate,
        t1.hospital_code hospitalCode,
        t1.hospital_name hospitalName,
        t1.settle_amount settleAmount
        from clms_duty_detail_bill_settle_info t1
        where t1.policy_no = #{policyNo}
        and t1.is_deleted = '0'
        and t1.approval_status = '1'
        and t1.settle_type = '0'
        and (t1.settle_amount > 0 or t1.auto_settle_amount > 0)
        <if test="billStartDate !=null">
            and t1.bill_date<![CDATA[>=]]>#{billStartDate}
        </if>
        <if test="billEndDate !=null">
            and t1.bill_date<![CDATA[<=]]>#{billEndDate}
        </if>
        and t1.case_times = (
            SELECT MAX(t2.case_times)
            FROM clms_duty_detail_bill_settle_info t2
            WHERE t2.report_no = t1.report_no
        )
    </select>

    <delete id="deleteByReportNo">
        delete from clms_duty_detail_bill_settle_info
        where
        report_no = #{reportNo}
        and case_times = #{caseTimes}
        and is_deleted =0
        and approval_status=0
    </delete>
    <select id="getAllInfoByReportNo" parameterType="com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO" resultType="com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO">
        select a.id,a.report_no,a.case_times,a.policy_no,a.product_code,a.plan_code,a.duty_code,a.duty_detail_code,a.bill_no,a.bill_date,a.auto_settle_amount,a.settle_amount,a.reasonable_amount,a.remit_amount,a.pay_proportion,a.remark,a.settle_type settleType,
        b.bill_amount billAmount,b.THERAPY_TYPE therapyType,a.remaind_day_deductible remaindDayDeductible,a.expend_day_deductible expendDayDeductible,a.hospital_code hospitalCode,a.hospital_name hospitalName
        from clms_duty_detail_bill_settle_info a ,clms_bill_info b
        where a.report_no=b.report_no
        and a.bill_no=b.bill_no
        and a.case_times=b.case_times
        and a.report_no=#{reportNo}
        and a.policy_no=#{policyNo}
        and a.case_times =#{caseTimes}
        and a.is_deleted=0
        and b.IS_EFFECTIVE='Y'
        <if test="scene == null or scene == '' ">
            and a.approval_status =0
        </if>

    </select>
    <update id="updateListById">
        <foreach collection="list" item="item" separator=";">
            update clms_duty_detail_bill_settle_info
            <set>
                <if test="item.settleAmount !=null and item.settleAmount!='' ">
                    settle_amount=#{item.settleAmount},
                </if>
                <if test="item.remitAmount !=null and item.remitAmount!=''">
                    remit_amount=#{item.remitAmount},
                </if>
                <if test="item.payProportion !=null and item.payProportion!= '' ">
                    pay_proportion=#{item.payProportion},
                </if>
                <if test="item.remark !=null and item.remark!= '' ">
                    remark=#{item.remark}
                </if>
            </set>
            where id=#{item.id}
        </foreach>
    </update>
    <select id="getDutyUsedRemitAmount" parameterType="com.paic.ncbs.claim.model.dto.settle.factor.RemitAmountDTO" resultType="com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO">
        select  nvl(sum(remit_amount),0) remitAmount,report_no reportNo,case_times caseTimes from clms_duty_detail_bill_settle_info
        where policy_no=#{policyNo}
        and plan_code=#{planCode}
        and duty_code=#{dutyCode}
        and bill_date<![CDATA[>=]]>#{insuranceBeginDate}
        and bill_date<![CDATA[<=]]>#{insuranceEndDate}
        and approval_status=1
        and is_deleted=0
        group by report_no,case_times
    </select>
    <select id="getDutyUsedDayRemitAmount" parameterType="com.paic.ncbs.claim.model.dto.settle.factor.RemitAmountDTO" resultType="com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO">
        select  expend_day_deductible,report_no reportNo,case_times caseTimes,bill_date billDate from clms_duty_detail_bill_settle_info
        where policy_no=#{policyNo}
        and plan_code=#{planCode}
        and duty_code=#{dutyCode}
        and bill_date in
        <foreach collection="dateList" separator="," open="(" close=")" item="billDate">
            #{billDate}
        </foreach>
        and approval_status=1
        and is_deleted=0
        group by report_no,bill_no
        order by case_times desc;
    </select>
    <select id="getBillSettleInfoByCondition" parameterType="com.paic.ncbs.claim.model.dto.settle.DutyDetailSettleRequest" resultType="com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO">
        select id,report_no,case_times,policy_no,product_code,plan_code,duty_code,duty_detail_code,bill_no,bill_date,auto_settle_amount,settle_amount,reasonable_amount,remit_amount,pay_proportion,remark,settle_type,expend_day_deductible
        from clms_duty_detail_bill_settle_info
        where report_no=#{reportNo}
        and POLICY_NO=#{policyNo}
        and PLAN_CODE=#{planCode}
        and DUTY_CODE=#{dutyCode}
        <if test="dutyDetailCode != null and dutyDetailCode != '' ">
        and DUTY_DETAIL_CODE=#{dutyDetailCode}
        </if>
        and case_times= #{caseTimes}
        and IS_DELETED=0
        order by bill_date
    </select>
    <update id="updateOneById" parameterType="com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO">
        update clms_duty_detail_bill_settle_info
        set settle_amount=#{settleAmount},
        remit_amount=#{remitAmount},
        remark=#{remark}
        where id=#{id}
    </update>
    <select id="getClmsDutyDetailByDutyCode" resultType="java.math.BigDecimal">
        select nvl(sum(remit_amount),0) from clms_duty_detail_bill_settle_info
        where report_no=#{reportNo}
        and POLICY_NO=#{policyNo}
        and PLAN_CODE=#{planCode}
        and DUTY_CODE=#{dutyCode}
        and case_times= #{caseTimes}
        and IS_DELETED=0
    </select>
    <select id="getCurrentPolicyDutyBillSettleInfo" resultType="com.paic.ncbs.claim.model.dto.settle.factor.ClmsDutyDetailBillSettleDTO">
        select nvl(sum(remit_amount),0) remitAmount,policy_no policyNo,plan_code planCode,duty_code dutyCode
        from clms_duty_detail_bill_settle_info where
        report_no=#{reportNo}
        and case_times=#{caseTimes}
        and is_deleted=0
        GROUP BY  policy_no,plan_code,duty_code
    </select>
</mapper>