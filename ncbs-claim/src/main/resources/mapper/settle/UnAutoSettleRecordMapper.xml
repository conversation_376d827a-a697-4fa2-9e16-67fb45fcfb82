<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.UnAutoSettleRecordMapper">

    <insert id="saveUnAutoSettleRecord" parameterType="com.paic.ncbs.claim.model.dto.settle.UnAutoSettleRecordDTO">
        INSERT INTO CLMS_UNAUTO_SETTLE_RECORD
        (
        ID_AHCS_UNAUTO_SETTLE_RECORD,
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        POLICY_NO,
        CASE_NO,
        PLAN_CODE,
        DUTY_CODE,
        DUTY_DETAIL_CODE,
        UNAUTO_SETTLE_REASON,
        ARCHIVE_TIME
        ,PLAN_NAME
        ,DUTY_NAME
        ,DUTY_DETAIL_NAME)
        VALUES
        (
        left(hex(uuid()),32),
        'SYSTEM',
        NOW(),
        'SYSTEM',
        NOW(),
        #{reportNo,jdbcType=VARCHAR},
        #{caseTimes,jdbcType=INTEGER},
        #{policyNo,jdbcType=VARCHAR},
        #{caseNo,jdbcType=VARCHAR},
        #{planCode,jdbcType=VARCHAR},
        #{dutyCode,jdbcType=VARCHAR},
        #{dutyDetailCode,jdbcType=VARCHAR},
        #{unAutoSettleReason,jdbcType=VARCHAR},
        NOW(),
        #{planName,jdbcType=VARCHAR},
        #{dutyName,jdbcType=VARCHAR},
        #{dutyDetailName,jdbcType=VARCHAR})
    </insert>

    <insert id="batchSaveUnAutoSettleRecord" parameterType="com.paic.ncbs.claim.model.dto.settle.UnAutoSettleRecordDTO">
        INSERT INTO CLMS_UNAUTO_SETTLE_RECORD
        (
        ID_AHCS_UNAUTO_SETTLE_RECORD,
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_TIMES,
        POLICY_NO,
        CASE_NO,
        PLAN_CODE,
        DUTY_CODE,
        DUTY_DETAIL_CODE,
        UNAUTO_SETTLE_REASON,
        ARCHIVE_TIME
        ,PLAN_NAME
        ,DUTY_NAME
        ,DUTY_DETAIL_NAME)
        <foreach collection="unAutoSettleRecordList" item="item" separator=" union all " open="(" close=")">
            SELECT
            left(hex(uuid()),32),
            'SYSTEM',
            NOW(),
            'SYSTEM',
            NOW(),
            #{item.reportNo,jdbcType=VARCHAR},
            #{item.caseTimes,jdbcType=INTEGER},
            #{item.policyNo,jdbcType=VARCHAR},
            #{item.caseNo,jdbcType=VARCHAR},
            #{item.planCode,jdbcType=VARCHAR},
            #{item.dutyCode,jdbcType=VARCHAR},
            #{item.dutyDetailCode,jdbcType=VARCHAR},
            #{item.unAutoSettleReason,jdbcType=VARCHAR},
            NOW(),
            #{item.planName,jdbcType=VARCHAR},
            #{item.dutyName,jdbcType=VARCHAR},
            #{item.dutyDetailName,jdbcType=VARCHAR}
            FROM DUAL
        </foreach>

    </insert>

    <delete id="deleteUnautoSettleRecord">
        delete from CLMS_UNAUTO_SETTLE_RECORD where
        report_no = #{reportNo,jdbcType=VARCHAR} and case_times = #{caseTimes,jdbcType=INTEGER}
    </delete>

    <select id="getUnautoSettleRecord" resultType="com.paic.ncbs.claim.model.vo.settle.SettleSpreadSheetResultVO">
        select
        REPORT_NO reportNo,
        CASE_TIMES caseTimes,
        POLICY_NO policyNo,
        (select pi.policy_cer_no from CLMS_policy_info pi where pi.case_no=sr.case_no limit 1) policyCerNo,
        CASE_NO caseNo,
        PLAN_CODE planCode,
        PLAN_NAME planName,
        DUTY_CODE dutyCode,
        DUTY_NAME dutyName,
        DUTY_DETAIL_CODE dutyDetailCode,
        DUTY_DETAIL_NAME dutyDetailName,
        UNAUTO_SETTLE_REASON unAutoSettleReason
        from CLMS_UNAUTO_SETTLE_RECORD sr where
        report_no = #{reportNo,jdbcType=VARCHAR} and case_times = #{caseTimes,jdbcType=INTEGER}
    </select>

</mapper>