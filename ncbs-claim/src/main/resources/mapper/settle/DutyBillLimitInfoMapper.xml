<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.DutyBillLimitInfoMapper">
  <sql id="Base_Column_List">
    id_clms_bill_duty_limit idClmsDutyBillLimit,
    report_no,
    case_times,
    bill_no,
    bill_date,
    bill_amount,
    social_security_flag,
    claim_rate,
    pay_amount,
    policy_no,
    product_code,
    plan_code,
    duty_code,
    approval_status,
    id_ahcs_bill_info,
    created_by,
    created_date,
    updated_by,
    updated_date,
    is_deleted,
    limit_amount,
    limit_type,
    settle_claim_amount,
    duty_detail_code
  </sql>

  <insert id="addDutyBillLimit">
    insert into
    clms_duty_bill_limit
    (created_by,
    created_date,
    updated_by,
    updated_date,
    id_clms_bill_duty_limit,
    report_no,
    case_times,
    bill_date,
    settle_claim_amount,
    bill_amount,
    policy_no,
    product_code,
    plan_code,
    duty_code,
    approval_status,
    limit_type,
    duty_detail_code
    ) values(
    #{createdBy},
    #{createdDate},
    #{updatedBy},
    #{updatedDate},
    #{idClmsDutyBillLimit},
    #{reportNo},
    #{caseTimes},
    #{billDate},
    #{settleClaimAmount},
    #{billAmount},
    #{policyNo},
    #{productCode},
    #{planCode},
    #{dutyCode},
    #{approvalStatus},
    #{limitType},
    #{dutyDetailCode}
    )
  </insert>
  <insert id="saveList" parameterType="java.util.List">
    insert into
    clms_duty_bill_limit
    (created_by,
    created_date,
    updated_by,
    updated_date,
    id_clms_bill_duty_limit,
    report_no,
    case_times,
    bill_date,
    settle_claim_amount,
    bill_amount,
    policy_no,
    product_code,
    plan_code,
    duty_code,
    approval_status,
    limit_type,
    duty_detail_code,
    hospital_property_des
    ) values
    <foreach collection="list" item="term" index="index" separator=",">
      (#{term.createdBy}, #{term.createdDate}, #{term.updatedBy},
      #{term.updatedDate}, #{term.idClmsDutyBillLimit}, #{term.reportNo},
      #{term.caseTimes}, #{term.billDate}, #{term.settleClaimAmount},
      #{term.billAmount}, #{term.policyNo}, #{term.productCode},
      #{term.planCode}, #{term.dutyCode}, #{term.approvalStatus},#{term.limitType},#{term.dutyDetailCode},
      #{term.hospitalPropertyDes})
    </foreach>
  </insert>

  <select id="getBillDate" resultType="com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO">
    select * from clms_duty_bill_limit where policy_no =#{policyNo} and approval_status !='2' and is_deleted =0
  </select>

  <select id="getLimitBilltByReportNoAndCaseTimes" resultType="com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO"  >
    select
           report_no reportNo,
           case_times caseTimes,
           bill_date billDate
    from clms_duty_bill_limit
    where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
      AND CASE_TIMES = #{caseTimes,jdbcType=INTEGER}
      AND IS_DELETED = 0
  </select>

  <select id="selectAll" resultType="com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO">
    select
    report_no,
    bill_date,
    duty_code
    from clms_duty_bill_limit where policy_no =#{policyNo} and is_deleted = 0 and approval_status !='2'
  </select>

  <select id="getPolicyNo" resultType="java.lang.String">
    select policy_no from  clms_policy_claim_case
    where
    REPORT_NO= #{reportNo,jdbcType=VARCHAR}
    limit 1
  </select>
  <select id="getDaySum" parameterType="com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO" resultType="java.math.BigDecimal">
    select nvl(sum(settle_claim_amount),0) from  clms_duty_bill_limit
    where policy_no=#{policyNo}
    and plan_code=#{planCode}
    and duty_code=#{dutyCode}
    and  bill_date=#{billDate}
    and limit_type='2'
    and approval_status='1'
    and is_deleted='0'
  </select>

  <delete id="deleteByReportNoAndCaseTimes">
    delete from clms_duty_bill_limit
    where
    report_no=#{reportNo}
    and case_times= #{caseTimes}
  </delete>
  <select id="getUpCasetimesAmount" resultType="java.math.BigDecimal">
    select nvl(sum(settle_claim_amount),0) from clms_duty_bill_limit where
    report_no=#{reportNo}
    and policy_no=#{policyNo}
    and plan_code=#{planCode}
    and duty_code=#{dutyCode}
    and bill_date=#{billDate}
    and limit_type='2'
    and approval_status='1'
    and is_deleted='0'
    and case_times=#{caseTimes}
  </select>

  <update id="updateUpCaseTimesHistory">
    update clms_duty_bill_limit set approval_status='3'
    where report_no=#{reportNo}
    and case_times= #{caseTimes}
  </update>

  <select id="getDutyBillDateInfo" resultType="com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO">
    select ID_CLMS_BILL_DUTY_LIMIT idClmsDutyBillLimit,bill_date billDate,settle_claim_amount settleClaimAmount,hospital_property_des hospitalPropertyDes
    from clms_duty_bill_limit where
    report_no=#{reportNo}
    and policy_no=#{policyNo}
    and plan_code=#{planCode}
    and duty_code=#{dutyCode}
    and case_times=#{caseTimes}
    and limit_type='2'
    and approval_status!='1'
    and is_deleted='0'
    and settle_claim_amount != 0
    and bill_date in
    <foreach collection="billDateList" separator="," open="(" close=")" item="billDate">
      #{billDate}
    </foreach>
    ORDER BY bill_date asc
  </select>
  <select id="getAllPayDutyLimitDate"  resultType="com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO">
    select nvl(sum(settle_claim_amount),0) settleClaimAmount,bill_date billDate ,report_no reportNo
    from clms_duty_bill_limit where
    policy_no=#{policyNo}
    and plan_code=#{planCode}
    and duty_code=#{dutyCode}
    and approval_status='1'
    and is_deleted='0'
    and limit_type='2'
    and bill_date in
    <foreach collection="billDateList"  index="index" separator="," open="(" close=")" item="item">
      #{item}
    </foreach>
    group by bill_date,report_no
  </select>

  <select id="getHisDutyBillLimitList" resultType="com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO">
    select
    <include refid="Base_Column_List"/>
    from clms_duty_bill_limit where report_no != #{reportNo}
                                and policy_no = #{policyNo}
    <if test="planCodeList != null and !planCodeList.isEmpty()">
      and plan_code in
      <foreach collection="planCodeList" index="index" separator="," open="(" close=")" item="item">
        #{item}
      </foreach>
    </if>
    <if test="dutyCodeList != null and !dutyCodeList.isEmpty()">
      and duty_code in
      <foreach collection="dutyCodeList" index="index" separator="," open="(" close=")" item="item">
        #{item}
      </foreach>
    </if>
    <if test="billDateList != null and !billDateList.isEmpty()">
      and bill_date in
      <foreach collection="billDateList" index="index" separator="," open="(" close=")" item="item">
        #{item}
      </foreach>
    </if>
    and approval_status = '1'
    and is_deleted = '0'
    and limit_type = '2'
    order by bill_date, report_no
  </select>

  <update id="updateBatchDutyBillLimit" parameterType="java.util.List">
    <foreach collection="paramList" index="index" item="item" open="" separator=";" close="">
      update clms_duty_bill_limit set approval_status=#{item.approvalStatus},
      settle_claim_amount=#{item.settleClaimAmount},
      is_deleted=#{item.isDeleted},
      UPDATED_DATE=#{item.updatedDate}
      where ID_CLMS_BILL_DUTY_LIMIT=#{item.idClmsDutyBillLimit}
    </foreach>

  </update>
  <select id="getAutoSettleAmountByReportNo" resultType="com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO">
    select DUTY_CODE dutyCode, nvl(sum(settle_claim_amount),0) settleClaimAmount from
    clms_duty_bill_limit where report_no=#{reportNo}
    and case_times=#{caseTimes}
    GROUP BY DUTY_CODE
  </select>
  <update id="updateByReportNo">
    update clms_duty_bill_limit set approval_status='1',updated_date=now()
    where report_no=#{reportNo}
    and case_times=#{caseTimes}
  </update>

  <select id="getAllDate" resultType="java.lang.String">
    select distinct bill_date from clms_duty_bill_limit
    where report_no=#{reportNo}
    and case_times=#{caseTimes}
    and limit_type='2'
  </select>
  <select id="getAllAlreadyPayTimes" parameterType="com.paic.ncbs.claim.model.vo.duty.DutyLimitQueryVo" resultType="com.paic.ncbs.claim.model.vo.duty.DutyBillLimitDto">
    select distinct a.bill_date billDate,a.report_no reportNo,b.case_no caseNo,a.settle_claim_amount settleClaimAmount
    from clms_duty_bill_limit a ,clm_case_base b
    where
    a.report_no=b.report_no
    and a.policy_no=#{policyNo}
    and a.plan_code=#{planCode}
    and a.duty_code=#{dutyCode}
    and a.approval_status='1'
    and a.is_deleted='0'
    and a.bill_date <![CDATA[>=]]>#{satrtDate}
    and a.bill_date <![CDATA[<=]]>#{endDate}
    and a.settle_claim_amount != 0
    <!-- and not exists (select 1 from CLMS_DUTY_DETAIL_PAY c where c.CASE_NO=b.case_no
    and c.duty_code=#{dutyCode}
    and c.INDEMNITY_MODE  in('15','16')) -->
  </select>

  <select id="getPolicyAlreadyPayTimes" parameterType="com.paic.ncbs.claim.model.vo.duty.DutyLimitQueryVo" resultType="com.paic.ncbs.claim.model.vo.duty.DutyBillLimitDto">
    select distinct a.bill_date billDate,a.report_no reportNo,a.settle_claim_amount settleClaimAmount,a.case_times caseTimes
    from clms_duty_bill_limit a
    where
     a.policy_no=#{policyNo}
    and a.approval_status='1'
    and a.is_deleted='0'
    and a.bill_date <![CDATA[>=]]>#{satrtDate}
    and a.bill_date <![CDATA[<=]]>#{endDate}
    and a.settle_claim_amount != 0
    and a.report_no != #{reportNo}
  </select>

  <select id="getAllAlreadyPayPolicy" parameterType="com.paic.ncbs.claim.model.vo.duty.DutyLimitQueryVo" resultType="com.paic.ncbs.claim.model.vo.duty.DutyBillLimitDto">
    select distinct a.bill_date billDate,a.report_no reportNo,b.case_no caseNo,a.settle_claim_amount settleClaimAmount
    from clms_duty_bill_limit a
    where
    and a.policy_no=#{policyNo}
    and a.approval_status='1'
    and a.is_deleted='0'
    and a.settle_claim_amount != 0
  </select>

  <select id="getReportNoPolicyBillLimitInfo" resultType="com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO">
    select distinct policy_no policyNo,bill_date billDate,plan_code planCode,duty_code dutyCode,settle_claim_amount settleClaimAmount
    from clms_duty_bill_limit where
    report_no=#{reportNo}
    and CASE_TIMES=#{caseTimes}
    and is_deleted='0'
    and settle_claim_amount != 0
  </select>
  <update id="updateNoLimitInfo">
    update clms_duty_bill_limit set approval_status='1' ,
    updated_date=now()
    where report_no=#{reportNo}
    and case_times=#{caseTimes}
    and limit_type='N'
  </update>

  <select id="getDutyLimitPayList" parameterType="com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO" resultType="com.paic.ncbs.claim.model.vo.settle.DutyBillLimitInfoVO">
    select bill_date billDate,nvl(sum(settle_claim_amount),0) settleAmount from  clms_duty_bill_limit
    where policy_no=#{policyNo}
      and plan_code=#{planCode}
      and duty_code=#{dutyCode}
      and report_no=#{reportNo}
      and CASE_TIMES=#{caseTimes}
      and limit_type='2'
      and approval_status='1'
      and is_deleted='0'
    group by bill_date
  </select>

  <select id="getYearlyPayDaysInfo" resultType="com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO"  >
    select policy_no policyNo,plan_code planCode,DUTY_CODE dutyCode,bill_date billDate
    from clms_duty_bill_limit
    where REPORT_NO = #{reportNo}
    AND CASE_TIMES = #{caseTimes}
    AND APPROVAL_STATUS='0'
    and is_deleted=0
    and settle_claim_amount != 0
  </select>

  <select id="getHistoryYearlyPayDaysInfo" parameterType="com.paic.ncbs.claim.model.vo.duty.DutyLimitQueryVo" resultType="com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO"  >
    select distinct a.bill_date billDate
    from clms_duty_bill_limit a ,clm_case_base b
    where
    a.report_no=b.report_no
    and a.policy_no=#{policyNo}
    and a.plan_code=#{planCode}
    and a.duty_code=#{dutyCode}
    and a.approval_status='1'
    and a.is_deleted='0'
    and a.settle_claim_amount != 0
    and a.bill_date <![CDATA[>=]]>#{satrtDate}
    and a.bill_date <![CDATA[<=]]>#{endDate}
    and  exists (select 1 from CLMS_DUTY_DETAIL_PAY c where c.CASE_NO=b.case_no
    and c.duty_code=#{dutyCode}
    and c.INDEMNITY_MODE='1')
    and a.bill_date not in
    <foreach collection="dateList" separator="," open="(" close=")" item="item">
      #{item}
    </foreach>
  </select>

  <select id="getUsedAmount" resultType="com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO">
    select report_no reportNo,duty_code dutyCode,case_times caseTimes,settle_claim_amount settleClaimAmount from clms_duty_bill_limit where
    policy_no=#{policyNo}
    and bill_date<![CDATA[>=]]>#{satrtDate}
    and bill_date<![CDATA[<=]]>#{endDate}
    and approval_status='1'
    and is_deleted=0
    and limit_type='2'
  </select>

  <select id="getHistoryAmount" resultType="java.math.BigDecimal">
    select nvl(sum(c.settle_claim_amount), 0)
    from clms_duty_bill_limit c,
         (select a.report_no, max(case_times) case_times
          from clms_duty_bill_limit a
          where a.policy_no = #{policyNo}
            and a.report_no <![CDATA[<>]]> #{reportNo}
            and a.limit_type = '2'
            and a.approval_status = '1'
            and a.is_deleted = '0'
          group by a.report_no) b
    where c.policy_no = #{policyNo}
      and c.bill_date <![CDATA[>=]]> #{startDate}
      and c.bill_date <![CDATA[<=]]> #{endDate}
<!--      and c.plan_code = #{planCode}-->
<!--      and c.duty_code = #{dutyCode}-->
      and c.limit_type = '2'
      and c.approval_status = '1'
      and c.is_deleted = '0'
      and b.report_no = c.REPORT_NO
      and b.case_times = c.case_times
  </select>

  <select id="getBillLimitInfo" resultType="com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO">
    select policy_no policyNo, bill_date billDate, plan_code planCode, duty_code dutyCode, settle_claim_amount settleClaimAmount
    from clms_duty_bill_limit
    where report_no = #{reportNo}
      and CASE_TIMES = #{caseTimes}
      and limit_type = '2'
      and is_deleted = '0'
      and approval_status = '0'
  </select>
  <select id="getBillDayLimit" parameterType="com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO" resultType="com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO">
    select id_clms_bill_duty_limit idClmsDutyBillLimit
    from clms_duty_bill_limit where report_no=#{reportNo}
    and  policy_no= #{policyNo}
    and plan_code=#{planCode}
    and duty_code=#{dutyCode}
    and bill_date=#{billDate}
    and case_times=#{caseTimes} and is_deleted='0'
  </select>
  <update id="updateDutyLimitById" parameterType="com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO">
    update clms_duty_bill_limit set bill_amount=#{billAmount},settle_claim_amount=#{settleClaimAmount},updated_date=now()
    where id_clms_bill_duty_limit=#{idClmsDutyBillLimit}
  </update>
  <delete id="deleteDutyBillByDutyCode">
    delete from  clms_duty_bill_limit
    where report_no=#{reportNo}
    and case_times=#{caseTimes}
    and is_deleted='0'
    and approval_status='0'
  </delete>
  <delete id="deleteDutyBillByDutyDetailCode">
    delete from  clms_duty_bill_limit
    where report_no=#{reportNo}
    and case_times=#{caseTimes}
    and is_deleted='0'
    and approval_status='0'
    and duty_detail_code=#{dutyDetailCode}
  </delete>
  <select id="getDutyLimitData" parameterType="com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO" resultType="com.paic.ncbs.claim.model.dto.settle.DutyBillLimitInfoDTO">
    select
    id_clms_bill_duty_limit idClmsDutyBillLimit,
    report_no,
    case_times,
    bill_date,
    settle_claim_amount,
    bill_amount,
    policy_no,
    product_code,
    plan_code,
    duty_code,
    approval_status,
    limit_type,
    duty_detail_code dutyDetailCode from clms_duty_bill_limit
    where report_no=#{reportNo}
    and case_times=#{caseTimes}
    and policy_no= #{policyNo}
  </select>
  <update id="updateDutyBillLimitAmount" parameterType="java.util.List">
    <foreach collection="list" index="index" item="item" open="" separator=";" close="">
      update clms_duty_bill_limit set
      settle_claim_amount=#{item.settleClaimAmount},
      UPDATED_DATE=#{item.updatedDate}
      where ID_CLMS_BILL_DUTY_LIMIT=#{item.idClmsDutyBillLimit}
    </foreach>

  </update>
</mapper>