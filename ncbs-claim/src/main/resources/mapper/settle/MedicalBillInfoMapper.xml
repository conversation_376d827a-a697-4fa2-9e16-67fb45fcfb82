<?xml version = "1.0" encoding = "UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.MedicalBillInfoMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO" id="medicalBillInfoMap1">
        <id column="ID_AHCS_BILL_INFO" property="idAhcsBillInfo"/>
        <result column="CREATED_BY" property="createdBy"/>
        <result column="CREATED_DATE" property="createdDate"/>
        <result column="UPDATED_BY" property="updatedBy"/>
        <result column="UPDATED_DATE" property="updatedDate"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="ID_AHCS_CHANNEL_PROCESS" property="idClmChannelProcess"/>
        <result column="BILL_NO" property="billNo"/>
        <result column="BILL_CLASS" property="billClass"/>
        <result column="therapy_type" property="therapyType"/>
        <result column="therapy_type_NAME" property="therapyTypeName"/>
        <result column="START_DATE" property="startDate"/>
        <result column="END_DATE" property="endDate"/>
        <result column="HOSPITAL_CODE" property="hospitalCode"/>
        <result column="HOSPITAL_NAME" property="hospitalName"/>
        <result column="GRADE" property="grade"/>
        <result column="IS_SOCIAL_INSURANCE" property="isSocialInsurance"/>
        <result column="IS_APPOINTED_HOSPITAL" property="isAppointedHospital"/>
        <result column="HOSPITAL_PROPERTY_DES" property="hospitalPropertyDes"/>
        <result column="PREPAID_TYPE" property="prepaidType"/>
        <result column="BILL_CATEGORY" property="billCategory"/>
        <result column="BILL_TYPE" property="billType"/>
        <result column="DAYS" property="days"/>
        <result column="BILL_AMOUNT" property="billAmount"/>
        <result column="DEDUCTIBLE_AMOUNT" property="deductibleAmount"/>
        <result column="PREPAID_AMOUNT" property="prepaidAmount"/>
        <result column="IMMODERATE_AMOUNT" property="immoderateAmount"/>
        <result column="PARTIAL_DEDUCTIBLE" property="partialDeductible"/>
        <result column="REASONABLE_AMOUNT" property="reasonableAmount"/>
        <result column="IS_EFFECTIVE" property="isEffective"/>
        <result column="INTENSIVE_CARE_DAYS" property="intensiveCareDays"/>
        <result column="remark" property="remark"/>
        <result column="LOSS_OBJECT_NO" property="lossObjectNo"/>
        <result column="gender" property="gender" />
        <result column="payer" property="payer" />
        <collection property="medicalBillReduceDetailDTOList"
                    ofType="com.paic.ncbs.claim.model.dto.settle.MedicalBillReduceDetailDTO"
                    select="com.paic.ncbs.claim.dao.mapper.settle.MedicalBillReduceDetailMapper.getBillReduceDetailList"
                    column="ID_AHCS_BILL_INFO" javaType="java.util.ArrayList">
        </collection>
        <collection property="billSpecial"
                    ofType="com.paic.ncbs.claim.model.dto.settle.MedicalBillSpecialDTO"
                    select="com.paic.ncbs.claim.dao.mapper.settle.MedicalBillSpecialMapper.getBillSpecial"
                    column="ID_AHCS_BILL_INFO">

        </collection>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO" id="medicalBillInfoMap2">
        <id column="ID_AHCS_BILL_INFO" property="idAhcsBillInfo"/>
        <result column="CREATED_BY" property="createdBy"/>
        <result column="CREATED_DATE" property="createdDate"/>
        <result column="UPDATED_BY" property="updatedBy"/>
        <result column="UPDATED_DATE" property="updatedDate"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="ID_AHCS_CHANNEL_PROCESS" property="idClmChannelProcess"/>
        <result column="BILL_NO" property="billNo"/>
        <result column="BILL_CLASS" property="billClass"/>
        <result column="THERAPY_TYPE" property="therapyType"/>
        <result column="START_DATE" property="startDate"/>
        <result column="END_DATE" property="endDate"/>
        <result column="HOSPITAL_CODE" property="hospitalCode"/>
        <result column="HOSPITAL_NAME" property="hospitalName"/>
        <result column="GRADE" property="grade"/>
        <result column="IS_SOCIAL_INSURANCE" property="isSocialInsurance"/>
        <result column="IS_APPOINTED_HOSPITAL" property="isAppointedHospital"/>
        <result column="HOSPITAL_PROPERTY_DES" property="hospitalPropertyDes"/>
        <result column="PREPAID_TYPE" property="prepaidType"/>
        <result column="BILL_CATEGORY" property="billCategory"/>
        <result column="BILL_TYPE" property="billType"/>
        <result column="DAYS" property="days"/>
        <result column="BILL_AMOUNT" property="billAmount"/>
        <result column="DEDUCTIBLE_AMOUNT" property="deductibleAmount"/>
        <result column="PREPAID_AMOUNT" property="prepaidAmount"/>
        <result column="IMMODERATE_AMOUNT" property="immoderateAmount"/>
        <result column="PARTIAL_DEDUCTIBLE" property="partialDeductible"/>
        <result column="REASONABLE_AMOUNT" property="reasonableAmount"/>
        <result column="IS_EFFECTIVE" property="isEffective"/>
        <result column="INTENSIVE_CARE_DAYS" property="intensiveCareDays"/>
        <result column="remark" property="remark"/>
        <result column="LOSS_OBJECT_NO" property="lossObjectNo"/>
        <result column="gender" property="gender" />
        <result column="payer" property="payer" />
        <association column="ID_AHCS_BILL_INFO" property="billSpecial" select="getBillSpecialByBillId"/>
        <collection property="medicalBillDetailDTOList" ofType="com.paic.ncbs.claim.model.dto.settle.MedicalBillDetailDTO"
                    column="ID_AHCS_BILL_INFO" javaType="java.util.ArrayList" select="getBillDetailList"/>
        <collection property="medicalBillReduceDetailDTOList"
                    ofType="com.paic.ncbs.claim.model.dto.settle.MedicalBillReduceDetailDTO"
                    column="ID_AHCS_BILL_INFO" javaType="java.util.ArrayList"
                    select="com.paic.ncbs.claim.dao.mapper.settle.MedicalBillReduceDetailMapper.getBillReduceDetailList"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.MedicalBillSpecialDTO" id="MedicalBillSpecialDTOMap">
        <id column="ID_AHCS_BILL_SPECIAL" property="idAhcsBillSpecial"/>
        <result property="createdBy" column="CREATED_BY"/>
        <result property="createdDate" column="CREATED_DATE"/>
        <result property="updatedBy" column="UPDATED_BY"/>
        <result property="updatedDate" column="UPDATED_DATE"/>
        <result column="ID_AHCS_BILL_INFO" property="idAhcsBillInfo"/>
        <result column="PAYMENT_A" property="paymentA"/>
        <result column="PAYMENT_FROM" property="paymentFrom"/>
        <result column="CAP_AMOUNT" property="capAmount"/>
        <result column="PAYMENT_B" property="paymentB"/>
        <result column="INDIVIDUAL_PAYMENT" property="individualPayment"/>
        <result column="PERSONAL_AMOUNT" property="personalAmount"/>
        <result column="ACCOUNT_PAYMENT" property="accountPayment"/>
        <result column="ACCOUNT_BALANCE" property="accountBalance"/>
        <result column="TOTAL_MEDICAL_AMOUNT" property="totalMedicalAmount"/>
        <result column="INSURANCE_TOTAL_PAYMENT" property="insuranceTotalPayment"/>
        <result column="ANNUAL_TOTAL_PAYMENT" property="annualTotalPayment"/>
        <result column="CLINIC_LARGE_PAYMENT" property="clinicLargePayment"/>
        <result column="ANNUAL_PAYMENT_BALANCE" property="annualPaymentBalance"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.MedicalBillDetailDTO" id="medicalBillDetailMap">
        <id column="ID_AHCS_BILL_DETAIL" property="idAhcsBillDetail"/>
        <result column="CREATED_BY" property="createdBy"/>
        <result column="CREATED_DATE" property="createdDate"/>
        <result column="UPDATED_BY" property="updatedBy"/>
        <result column="UPDATED_DATE" property="updatedDate"/>
        <result column="ID_AHCS_BILL_INFO" property="idAhcsBillInfo"/>
        <result column="DETAIL_NO" property="detailNo"/>
        <result column="INPUT_MODE" property="inputMode"/>
        <result column="COST_CODE" property="costCode"/>
        <result column="BILL_AMOUNT" property="billAmount"/>
        <result column="DEDUCTIBLE_AMOUNT" property="deductibleAmount"/>
        <result column="IMMODERATE_AMOUNT" property="immoderateAmount"/>
        <result column="PARTIAL_DEDUCTIBLE" property="partialDeductible"/>
        <result column="COST_COMMENT" property="costComment"/>
        <result column="FOREIGN_BILL_AMOUNT" property="foreignBillAmount"/>
        <result column="FOREIGN_IMMODERATE_AMOUNT" property="foreignimmoderateAmount"/>
        <result column="CURRENCY" property="currency"/>
        <result column="CUR_RATE" property="curRate"/>
        <result column="IS_EFFECTIVE" property="isEffective"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO" id="resultMedicalBillInfo">
        <result column="START_DATE_STR" property="startDateStr"/>
        <result column="END_DATE_STR" property="endDateStr"/>
        <result column="PREPAID_TYPE" property="prepaidType"/>
        <result column="DAYS" property="days"/>
    </resultMap>

    <resultMap type="com.paic.ncbs.claim.model.dto.duty.PersonDiagnoseDTO" id="result">
        <id column="ID_AHCS_PERSON_DIAGNOSE" property="personDiagnoseId"/>
        <result column="REPORT_NO" property="reportNo"/>
        <result column="CASE_TIMES" property="caseTimes"/>
        <result column="ID_AHCS_CHANNEL_PROCESS" property="idAhcsChannelProcess"/>
        <result column="DISPLAY_NO" property="displayNo"/>
        <result column="DIAGNOSE_CODE" property="diagnoseCode"/>
        <result column="DIAGNOSE_NAME" property="diagnoseName"/>
        <result column="IS_SURGICAL" property="isSurgical"/>
        <result column="SURGICAL_CODE" property="surgicalCode"/>
        <result column="SURGICAL_NAME" property="surgicalName"/>
        <result column="TASK_ID" property="taskId"/>
        <result column="STATUS" property="status"/>
        <result column="IS_FERTILITY" property="isFertility"/>
        <result column="SURGICAL_TYPE" property="surgicalType"/>
        <result column="SURGICAL_TYPE_NAME" property="surgicalTypeName"/>
        <result column="SURGICAL_NAME" property="surgicalName"/>
        <result column="IS_FERTILITY" property="isFertility"/>
        <result column="ARCHIVE_TIME" property="archiveTime"/>
        <result column="diagnostic_typology_code" property="diagnosticTypologyCode"/>
        <result column="IS_MAIN" property="isMain"/>
    </resultMap>

    <insert id="addBillInfo" parameterType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO">
        insert into
        CLMS_bill_info
        ( CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_BILL_INFO,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        BILL_NO,
        BILL_CLASS,
        THERAPY_TYPE,
        START_DATE,
        END_DATE,
        HOSPITAL_CODE,
        HOSPITAL_NAME,
        GRADE,
        IS_SOCIAL_INSURANCE,
        IS_APPOINTED_HOSPITAL,
        HOSPITAL_PROPERTY_DES,
        PREPAID_TYPE,
        BILL_CATEGORY,
        BILL_TYPE,
        DAYS,
        BILL_AMOUNT,
        DEDUCTIBLE_AMOUNT,
        PREPAID_AMOUNT,
        IMMODERATE_AMOUNT,
        PARTIAL_DEDUCTIBLE,
        REASONABLE_AMOUNT,
        IS_EFFECTIVE,
        INTENSIVE_CARE_DAYS,
        remark,
        LOSS_OBJECT_NO,
        archive_time,
        gender,
        payer,
        SUBJECT_CODE,
        VISIT_PLACE,
        HOSPITAL_PROVINCE_CODE,
        HOSPITAL_PROVINCE_NAME,
        HOSPITAL_CITY_CODE,
        HOSPITAL_CITY_NAME,
        HOSPITAL_DISTRICT_CODE,
        HOSPITAL_DISTRICT_NAME
        )
        values
        (#{createdBy},
        SYSDATE(),
        #{updatedBy},
        SYSDATE(),
        #{idAhcsBillInfo},
        #{reportNo},
        #{caseTimes},
        #{idClmChannelProcess},
        #{billNo},
        #{billClass},
        #{therapyType},
        #{startDate},
        #{endDate},
        #{hospitalCode},
        #{hospitalName},
        #{grade},
        #{isSocialInsurance},
        #{isAppointedHospital},
        #{hospitalPropertyDes},
        #{prepaidType},
        #{billCategory},
        #{billType},
        #{days},
        #{billAmount},
        #{deductibleAmount},
        #{prepaidAmount},
        #{immoderateAmount},
        #{partialDeductible},
        #{reasonableAmount},
        default,
        #{intensiveCareDays},
        #{remark},
        #{lossObjectNo},
        #{archiveTime},
        #{gender},
        #{payer},
        #{subjectCode},
        #{visitPlace},
        #{hospitalProvinceCode},
        #{hospitalProvinceName},
        #{hospitalCityCode},
        #{hospitalCityName},
        #{hospitalDistrictCode},
        #{hospitalDistrictName}
        )
    </insert>
    <insert id="addBillInfoAmend" parameterType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO">
        insert into
        clms_bill_info_amend
        ( CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_BILL_INFO_AMEND,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        BILL_NO,
        BILL_CLASS,
        THERAPY_TYPE,
        START_DATE,
        END_DATE,
        HOSPITAL_CODE,
        HOSPITAL_NAME,
        HOSPITAL_PROVINCE,
        HOSPITAL_CITY,
        GRADE,
        IS_SOCIAL_INSURANCE,
        IS_APPOINTED_HOSPITAL,
        HOSPITAL_PROPERTY_DES,
        PREPAID_TYPE,
        BILL_CATEGORY,
        BILL_TYPE,
        DAYS,
        BILL_AMOUNT,
        DEDUCTIBLE_AMOUNT,
        PREPAID_AMOUNT,
        IMMODERATE_AMOUNT,
        PARTIAL_DEDUCTIBLE,
        REASONABLE_AMOUNT,
        IS_EFFECTIVE,
        INTENSIVE_CARE_DAYS,
        remark,
        LOSS_OBJECT_NO,
        archive_time,
        gender,
        payer
        )
        values
        (#{createdBy},
        SYSDATE(),
        #{updatedBy},
        SYSDATE(),
        #{idAhcsBillInfo},
        #{reportNo},
        #{caseTimes},
        #{idClmChannelProcess},
        #{billNo},
        #{billClass},
        #{therapyType},
        #{startDate},
        #{endDate},
        #{hospitalCode},
        #{hospitalName},
        #{hospitalProvince},
        #{hospitalCity},
        #{grade},
        #{isSocialInsurance},
        #{isAppointedHospital},
        #{hospitalPropertyDes},
        #{prepaidType},
        #{billCategory},
        #{billType},
        #{days},
        #{billAmount},
        #{deductibleAmount},
        #{prepaidAmount},
        #{immoderateAmount},
        #{partialDeductible},
        #{reasonableAmount},
        default,
        #{intensiveCareDays},
        #{remark},
        #{lossObjectNo},
        #{archiveTime},
        #{gender},
        #{payer}
        )
    </insert>
    <insert id="addBillInfoList" parameterType="java.util.List">
        insert into
        CLMS_bill_info
        ( CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_BILL_INFO,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        BILL_NO,
        BILL_CLASS,
        THERAPY_TYPE,
        START_DATE,
        END_DATE,
        HOSPITAL_CODE,
        HOSPITAL_NAME,
        GRADE,
        IS_SOCIAL_INSURANCE,
        IS_APPOINTED_HOSPITAL,
        HOSPITAL_PROPERTY_DES,
        PREPAID_TYPE,
        BILL_CATEGORY,
        BILL_TYPE,
        DAYS,
        BILL_AMOUNT,
        DEDUCTIBLE_AMOUNT,
        PREPAID_AMOUNT,
        IMMODERATE_AMOUNT,
        PARTIAL_DEDUCTIBLE,
        REASONABLE_AMOUNT,
        INTENSIVE_CARE_DAYS,
        remark,
        archive_time
        )
        <foreach collection="list" item="item" index="index" separator="union all">
            select
            #{item.createdBy},
            SYSDATE(),
            #{item.updatedBy},
            SYSDATE(),
            #{item.idAhcsBillInfo},
            #{item.reportNo},
            #{item.caseTimes},
            #{item.idClmChannelProcess},
            #{item.billNo},
            #{item.billClass},
            #{item.therapyType},
            #{item.startDate},
            #{item.endDate},
            #{item.hospitalCode},
            #{item.hospitalName},
            #{item.grade},
            #{item.isSocialInsurance},
            #{item.isAppointedHospital},
            #{item.hospitalPropertyDes},
            #{item.prepaidType},
            #{item.billCategory},
            #{item.billType},
            #{item.days},
            #{item.billAmount},
            #{item.deductibleAmount},
            #{item.prepaidAmount},
            #{item.immoderateAmount},
            #{item.partialDeductible},
            #{item.reasonableAmount},
            #{item.intensiveCareDays},
            #{item.remark},
            #{item.archiveTime}
            from dual
        </foreach>
    </insert>
    <update id="modifyBillInfo" parameterType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO">
        update
        CLMS_bill_info
        set
        UPDATED_BY = #{updatedBy},
        UPDATED_DATE = SYSDATE(),
        BILL_NO = #{billNo},
        BILL_CLASS = #{billClass},
        THERAPY_TYPE = #{therapyType},
        START_DATE = #{startDate},
        END_DATE = #{endDate},
        HOSPITAL_CODE = #{hospitalCode},
        HOSPITAL_NAME = #{hospitalName},
        GRADE = #{grade},
        <if test="idClmChannelProcess != null and idClmChannelProcess != '' ">
            ID_AHCS_CHANNEL_PROCESS = #{idClmChannelProcess},
        </if>
        IS_SOCIAL_INSURANCE = #{isSocialInsurance},
        IS_APPOINTED_HOSPITAL = #{isAppointedHospital},
        HOSPITAL_PROPERTY_DES = #{hospitalPropertyDes},
        PREPAID_TYPE = #{prepaidType},
        BILL_CATEGORY = #{billCategory},
        BILL_TYPE = #{billType},
        DAYS = #{days},
        INTENSIVE_CARE_DAYS = #{intensiveCareDays},
        BILL_AMOUNT = #{billAmount},
        DEDUCTIBLE_AMOUNT = #{deductibleAmount},
        PREPAID_AMOUNT = #{prepaidAmount},
        IMMODERATE_AMOUNT = #{immoderateAmount},
        PARTIAL_DEDUCTIBLE = #{partialDeductible},
        REASONABLE_AMOUNT = #{reasonableAmount},
        remark=#{remark},
        gender=#{gender},
        payer=#{payer}
        where ID_AHCS_BILL_INFO = #{idAhcsBillInfo}
    </update>

    <update id="removeBillInfoList">
        update
        CLMS_bill_info
        set IS_EFFECTIVE = 'N',
        UPDATED_BY = #{userUM},
        UPDATED_DATE = SYSDATE()
        where ID_AHCS_BILL_INFO in
        <foreach collection="idAhcsBillInfoList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getBillInfo" resultMap="medicalBillInfoMap2"
            parameterType="com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO">
        select CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_BILL_INFO,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        BILL_NO,
        BILL_CLASS,
        THERAPY_TYPE,
        START_DATE,
        END_DATE,
        HOSPITAL_CODE,
        HOSPITAL_NAME,
        GRADE,
        IS_SOCIAL_INSURANCE,
        IS_APPOINTED_HOSPITAL,
        HOSPITAL_PROPERTY_DES,
        PREPAID_TYPE,
        BILL_CATEGORY,
        BILL_TYPE,
        DAYS,
        nvl(BILL_AMOUNT,0) BILL_AMOUNT,
        nvl(DEDUCTIBLE_AMOUNT,0) DEDUCTIBLE_AMOUNT,
        nvl(PREPAID_AMOUNT,0) PREPAID_AMOUNT,
        nvl(IMMODERATE_AMOUNT,0) IMMODERATE_AMOUNT,
        nvl(PARTIAL_DEDUCTIBLE,0) PARTIAL_DEDUCTIBLE,
        nvl(REASONABLE_AMOUNT,0) REASONABLE_AMOUNT,
        IS_EFFECTIVE,
        INTENSIVE_CARE_DAYS ,
        remark,
        LOSS_OBJECT_NO,
        gender,
        payer
        from
        CLMS_bill_info
        where ID_AHCS_BILL_INFO = #{idAhcsBillInfo} and IS_EFFECTIVE = 'Y'
    </select>

    <select id="getBillInfoByPage" resultMap="medicalBillInfoMap1"
            parameterType="com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO">
        SELECT CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_BILL_INFO,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        BILL_NO,
        BILL_CLASS,
        BILL_CATEGORY,
        BILL_TYPE,
        THERAPY_TYPE,
        (select t.value_chinese_name from clm_common_parameter t where t.collection_code='AHCS_THERAPY' and
        t.value_code=THERAPY_TYPE limit 1)THERAPY_TYPE_NAME,
        START_DATE,
        END_DATE,
        HOSPITAL_CODE,
        HOSPITAL_NAME,
        GRADE,
        IS_SOCIAL_INSURANCE,
        IS_APPOINTED_HOSPITAL,
        HOSPITAL_PROPERTY_DES,
        PREPAID_TYPE,
        DAYS,
        BILL_AMOUNT,
        DEDUCTIBLE_AMOUNT,
        PREPAID_AMOUNT,
        IMMODERATE_AMOUNT,
        PARTIAL_DEDUCTIBLE,
        REASONABLE_AMOUNT,
        IS_EFFECTIVE,
        INTENSIVE_CARE_DAYS ,
        remark,
        gender,
        payer
        from
        CLMS_bill_info
        where REPORT_NO = #{reportNo}
        and IS_EFFECTIVE = 'Y'
        <if test="caseTimes != null and caseTimes != '' ">
            and CASE_TIMES = #{caseTimes}
        </if>
        order by CREATED_DATE,BILL_NO asc
    </select>

    <select id="getMedicalBillAmount" resultType="com.paic.ncbs.claim.model.vo.settle.MedicalBillAmountVO"
            parameterType="com.paic.ncbs.claim.model.vo.settle.MedicalBillAmountVO">
        select
        THERAPY_TYPE therapyType,
        sum(REASONABLE_AMOUNT) reasonableAmountSum
        from
        CLMS_bill_info
        where
        report_no = #{reportNo}
        <if test="caseTimes != null and caseTimes != '' ">
            and CASE_TIMES = #{caseTimes}
        </if>
        <if test="prepaidType != null and prepaidType != '' ">
            and prepaid_type is not null
        </if>
        <if test="prepaidType == null or prepaidType == '' ">
            and prepaid_type is null
        </if>
        and
        IS_EFFECTIVE = 'Y'
        group by THERAPY_TYPE
    </select>

    <select id="getBillDetailList" resultMap="medicalBillDetailMap"
            parameterType="com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO">
        select CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_BILL_DETAIL,
        ID_AHCS_BILL_INFO,
        DETAIL_NO,
        INPUT_MODE,
        COST_CODE,
        BILL_AMOUNT,
        DEDUCTIBLE_AMOUNT,
        IMMODERATE_AMOUNT,
        PARTIAL_DEDUCTIBLE,
        COST_COMMENT,
        FOREIGN_BILL_AMOUNT,
        FOREIGN_IMMODERATE_AMOUNT,
        CURRENCY,
        CUR_RATE,
        IS_EFFECTIVE,
        RISK_LABEL
        from
        CLMS_bill_detail
        where ID_AHCS_BILL_INFO = #{idAhcsBillInfo} and IS_EFFECTIVE = 'Y' order by DETAIL_NO
    </select>

    <select id="validBillExist" resultType="com.paic.ncbs.claim.model.vo.settle.CompareBillNoResultVO"
            parameterType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO">
        select concat(REPORT_NO,'_',case_times) reportNo ,
        ID_AHCS_BILL_INFO idAhcsBillInfo
        from
        CLMS_bill_info
        where bill_no = #{billNo}
        and report_no=#{reportNo}
        and IS_EFFECTIVE = 'Y'
        <if test="lossObjectNo != null and lossObjectNo != '' ">
            and LOSS_OBJECT_NO = #{lossObjectNo}
        </if>
        limit 1
    </select>

    <!--获取医疗账单明细，用于打印客户通知书 -->
    <select id="getMedicalBillInfoForPrint" resultMap="medicalBillInfoMap2">
        SELECT CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_BILL_INFO,
        REPORT_NO,
        CASE_TIMES,
        ID_AHCS_CHANNEL_PROCESS,
        BILL_NO,
        BILL_CLASS,
        THERAPY_TYPE,
        START_DATE,
        END_DATE,
        HOSPITAL_CODE,
        HOSPITAL_NAME,
        GRADE,
        IS_SOCIAL_INSURANCE,
        IS_APPOINTED_HOSPITAL,
        HOSPITAL_PROPERTY_DES,
        PREPAID_TYPE,
        BILL_CATEGORY,
        BILL_TYPE,
        DAYS,
        BILL_AMOUNT,
        DEDUCTIBLE_AMOUNT,
        PREPAID_AMOUNT,
        IMMODERATE_AMOUNT,
        PARTIAL_DEDUCTIBLE,
        REASONABLE_AMOUNT,
        IS_EFFECTIVE,
        INTENSIVE_CARE_DAYS,
        remark,
        LOSS_OBJECT_NO,
        gender,
        payer
        FROM CLMS_BILL_INFO
        WHERE IS_EFFECTIVE = 'Y'
        <if test="reportNo != null and reportNo != '' ">
            and REPORT_NO = #{reportNo}
        </if>
        <if test="caseTimes != null and caseTimes != '' ">
            and CASE_TIMES = #{caseTimes}
        </if>

    </select>

    <!-- 通过报案号和赔付次数获取医疗帐单录入创建人 -->
    <select id="getMedicalBillCreatedBy" resultType="string">
        select bi.CREATED_BY
        from CLMS_bill_info bi
        where bi.REPORT_NO = #{reportNo}
        and bi.CASE_TIMES = #{caseTimes}
        and bi.IS_EFFECTIVE='Y'
        limit 1
    </select>

    <select id="getBillInfoCount" resultType="Integer"
            parameterType="com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO">
        SELECT count(1) from
        CLMS_bill_info
        where REPORT_NO = #{reportNo}
        <if test="caseTimes != null and caseTimes != '' ">
            and CASE_TIMES = #{caseTimes}
        </if>
        and IS_EFFECTIVE = 'Y'
    </select>

    <select id="getMedicalBillAllAmount" resultType="com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO">
        select
        sum(REASONABLE_AMOUNT) reasonableAmount
        from
        CLMS_bill_info
        where
        report_no = #{reportNo}
        and CASE_TIMES = #{caseTimes}
        <if test="lossObjectNo != null and lossObjectNo != '' ">
            and LOSS_OBJECT_NO = #{lossObjectNo}
        </if>
        and
        IS_EFFECTIVE = 'Y'
    </select>

    <select id="getMedicalBillTotal" resultType="com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO">
        select
        sum(BILL_AMOUNT) billAmount
        from
        CLMS_bill_info
        where
        report_no = #{reportNo}
        and CASE_TIMES = #{caseTimes}
        and
        IS_EFFECTIVE = 'Y'
    </select>

    <update id="clearBillInfoAmonut">
        update
        CLMS_bill_info
        set
        UPDATED_BY = #{userId},
        UPDATED_DATE = SYSDATE(),
        deductible_amount=0 ,
        immoderate_amount=0,
        partial_deductible=0,
        reasonable_amount=(bill_amount-prepaid_amount )
        <!--  (select bill_amount-prepaid_amount
                from CLMS_bill_info where ID_AHCS_BILL_INFO=t.ID_AHCS_BILL_INFO) -->
        where report_no=#{reportNo}
        and case_times=#{caseTimes}
        <if test="lossObjectNo != null and lossObjectNo != '' ">
            and LOSS_OBJECT_NO = #{lossObjectNo}
        </if>
        and IS_EFFECTIVE='Y'
    </update>

    <select id="getIdBillInfoList" resultType="string">
        select
        ID_AHCS_BILL_INFO
        from
        CLMS_bill_info
        where
        report_no = #{reportNo}
        <if test="caseTimes != null and caseTimes != '' ">
            and CASE_TIMES = #{caseTimes}
        </if>
        and
        IS_EFFECTIVE = 'Y'
    </select>

    <select id="getBillClassByReportNo" resultType="string">
        select distinct
        THERAPY_TYPE
        from
        CLMS_bill_info
        where
        report_no = #{reportNo}
        <if test="caseTimes != null and caseTimes != '' ">
            and CASE_TIMES = #{caseTimes}
        </if>
        and
        IS_EFFECTIVE = 'Y'
    </select>

    <select id="getMedicalBillAllSumAmount" resultType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoPageVO"
            parameterType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoPageVO">
        select
        sum(bill_amount) billAmountSum,
        sum(reasonable_amount) reasonableAmountSum,
        sum(if(THERAPY_TYPE = 'THE_0301',REASONABLE_AMOUNT,0)) outpatientReasonableAmountSum,
        sum(if(THERAPY_TYPE = 'THE_0302',REASONABLE_AMOUNT,0)) hospitalReasonableAmountSum,
        sum(deductible_amount) deductibleAmountSum,
        sum(partial_deductible) partialDeductibleSum,
        sum(prepaid_amount) prepaidAmountSum,
        sum(immoderate_amount) immoderateAmountSum,
        GROUP_CONCAT( DISTINCT THERAPY_TYPE SEPARATOR ';' ) AS therapyType
        from
        clms_bill_info
        where
        report_no = #{reportNo}
        <if test="caseTimes != null and caseTimes != '' ">
            and CASE_TIMES = #{caseTimes}
        </if>
        and IS_EFFECTIVE = 'Y'
    </select>
    <select id="getMedicalBillAllSumAmountnew" resultType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoPageVO"
            parameterType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoPageVO">
        select  START_DATE billDate,
        nvl(sum(bill_amount),0) billAmountSum,
        nvl(sum(reasonable_amount),0) reasonableAmountSum,
        nvl(sum(if(THERAPY_TYPE = 'THE_0301',REASONABLE_AMOUNT,0)) outpatientReasonableAmountSum,
        nvl( sum(if(THERAPY_TYPE = 'THE_0302',REASONABLE_AMOUNT,0)) hospitalReasonableAmountSum,
        nvl(sum(deductible_amount),0) deductibleAmountSum,
        nvl(sum(partial_deductible),0) partialDeductibleSum,
        nvl(sum(prepaid_amount),0) prepaidAmountSum,
        nvl(sum(immoderate_amount),0) immoderateAmountSum
        from
        clms_bill_info
        where
        report_no = #{reportNo}
        <if test="caseTimes != null and caseTimes != '' ">
            and CASE_TIMES = #{caseTimes}
        </if>
        and IS_EFFECTIVE = 'Y'
    </select>

    <select id="getBillSpecialByBillId" parameterType="java.lang.String" resultMap="MedicalBillSpecialDTOMap">
        select
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_BILL_SPECIAL,
        ID_AHCS_BILL_INFO,
        PAYMENT_A,
        PAYMENT_FROM,
        CAP_AMOUNT,
        PAYMENT_B,
        INDIVIDUAL_PAYMENT,
        PERSONAL_AMOUNT,
        ACCOUNT_PAYMENT,
        ACCOUNT_BALANCE,
        TOTAL_MEDICAL_AMOUNT,
        INSURANCE_TOTAL_PAYMENT,
        ANNUAL_TOTAL_PAYMENT,
        CLINIC_LARGE_PAYMENT,
        ANNUAL_PAYMENT_BALANCE,
        IS_EFFECTIVE
        from
        CLMS_bill_special
        where ID_AHCS_BILL_INFO = #{idAhcsBillInfo} and IS_EFFECTIVE = 'Y'
    </select>

    <select id="getSameBillNoReportNo" resultType="com.paic.ncbs.claim.model.vo.settle.CompareBillNoResultVO"
            parameterType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO">
        select
        concat(REPORT_NO,'_',case_times) reportNo ,
        ID_AHCS_BILL_INFO idAhcsBillInfo
        from
        CLMS_bill_info
        where bill_no = #{billNo}
        <if test="lossObjectNo != null and lossObjectNo != '' ">
            and LOSS_OBJECT_NO = #{lossObjectNo}
        </if>
        and IS_EFFECTIVE = 'Y'
    </select>

    <select id="getSameBillOnlyReportNo" resultType="java.lang.String"
            parameterType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO">
        select
        distinct report_no
        from
        CLMS_bill_info
        where bill_no = #{billNo}
        and report_no != #{reportNo}
        and IS_EFFECTIVE = 'Y'
    </select>

    <select id="getOtherCaseSameBillNo" resultType="com.paic.ncbs.claim.model.vo.settle.CompareBillNoResultVO">
        select distinct t1.bill_no billNo,t1.report_no reportNo, t1.case_times caseTimes
        from CLMS_bill_info t1
        where t1.is_effective = 'Y'
        <if test="lossObjectNo != null and lossObjectNo != '' ">
            and t1.loss_object_no = #{lossObjectNo}
        </if>
        and t1.bill_no in (select distinct t.bill_no
        from CLMS_bill_info t
        where t.report_no = #{reportNo}
        <if test="lossObjectNo != null and lossObjectNo != '' ">
            and t.loss_object_no = #{lossObjectNo}
        </if>
        and t.IS_EFFECTIVE = 'Y')

        and not exists (select 1
        from CLMS_bill_info t2
        where t1.report_no = t2.report_no
        <if test="lossObjectNo != null and lossObjectNo != '' ">
            and t2.loss_object_no = #{lossObjectNo}
        </if>
        and t2.report_no = #{reportNo})
        limit 10
    </select>

    <!-- 获取住院的信息 -->
    <select id="getMedicalBillInfoByReportNo" resultMap="resultMedicalBillInfo">
        select date_format(t.start_date,'%Y%m%d') START_DATE_STR,
        date_format(t.end_date,'%Y%m%d') END_DATE_STR,
        t.days,
        t.prepaid_type
        from CLMS_bill_info t
        where t.report_no = #{reportNo}
        and t.case_times = #{caseTimes}
        and t.therapy_type = 'THE_0302'
        and t.is_effective='Y'
        limit 1
    </select>

    <!-- 查询被保险人某年度下的，门诊或住院最新（新增）的医疗年度累计值 -->
    <select id="getPreInsuranceTotalPayment" resultType="java.math.BigDecimal"
            parameterType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO">
        select a.insurance_total_payment
        from (select t4.insurance_total_payment
        from CLMS_report_customer t1,
        CLMS_report_customer t2,
        CLMS_bill_info t3,
        CLMS_bill_special t4
        where t1.client_no = t2.client_no
        and t1.report_no = #{reportNo}
        and t2.report_no = t3.report_no
        and t3.is_effective = 'Y'
        and t3.prepaid_type in ('PT_2701', 'PT_2702')
        <![CDATA[and t3.PREPAID_AMOUNT>0]]>
        and t3.therapy_type = #{therapyType}
        <if test="therapyType != null and therapyType=='THE_0301'">
            and date_format(t3.start_date,'%Y')=date_format(#{startDate},'%Y')
        </if>
        <if test="therapyType != null and therapyType=='THE_0302'">
            and date_format(t3.END_DATE,'%Y')=date_format(#{endDate},'%Y')
        </if>
        and t3.id_ahcs_bill_info = t4.id_ahcs_bill_info
        and t4.is_effective = 'Y'
        order by t3.created_date desc)a
        limit 1
    </select>

    <!-- 更新客户 门诊或住院的某年度的年度医疗保险基金累计支付金额为最新值-->
    <update id="updateInsuranceTotalPayment">
        update CLMS_bill_special set
        insurance_total_payment=(insurance_total_payment+#{insuranceTotalPayment}), UPDATED_BY =
        #{userUM},
        UPDATED_DATE = SYSDATE() where is_effective='Y' and IS_AUTO_VALUE='Y'
        and id_ahcs_bill_info in
        <foreach collection="idAhcsBillInfoList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <!-- 查询以某发票为基准，往后找出该被保险人该年度下的、门诊或住院第一张的医疗年度累计值为非自动值 的新增发票-->
    <select id="getTargetBillId" resultType="String" parameterType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO">
        select a.id_ahcs_bill_info from (
        select t3.id_ahcs_bill_info
        from CLMS_report_customer t1,
        CLMS_report_customer t2,
        CLMS_bill_info t3,
        CLMS_bill_special t4
        where t1.client_no = t2.client_no
        and t1.report_no = #{reportNo}
        and t2.report_no = t3.report_no
        and t3.is_effective = 'Y'
        <![CDATA[and t3.PREPAID_AMOUNT>0]]>
        and t3.prepaid_type in ('PT_2701', 'PT_2702')
        and t3.therapy_type = #{therapyType}
        <if test="therapyType != null and therapyType=='THE_0301'">
            and date_format(t3.start_date,'%Y')=date_format(#{startDate},'%Y')
        </if>
        <if test="therapyType != null and therapyType=='THE_0302'">
            and date_format(t3.END_DATE,'%Y')=date_format(#{endDate},'%Y')
        </if>
        and t3.id_ahcs_bill_info = t4.id_ahcs_bill_info
        <![CDATA[ and t3.CREATED_DATE>=(select t5.CREATED_DATE from CLMS_bill_info t5 where t5.id_ahcs_bill_info=#{idAhcsBillInfo})]]>
        and t4.is_effective = 'Y'
        and not exists(select 1 from CLMS_bill_info t6 where t6.id_ahcs_bill_info=t3.id_ahcs_bill_info and
        t6.id_ahcs_bill_info=#{idAhcsBillInfo})
        and (t4.IS_AUTO_VALUE='N' or t4.IS_AUTO_VALUE is null)
        order by t3.CREATED_DATE desc)a
        limit 1
    </select>

    <!-- 查询以某发票为基准，往前找出该被保险人该年度下的、门诊或住院最新（新增）的医疗年度累计值-->
    <select id="getPreInsuranceTotalPaymentById" resultType="java.math.BigDecimal"
            parameterType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO">
        select insurance_total_payment from (
        select t4.insurance_total_payment
        from CLMS_report_customer t1,
        CLMS_report_customer t2,
        CLMS_bill_info t3,
        CLMS_bill_special t4
        where t1.client_no = t2.client_no
        and t1.report_no = #{reportNo}
        and t2.report_no = t3.report_no
        and t3.is_effective = 'Y'
        and t3.prepaid_type in ('PT_2701', 'PT_2702')
        <![CDATA[and t3.PREPAID_AMOUNT>0]]>
        and t3.therapy_type = #{therapyType}
        <if test="therapyType != null and therapyType=='THE_0301'">
            and date_format(t3.start_date,'%Y')=date_format(#{startDate},'%Y')
        </if>
        <if test="therapyType != null and therapyType=='THE_0302'">
            and date_format(t3.END_DATE,'%Y')=date_format(#{endDate},'%Y')
        </if>
        and t3.id_ahcs_bill_info = t4.id_ahcs_bill_info
        <![CDATA[ and t3.CREATED_DATE<=(select t5.CREATED_DATE from CLMS_bill_info t5 where t5.id_ahcs_bill_info=#{idAhcsBillInfo})]]>
        and t4.is_effective = 'Y'
        and not exists(select 1 from CLMS_bill_info t6 where t6.id_ahcs_bill_info=t3.id_ahcs_bill_info and
        t6.id_ahcs_bill_info=#{idAhcsBillInfo})
        order by t3.CREATED_DATE desc)limit 1
    </select>

    <!-- 查询以某发票为基准，往后找出该被保险人该年度下的、门诊或住院最近的，且不为非自动值（新增）的医疗年度累计值-->
    <select id="getSufPrepaidAmountById" resultType="java.math.BigDecimal"
            parameterType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO">
        select PREPAID_AMOUNT from (
        select t3.PREPAID_AMOUNT
        from CLMS_report_customer t1,
        CLMS_report_customer t2,
        CLMS_bill_info t3,
        CLMS_bill_special t4
        where t1.client_no = t2.client_no
        and t1.report_no = #{reportNo}
        and t2.report_no = t3.report_no
        and t3.is_effective = 'Y'
        and t3.prepaid_type in ('PT_2701', 'PT_2702')
        <![CDATA[ and t3.PREPAID_AMOUNT>0]]>
        and t3.therapy_type = #{therapyType}
        <if test="therapyType != null and therapyType=='THE_0301'">
            and date_format(t3.start_date,'%Y')=date_format(#{startDate},'%Y')
        </if>
        <if test="therapyType != null and therapyType=='THE_0302'">
            and date_format(t3.END_DATE,'%Y')=date_format(#{endDate},'%Y')
        </if>
        and t3.id_ahcs_bill_info = t4.id_ahcs_bill_info
        <![CDATA[ and t3.CREATED_DATE>=(select t5.CREATED_DATE from CLMS_bill_info t5 where t5.id_ahcs_bill_info=#{idAhcsBillInfo})]]>
        and t4.is_effective = 'Y'
        and not exists(select 1 from CLMS_bill_info t6 where t6.id_ahcs_bill_info=t3.id_ahcs_bill_info and
        t6.id_ahcs_bill_info=#{idAhcsBillInfo})
        order by t3.CREATED_DATE)limit 1
    </select>

    <!-- 查询以某发票为基准，往后找出新增的发票，直到该被保险人该年度下的、门诊或住院的医疗年度累计值非自动值  的发票-->
    <select id="getBillIdForUpdateInnsuranceTotal" resultType="String"
            parameterType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO">
        select t3.id_ahcs_bill_info
        from CLMS_report_customer t1,
        CLMS_report_customer t2,
        CLMS_bill_info t3,
        CLMS_bill_special t4
        where t1.client_no = t2.client_no
        and t1.report_no = #{reportNo}
        and t2.report_no = t3.report_no
        and t3.is_effective = 'Y'
        <![CDATA[ and t3.PREPAID_AMOUNT>0]]>
        and t3.prepaid_type in ('PT_2701', 'PT_2702')
        and t3.therapy_type = #{therapyType}
        <if test="therapyType != null and therapyType=='THE_0301'">
            and date_format(t3.start_date,'%Y')=date_format(#{startDate},'%Y')
        </if>
        <if test="therapyType != null and therapyType=='THE_0302'">
            and date_format(t3.END_DATE,'%Y')=date_format(#{endDate},'%Y')
        </if>
        <![CDATA[and t3.CREATED_DATE>=(select t5.CREATED_DATE from CLMS_bill_info t5 where t5.id_ahcs_bill_info=#{idAhcsBillInfo})]]>
        and not exists(select 1 from CLMS_bill_info t6 where t6.id_ahcs_bill_info=t3.id_ahcs_bill_info and
        t6.id_ahcs_bill_info=#{idAhcsBillInfo})
        <if test="targetIdAhcsBillInfo != null and targetIdAhcsBillInfo!=''">
            <![CDATA[and t3.CREATED_DATE<=(select t7.CREATED_DATE from CLMS_bill_info t7 where t7.id_ahcs_bill_info=#{targetIdAhcsBillInfo})]]>
            and not exists(select 1 from CLMS_bill_info t8 where t8.id_ahcs_bill_info=t3.id_ahcs_bill_info and
            t8.id_ahcs_bill_info=#{targetIdAhcsBillInfo})
        </if>
        and t3.id_ahcs_bill_info = t4.id_ahcs_bill_info
        and t4.is_effective = 'Y'
        and t4.IS_AUTO_VALUE='Y'
        order by t3.CREATED_DATE
    </select>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.BillCopyDTO">
        INSERT INTO CLMS_BILL_INFO (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_AHCS_BILL_INFO,
            REPORT_NO,
            CASE_TIMES,
            ID_AHCS_CHANNEL_PROCESS,
            BILL_NO,
            BILL_CLASS,
            THERAPY_TYPE,
            START_DATE,
            END_DATE,
            HOSPITAL_CODE,
            HOSPITAL_NAME,
            GRADE,
            IS_SOCIAL_INSURANCE,
            IS_APPOINTED_HOSPITAL,
            HOSPITAL_PROPERTY_DES,
            PREPAID_TYPE,
            BILL_CATEGORY,
            BILL_TYPE,
            DAYS,
            BILL_AMOUNT,
            DEDUCTIBLE_AMOUNT,
            PREPAID_AMOUNT,
            IMMODERATE_AMOUNT,
            PARTIAL_DEDUCTIBLE,
            REASONABLE_AMOUNT,
            IS_EFFECTIVE,
            INTENSIVE_CARE_DAYS,
            REMARK,
            ARCHIVE_TIME,
            LOSS_OBJECT_NO
        )
        <foreach collection="paramList" item="item" open="(" separator="union all" close=")">
            SELECT
                #{item.userId},
                NOW(),
                #{item.userId},
                NOW(),
                #{item.reopenIdAhcsBillInfo},
                REPORT_NO,
                #{item.reopenCaseTimes},
                #{item.idClmChannelProcess},
                BILL_NO,
                BILL_CLASS,
                THERAPY_TYPE,
                START_DATE,
                END_DATE,
                HOSPITAL_CODE,
                HOSPITAL_NAME,
                GRADE,
                IS_SOCIAL_INSURANCE,
                IS_APPOINTED_HOSPITAL,
                HOSPITAL_PROPERTY_DES,
                PREPAID_TYPE,
                BILL_CATEGORY,
                BILL_TYPE,
                DAYS,
                BILL_AMOUNT,
                DEDUCTIBLE_AMOUNT,
                PREPAID_AMOUNT,
                IMMODERATE_AMOUNT,
                PARTIAL_DEDUCTIBLE,
                REASONABLE_AMOUNT,
                IS_EFFECTIVE,
                INTENSIVE_CARE_DAYS,
                REMARK,
                NOW(),
                LOSS_OBJECT_NO
            FROM CLMS_BILL_INFO
            WHERE ID_AHCS_BILL_INFO = #{item.idAhcsBillInfo} AND IS_EFFECTIVE = 'Y'
        </foreach>
    </insert>

    <select id="getPersonDiagnoseList"  resultMap="result">
        select t1.ID_AHCS_PERSON_DIAGNOSE,
        t1.REPORT_NO,
        t1.CASE_TIMES,
        t1.ID_AHCS_CHANNEL_PROCESS,
        t1.DISPLAY_NO,
        t1.DIAGNOSE_CODE,
        (SELECT t2.DIAGNOSE_NAME
        FROM CLMS_diagnose_define T2
        WHERE T2.DIAGNOSE_CODE = T1.DIAGNOSE_CODE limit 1) DIAGNOSE_NAME,
        t1.IS_SURGICAL,
        t1.SURGICAL_NAME,
        t1.SURGICAL_CODE,
        t1.IS_FERTILITY,
        t1.SURGICAL_TYPE,
        t1.diagnostic_typology_code,
        (case when t1.IS_MAIN = 'Y' then '是'
            when t1.IS_MAIN = 'N' then '否'
            else '' end) as IS_MAIN
        from CLMS_person_diagnose t1
        where t1.REPORT_NO = #{reportNo}
        and t1.CASE_TIMES = #{caseTimes}
        <if test="taskId != null and taskId != ''">
        and t1.TASK_ID = ( SELECT a.TASK_ID
        FROM (SELECT T.TASK_ID
        FROM CLMS_CASE_CLASS T
        WHERE T.REPORT_NO = #{reportNo}
        AND T.CASE_TIMES = #{caseTimes}
        and T.TASK_ID != 'additionalSurveyProcessing'
        AND T.IS_EFFECTIVE = 'Y'
        order by t.CREATED_DATE desc) a
        limit 1)
        </if>
        AND t1.IS_EFFECTIVE = 'Y'
    </select>

    <!--<insert id="saveDiagnoseHospitalBillAssociation" parameterType="com.paic.ncbs.claim.model.dto.duty.DiagnoseHospitalBillAssociationDTO">
        INSERT INTO clms_diagnose_hospital_bill_association (
        id_clms_diagnose_hospital_bill_association,
        display_no,
        diagnose_code,
        is_fertility,
        is_surgical,
        surgical_name,
        surgical_type,
        surgical_code,
        is_effective,
        diagnostic_typology_code,
        created_by,
        created_date,
        updated_by,
        updated_date,
        id_clms_bill_info)
        values(
        #{idClmsDiagnoseHospitalBillAssociation},
        #{displayNo},
        #{diagnoseCode},
        #{isFertility},
        #{isSurgical},
        #{surgicalName},
        #{surgicalType},
        #{surgicalCode},
        'Y',
        #{diagnosticTypologyCode},
        #{createdBy},
        NOW(),
        #{updatedBy},
        NOW(),
        #{idClmsBillInfo}
        )
    </insert>-->

  <!--  <update id="updateDiagnoseHospitalBillAssociation">
        update clms_diagnose_hospital_bill_association set is_effective = 'N'
        where id_clms_bill_info = #{idAhcsBillInfo}
        and is_effective = 'Y'
    </update>-->

    <!--<select id="getDiagnoseHospitalBillAssociation" resultType="com.paic.ncbs.claim.model.dto.duty.DiagnoseHospitalBillAssociationDTO">
        select
        T1.id_clms_diagnose_hospital_bill_association,
        T1.display_no,
        T1.diagnose_code,
        (SELECT t2.diagnose_name
        FROM CLMS_diagnose_define T2
        WHERE T2.diagnose_code = T1.diagnose_code limit 1) diagnose_name,
        T1.is_fertility,
        T1.is_surgical,
        T1.surgical_name,
        T1.surgical_type,
        T1.surgical_code,
        T1.is_effective,
        T1.diagnostic_typology_code,
        T1.created_by,
        T1.created_date,
        T1.updated_by,
        T1.updated_date,
        T1.id_clms_bill_info
        from clms_diagnose_hospital_bill_association T1
        where T1.id_clms_bill_info = #{idAhcsBillInfo}
        and T1.is_effective = 'Y'
    </select>-->

    <select id="getIsSocBillInfoList" resultType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO">
        select START_DATE startDate,THERAPY_TYPE therapyType,
        nvl(sum(bill_amount),0) billAmount,
        nvl(sum(DEDUCTIBLE_AMOUNT),0) deductibleAmount,
        nvl(sum(PREPAID_AMOUNT),0) prepaidAmount,
        nvl(sum(IMMODERATE_AMOUNT),0) immoderateAmount,
        nvl(sum(PARTIAL_DEDUCTIBLE),0) partialDeductible,
        nvl(sum(REASONABLE_AMOUNT),0) reasonableAmount
        from clms_bill_info
        where report_no=#{reportNo}
        and case_times=#{caseTimes}
        and IS_EFFECTIVE='Y'
        and BILL_TYPE in
        <foreach collection="list" open="(" close=")" separator="," item="billType">
            #{billType}
        </foreach>
        GROUP BY START_DATE,THERAPY_TYPE
    </select>
    <select id="getNoSocBillInfoList" resultType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO">
        select START_DATE startDate,THERAPY_TYPE therapyType,
        nvl(sum(bill_amount),0) billAmount,
        nvl(sum(DEDUCTIBLE_AMOUNT),0) deductibleAmount,
        nvl(sum(PREPAID_AMOUNT),0) prepaidAmount,
        nvl(sum(IMMODERATE_AMOUNT),0) immoderateAmount,
        nvl(sum(PARTIAL_DEDUCTIBLE),0) partialDeductible,
        nvl(sum(REASONABLE_AMOUNT),0) reasonableAmount
        from clms_bill_info
        where report_no=#{reportNo}
        and case_times=#{caseTimes}
        and IS_EFFECTIVE='Y'
        and BILL_TYPE not in
        <foreach collection="list" open="(" close=")" separator="," item="billType">
            #{billType}
        </foreach>
        GROUP BY START_DATE,THERAPY_TYPE
    </select>

    <select id="getBillInfoGroupDateList" resultType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO">
        select START_DATE startDate,THERAPY_TYPE therapyType,BILL_TYPE billType,
        nvl(sum(bill_amount),0) billAmount,
        nvl(sum(DEDUCTIBLE_AMOUNT),0) deductibleAmount,
        nvl(sum(PREPAID_AMOUNT),0) prepaidAmount,
        nvl(sum(IMMODERATE_AMOUNT),0) immoderateAmount,
        nvl(sum(PARTIAL_DEDUCTIBLE),0) partialDeductible,
        nvl(sum(REASONABLE_AMOUNT),0) reasonableAmount
        from clms_bill_info
        where report_no=#{reportNo}
        and case_times=#{caseTimes}
        and IS_EFFECTIVE='Y'
        GROUP BY START_DATE,THERAPY_TYPE,BILL_TYPE
    </select>
    <!-- 查询保单历史报案发票金额信息-->
    <select id="getPolicyHistoryBillInfo" resultType="com.paic.ncbs.claim.model.vo.settle.MedicalBillHistoryVo">
        select  b.report_no reportNo,b.case_times caseTimes ,c.bill_amount billAmount,
        c.DEDUCTIBLE_AMOUNT deductibleAmount,
        c.PREPAID_AMOUNT prepaidAmount,
        c.IMMODERATE_AMOUNT immoderateAmount,
        c.PARTIAL_DEDUCTIBLE partialDeductible
        from clms_policy_info a ,clms_case_process b,clms_bill_info c
        where a.REPORT_NO=b.REPORT_NO and b.REPORT_NO=c.REPORT_NO and b.CASE_TIMES=c.CASE_TIMES
        and c.is_effective='Y' and b.PROCESS_STATUS='05'
        and a.policy_no=#{policyNo}
    </select>
    <!--  查询发票日期-->
    <select id="getBillDateList" resultType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO">
        select  DISTINCT START_DATE startDate
        from clms_bill_info
        where report_no=#{reportNo}
        and case_times=#{caseTimes}
        and IS_EFFECTIVE='Y'
    </select>
    <select id="getAllBillInfo" resultType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO">
        select  BILL_NO billNo ,gender gender,payer payer,START_DATE startDate,END_DATE endDate,HOSPITAL_PROPERTY_DES hospitalPropertyDes
        from clms_bill_info
        where report_no=#{reportNo}
        and case_times=#{caseTimes}
        and IS_EFFECTIVE='Y'
    </select>

    <select id="getThisAllBillDate" resultType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO">
        select  distinct START_DATE startDate
        from clms_bill_info
        where report_no=#{reportNo}
        and case_times=#{caseTimes}
        and IS_EFFECTIVE='Y'
    </select>
    <select id="getOtherAllBillDate" resultType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO">
        select distinct
            cbi.report_no reportNo,
            cbi.START_DATE startDate
        from clms_bill_info cbi
        left join CLMS_REPORT_CUSTOMER crc on cbi.report_no = crc.report_no
        where crc.client_no = #{clientNo}
        and cbi.IS_EFFECTIVE ='Y'
        and cbi.report_no <![CDATA[ <> ]]> #{reportNo}
        order by cbi.START_DATE desc
    </select>
    <select id="getOtherBillDateByClientInfo" resultType="com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO">
        select
            distinct
            cbi.report_no,
            cbi.START_DATE
        from clms_bill_info cbi
        left join CLMS_RISK_PROPERTY_CASE crpc on cbi.report_no = crpc.report_no
        where crpc.certificate_type = #{certificateType}
            and crpc.certificate_no = #{certificateNo}
            and crpc.name = #{name}
            and cbi.IS_EFFECTIVE ='Y'
    </select>
    <select id="getBIllNo" resultType="java.lang.String">
        select BILL_NO billNo from clms_bill_info
        where report_no=#{reportNo}
        and case_times=#{caseTimes}
        and IS_EFFECTIVE='Y'
        and THERAPY_TYPE='THE_0301'
        and START_DATE <![CDATA[<=]]>#{waitendDate}
        and START_DATE <![CDATA[>=]]>#{insuranceBeginDate}
    </select>
</mapper>