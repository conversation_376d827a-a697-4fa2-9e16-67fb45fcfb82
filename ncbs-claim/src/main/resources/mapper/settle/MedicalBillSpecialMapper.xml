<?xml version =  "1.0" encoding =  "UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace = "com.paic.ncbs.claim.dao.mapper.settle.MedicalBillSpecialMapper">
	<resultMap type = "com.paic.ncbs.claim.model.dto.settle.MedicalBillSpecialDTO" id = "medicalBillSpecialMap">
		<id column = "ID_AHCS_BILL_SPECIAL" property = "idAhcsBillSpecial"/>
		<id column = "ID_AHCS_BILL_INFO" property = "idAhcsBillInfo"/>
	  	<result column = "CREATED_BY" property = "createdBy"/>
	  	<result column = "CREATED_DATE" property = "createdDate"/>  	
	  	<result column = "UPDATED_BY" property = "updatedBy"/>
	  	<result column = "UPDATED_DATE" property = "updatedDate"/>  	
	  	<result column = "ID_AHCS_BILL_SPECIAL" property = "idAhcsBillSpecial"/>  	
	  	<result column = "ID_AHCS_BILL_INFO" property = "idAhcsBillInfo"/>
		<result column = "PROVER" property = "prover" />
		<result column = "MEDICAL_AMOUNT" property = "medicalAmount" />
		<result column = "INSURANCE_PAYMENT" property = "insurancePayment" />
		<result column = "CLINIC_LARGE_PAYMENT" property = "clinicLargePayment"/>
		<result column = "FUND_PAYMENT" property = "fundPayment" />
		<result column = "LARGE_MUTUAL_FUND_A" property = "largeMutualFundA" />
		<result column = "LARGE_MUTUAL_FUND_B" property = "largeMutualFundB" />
		<result column = "LARGE_MEDICAL_AMOUNT" property = "largeMedicalAmount" />
		<result column = "BALANCE_AMOUNT" property = "balanceAmount" />
		<result column = "RETIRE_SUPPLY_INSURANCE" property = "retireSupplyInsurance" />
		<result column = "DISABLED_VETS_ALLOWANCE" property = "disabledVetsAllowance" />
		<result column = "CIVIL_SERVANT_MEDICAL_ALLOWANCE" property = "civilServantMedicalAllowance" />
		<result column = "SELF_PAYMENT" property = "selfPayment" />
	  	<result column = "PAYMENT_A" property = "paymentA"/>
	  	<result column = "PAYMENT_FROM" property = "paymentFrom"/> 
	  	<result column = "CAP_AMOUNT" property = "capAmount"/>
		<result column = "OUTSIDE_MEDICAL_AMOUNT" property = "outsideMedicalAmount" />
	  	<result column = "PAYMENT_B" property = "paymentB"/>
	  	<result column = "INDIVIDUAL_PAYMENT" property = "individualPayment"/>  	
	  	<result column = "PERSONAL_AMOUNT" property = "personalAmount"/>  	
	  	<result column = "ACCOUNT_PAYMENT" property = "accountPayment"/>
	  	<result column = "ACCOUNT_BALANCE" property = "accountBalance"/>  	
	  	<result column = "TOTAL_MEDICAL_AMOUNT" property = "totalMedicalAmount"/>  	
	  	<result column = "INSURANCE_TOTAL_PAYMENT" property = "insuranceTotalPayment"/>
		<result column = "INSURANCE_TOTAL_PAYMENT_B" property = "insuranceTotalPaymentB" />
	  	<result column = "ANNUAL_TOTAL_PAYMENT" property = "annualTotalPayment"/>
	  	<result column = "ANNUAL_PAYMENT_BALANCE" property = "annualPaymentBalance"/>
		<result column = "TOTAL_LARGE_MUTUAL_FUND" property = "totalLargeMutualFund" />
		<result column = "TOTAL_FUND_PAYMENT" property = "totalFundPayment" />
	  	<result column = "IS_AUTO_VALUE" property = "isAutoValue"/>
	  	<result column = "IS_EFFECTIVE" property = "isEffective"/>
	  </resultMap>

	<insert id="addBillSpecial" parameterType="com.paic.ncbs.claim.model.dto.settle.MedicalBillSpecialDTO" >
		insert into 
				CLMS_BILL_SPECIAL
		 				(CREATED_BY,
		 				 CREATED_DATE,
		 				 UPDATED_BY, 
		 				 UPDATED_DATE,
		 				 ID_AHCS_BILL_SPECIAL,
		  				 ID_AHCS_BILL_INFO,
		 				 PROVER,
						 MEDICAL_AMOUNT,
						 INSURANCE_PAYMENT,
						 CLINIC_LARGE_PAYMENT,
						 FUND_PAYMENT,
						 LARGE_MUTUAL_FUND_A,
						 LARGE_MUTUAL_FUND_B,
						 LARGE_MEDICAL_AMOUNT,
						 BALANCE_AMOUNT,
						 RETIRE_SUPPLY_INSURANCE,
						 DISABLED_VETS_ALLOWANCE,
						 CIVIL_SERVANT_MEDICAL_ALLOWANCE,
						 SELF_PAYMENT,
						 PAYMENT_A,
		  				 PAYMENT_FROM, 
		  				 CAP_AMOUNT,
						 OUTSIDE_MEDICAL_AMOUNT,
		  				 PAYMENT_B, 
		  				 INDIVIDUAL_PAYMENT,
		   				 PERSONAL_AMOUNT, 
		   				 ACCOUNT_PAYMENT, 
		   				 ACCOUNT_BALANCE, 
		   				 TOTAL_MEDICAL_AMOUNT, 
		   				 INSURANCE_TOTAL_PAYMENT,
						 INSURANCE_TOTAL_PAYMENT_B,
		   				 ANNUAL_TOTAL_PAYMENT,
		   				 ANNUAL_PAYMENT_BALANCE,
						 TOTAL_LARGE_MUTUAL_FUND,
						 TOTAL_FUND_PAYMENT,
		   				 IS_AUTO_VALUE,
						 ARCHIVE_TIME)
		   	values (
		   			    #{createdBy},
		   			    SYSDATE(),
		   			    #{updatedBy},
		   			    SYSDATE(),
						left(hex(uuid()),32),
		    			#{idAhcsBillInfo},
		   	            #{prover},
		   	        	#{medicalAmount},
						#{insurancePayment},
						#{clinicLargePayment},
						#{fundPayment},
						#{largeMutualFundA},
						#{largeMutualFundB},
		   	            #{largeMedicalAmount},
						#{balanceAmount},
						#{retireSupplyInsurance},
						#{disabledVetsAllowance},
						#{civilServantMedicalAllowance},
		   	        	#{selfPayment},

		    			#{paymentA},
		    			#{paymentFrom},
		    			#{capAmount},
						#{outsideMedicalAmount},
		    			#{paymentB},
		     			#{individualPayment},

		     			#{personalAmount},
		     			#{accountPayment},
		     			#{accountBalance},

		     			#{totalMedicalAmount},
		     			#{insuranceTotalPayment},
						#{insuranceTotalPaymentB},
		     			#{annualTotalPayment},
		     			#{annualPaymentBalance},
						#{totalLargeMutualFund},
						#{totalFundPayment},
		     			#{isAutoValue},
		     			#{archiveTime})
	</insert>

	<!-- 批量增加操作 -->
  	<insert id =  "addBillSpecialList" parameterType =  "java.util.List">
  	insert into 
				CLMS_BILL_SPECIAL
		 				(CREATED_BY,
		 				 CREATED_DATE,
		 				 UPDATED_BY, 
		 				 UPDATED_DATE,
		                 ID_AHCS_BILL_SPECIAL,
		 				 ID_AHCS_BILL_INFO,
		 				PROVER,
						MEDICAL_AMOUNT,
						INSURANCE_PAYMENT,
						CLINIC_LARGE_PAYMENT,
						FUND_PAYMENT,
						LARGE_MUTUAL_FUND_A,
						LARGE_MUTUAL_FUND_B,
		                LARGE_MEDICAL_AMOUNT,
						BALANCE_AMOUNT,
						RETIRE_SUPPLY_INSURANCE,
						DISABLED_VETS_ALLOWANCE,
						CIVIL_SERVANT_MEDICAL_ALLOWANCE,
						SELF_PAYMENT,
		  				 PAYMENT_A, 
		  				 PAYMENT_FROM, 
		  				 CAP_AMOUNT,
		                 OUTSIDE_MEDICAL_AMOUNT,
		  				 PAYMENT_B, 
		  				 INDIVIDUAL_PAYMENT,
		   				 PERSONAL_AMOUNT, 
		   				 ACCOUNT_PAYMENT, 
		   				 ACCOUNT_BALANCE, 
		   				 TOTAL_MEDICAL_AMOUNT, 
		   				 INSURANCE_TOTAL_PAYMENT,
		                 INSURANCE_TOTAL_PAYMENT_B,
		   				 ANNUAL_TOTAL_PAYMENT,
		   				 ANNUAL_PAYMENT_BALANCE,
						 TOTAL_LARGE_MUTUAL_FUND,
						 TOTAL_FUND_PAYMENT,
		   				 IS_AUTO_VALUE,
		   				 archive_time)
  	<foreach collection =  "list" item =  "item" index =  "index" separator =  "union all"> 
  		select 
  			#{item.createdBy},
			SYSDATE(),
		    #{item.updatedBy},
		    SYSDATE(),
		    left(hex(uuid()),32),
		    #{item.idAhcsBillInfo},
		#{item.prover},
		#{item.medicalAmount},
		#{item.insurancePayment},
		#{item.clinicLargePayment},
		#{item.fundPayment},
		#{item.largeMutualFundA},
		#{item.largeMutualFundB},
		#{iyem.largeMedicalAmount},
		#{item.balanceAmount},
		#{item.retireSupplyInsurance},
		#{item.disabledVetsAllowance},
		#{item.civilServantMedicalAllowance},
		#{item.selfPayment},
   			#{item.paymentA},
   			#{item.paymentFrom},
   			#{item.capAmount},
		#{item.outsideMedicalAmount},
   			#{item.paymentB},
   			#{item.individualPayment},
   			#{item.personalAmount},
   			#{item.accountPayment},
   			#{item.accountBalance},
   			#{item.totalMedicalAmount},
   			#{item.insuranceTotalPayment},
		#{item.insuranceTotalPaymentB},
   			#{item.annualTotalPayment},
   			#{item.annualPaymentBalance},
		#{item.totalLargeMutualFund},
		#{item.totalFundPayment},
   			#{item.isAutoValue},
   			#{item.archiveTime}
		  from  dual
  	</foreach> 
  </insert>

	<select id="getBillSpecial" parameterType="java.lang.String" resultMap="medicalBillSpecialMap">
		select 
				ID_AHCS_BILL_SPECIAL	,
			  	ID_AHCS_BILL_INFO	,
		        PROVER,
				MEDICAL_AMOUNT,
				INSURANCE_PAYMENT,
				CLINIC_LARGE_PAYMENT,
				FUND_PAYMENT,
				LARGE_MUTUAL_FUND_A,
				LARGE_MUTUAL_FUND_B,
				LARGE_MEDICAL_AMOUNT,
				BALANCE_AMOUNT,
				RETIRE_SUPPLY_INSURANCE,
				DISABLED_VETS_ALLOWANCE,
				CIVIL_SERVANT_MEDICAL_ALLOWANCE,
				SELF_PAYMENT,
				PAYMENT_A,
				PAYMENT_FROM,
				CAP_AMOUNT,
				OUTSIDE_MEDICAL_AMOUNT,
				PAYMENT_B,
				INDIVIDUAL_PAYMENT,
				PERSONAL_AMOUNT,
				ACCOUNT_PAYMENT,
				ACCOUNT_BALANCE,
				TOTAL_MEDICAL_AMOUNT,
				INSURANCE_TOTAL_PAYMENT,
				INSURANCE_TOTAL_PAYMENT_B,
				ANNUAL_TOTAL_PAYMENT,
				ANNUAL_PAYMENT_BALANCE,
				TOTAL_LARGE_MUTUAL_FUND,
				TOTAL_FUND_PAYMENT,
			  	ifnull(IS_AUTO_VALUE,'N') IS_AUTO_VALUE,
			  	IS_EFFECTIVE	 
		from CLMS_BILL_SPECIAL
		where ID_AHCS_BILL_INFO = #{idAhcsBillInfo} and IS_EFFECTIVE = 'Y'  
	</select>

	<update id = "modifyBillSpecial" parameterType = "com.paic.ncbs.claim.model.dto.settle.MedicalBillSpecialDTO">
	  update 
	  	CLMS_BILL_SPECIAL
	  set 
	      UPDATED_BY  =  #{updatedBy},
		  UPDATED_DATE  =  SYSDATE(),
	      PROVER = #{prover},
		  MEDICAL_AMOUNT = #{medicalAmount},
		  INSURANCE_PAYMENT = #{insurancePayment},
		  CLINIC_LARGE_PAYMENT  =  #{clinicLargePayment},
		  FUND_PAYMENT = #{fundPayment},
		  LARGE_MUTUAL_FUND_A = #{largeMutualFundA},
		  LARGE_MUTUAL_FUND_B = #{largeMutualFundB},
		  LARGE_MEDICAL_AMOUNT = #{largeMedicalAmount},
		  BALANCE_AMOUNT = #{balanceAmount},
		  RETIRE_SUPPLY_INSURANCE = #{retireSupplyInsurance},
		  DISABLED_VETS_ALLOWANCE = #{disabledVetsAllowance},
		  CIVIL_SERVANT_MEDICAL_ALLOWANCE = #{civilServantMedicalAllowance},
		  SELF_PAYMENT = #{selfPayment},
		  PAYMENT_A  =  #{paymentA},
		  PAYMENT_FROM  =  #{paymentFrom},
		  CAP_AMOUNT  =  #{capAmount},
		  OUTSIDE_MEDICAL_AMOUNT = #{outsideMedicalAmount},
		  PAYMENT_B  =  #{paymentB},
		  INDIVIDUAL_PAYMENT  =  #{individualPayment},
		  PERSONAL_AMOUNT  =  #{personalAmount},
		  ACCOUNT_PAYMENT  =  #{accountPayment},
		  ACCOUNT_BALANCE  =  #{accountBalance},
		  TOTAL_MEDICAL_AMOUNT  = #{totalMedicalAmount},
		  INSURANCE_TOTAL_PAYMENT  =  #{insuranceTotalPayment},
		  ANNUAL_TOTAL_PAYMENT  =  #{annualTotalPayment},
		  ANNUAL_PAYMENT_BALANCE  =  #{annualPaymentBalance},
		  TOTAL_LARGE_MUTUAL_FUND = #{totalLargeMutualFund},
		  TOTAL_FUND_PAYMENT = #{totalFundPayment}

		where ID_AHCS_BILL_INFO = #{idAhcsBillInfo}
	</update>

	<update id = "removeBillSpecialList">
	  	update 
	  		CLMS_BILL_SPECIAL
	  	 set IS_EFFECTIVE = 'N',
	  		 UPDATED_BY  =  #{userUM}, 
			UPDATED_DATE  =  SYSDATE() where ID_AHCS_BILL_INFO in
  		<foreach collection = "idAhcsBillInfoList" item = "item" index = "index" open = "(" separator = "," close = ")"> 
	  		#{item}
	  	</foreach>
	</update>

	<insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.BillCopyDTO">
		INSERT INTO CLMS_BILL_SPECIAL (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_BILL_SPECIAL,
			ID_AHCS_BILL_INFO,
			PROVER,
			MEDICAL_AMOUNT,
			INSURANCE_PAYMENT,
			CLINIC_LARGE_PAYMENT,
			FUND_PAYMENT,
			LARGE_MUTUAL_FUND_A,
			LARGE_MUTUAL_FUND_B,
			LARGE_MEDICAL_AMOUNT,
			BALANCE_AMOUNT,
			RETIRE_SUPPLY_INSURANCE,
			DISABLED_VETS_ALLOWANCE,
			CIVIL_SERVANT_MEDICAL_ALLOWANCE,
			SELF_PAYMENT,
			PAYMENT_A,
			PAYMENT_FROM,
			CAP_AMOUNT,
			OUTSIDE_MEDICAL_AMOUNT,
			PAYMENT_B,
			INDIVIDUAL_PAYMENT,
			PERSONAL_AMOUNT,
			ACCOUNT_PAYMENT,
			ACCOUNT_BALANCE,
			TOTAL_MEDICAL_AMOUNT,
			INSURANCE_TOTAL_PAYMENT,
			INSURANCE_TOTAL_PAYMENT_B,
			ANNUAL_TOTAL_PAYMENT,
			ANNUAL_PAYMENT_BALANCE,
			TOTAL_LARGE_MUTUAL_FUND,
			TOTAL_FUND_PAYMENT,
			IS_EFFECTIVE,
			ARCHIVE_TIME,
			IS_AUTO_VALUE
		)
		<foreach collection="paramList" item="item" open="(" separator="union all" close=")">
			SELECT
				#{item.userId},
				NOW(),
				#{item.userId},
				NOW(),
				LEFT(HEX(UUID()), 32),
				#{item.reopenIdAhcsBillInfo},
				PROVER,
				MEDICAL_AMOUNT,
				INSURANCE_PAYMENT,
				CLINIC_LARGE_PAYMENT,
				FUND_PAYMENT,
				LARGE_MUTUAL_FUND_A,
				LARGE_MUTUAL_FUND_B,
				LARGE_MEDICAL_AMOUNT,
				BALANCE_AMOUNT,
				RETIRE_SUPPLY_INSURANCE,
				DISABLED_VETS_ALLOWANCE,
				CIVIL_SERVANT_MEDICAL_ALLOWANCE,
				SELF_PAYMENT,
				PAYMENT_A,
				PAYMENT_FROM,
				CAP_AMOUNT,
				OUTSIDE_MEDICAL_AMOUNT,
				PAYMENT_B,
				INDIVIDUAL_PAYMENT,
				PERSONAL_AMOUNT,
				ACCOUNT_PAYMENT,
				ACCOUNT_BALANCE,
				TOTAL_MEDICAL_AMOUNT,
				INSURANCE_TOTAL_PAYMENT,
				INSURANCE_TOTAL_PAYMENT_B,
				ANNUAL_TOTAL_PAYMENT,
				ANNUAL_PAYMENT_BALANCE,
				TOTAL_LARGE_MUTUAL_FUND,
				TOTAL_FUND_PAYMENT,
				IS_EFFECTIVE,
				NOW(),
				IS_AUTO_VALUE
			FROM CLMS_BILL_SPECIAL
			WHERE ID_AHCS_BILL_INFO = #{item.idAhcsBillInfo} AND IS_EFFECTIVE = 'Y'
		</foreach>
	</insert>
</mapper>