<?xml version = "1.0" encoding = "UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.DiagnoseHospitalBillAssociationMapper">

    <insert id="saveDiagnoseHospitalBillAssociation" parameterType="com.paic.ncbs.claim.model.dto.duty.DiagnoseHospitalBillAssociationDTO">
        INSERT INTO clms_diagnose_hospital_bill_association (
        id_clms_diagnose_hospital_bill_association,
        display_no,
        diagnose_code,
        is_fertility,
        is_surgical,
        surgical_name,
        surgical_type,
        surgical_code,
        is_effective,
        diagnostic_typology_code,
        created_by,
        created_date,
        updated_by,
        updated_date,
        id_clms_bill_info,
        IS_MAIN)
        values(
        #{idClmsDiagnoseHospitalBillAssociation},
        #{displayNo},
        #{diagnoseCode},
        #{isFertility},
        #{isSurgical},
        #{surgicalName},
        #{surgicalType},
        #{surgicalCode},
        'Y',
        #{diagnosticTypologyCode},
        #{createdBy},
        NOW(),
        #{updatedBy},
        NOW(),
        #{idClmsBillInfo},
        #{isMain}
        )
    </insert>

    <update id="updateDiagnoseHospitalBillAssociation">
        update clms_diagnose_hospital_bill_association set is_effective = 'N'
        where id_clms_bill_info = #{idAhcsBillInfo}
        and is_effective = 'Y'
    </update>

    <select id="getDiagnoseHospitalBillAssociation" resultType="com.paic.ncbs.claim.model.dto.duty.DiagnoseHospitalBillAssociationDTO">
        select
        T1.id_clms_diagnose_hospital_bill_association,
        T1.display_no,
        T1.diagnose_code,
        (SELECT t2.diagnose_name
        FROM CLMS_diagnose_define T2
        WHERE T2.diagnose_code = T1.diagnose_code limit 1) diagnose_name,
        T1.is_fertility,
        T1.is_surgical,
        T1.surgical_name,
        T1.surgical_type,
        T1.surgical_code,
        T1.is_effective,
        T1.diagnostic_typology_code,
        T1.created_by,
        T1.created_date,
        T1.updated_by,
        T1.updated_date,
        T1.id_clms_bill_info,
        (case when T1.is_main = 'Y' then '是'
            when T1.is_main = 'N' then '否'
        else '' end) as is_main,
        T1.is_effective
        from clms_diagnose_hospital_bill_association T1
        where T1.id_clms_bill_info = #{idAhcsBillInfo}
        and T1.is_effective = 'Y'
    </select>

    <select id="getDiagnoseHospitalBillAssociationList" resultType="com.paic.ncbs.claim.model.dto.duty.DiagnoseHospitalBillAssociationDTO">
        select
            id_clms_diagnose_hospital_bill_association,
            display_no,
            diagnose_code,
            is_fertility,
            is_surgical,
            surgical_name,
            surgical_type,
            surgical_code,
            is_effective,
            diagnostic_typology_code,
            id_clms_bill_info,
            is_main
        from clms_diagnose_hospital_bill_association
        where is_effective = 'Y'
            and id_clms_bill_info in
            <foreach collection="idAhcsBillInfoList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
    </select>

    <insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.BillCopyDTO">
        insert into clms_diagnose_hospital_bill_association (
        id_clms_diagnose_hospital_bill_association,
        display_no,
        diagnose_code,
        is_fertility,
        is_surgical,
        surgical_name,
        surgical_type,
        surgical_code,
        is_effective,
        diagnostic_typology_code,
        created_by,
        created_date,
        updated_by,
        updated_date,
        id_clms_bill_info,
        IS_MAIN)
        values
        <foreach collection="list" separator="," index="index" item="item">
            (
                #{item.idClmsDiagnoseHospitalBillAssociation},
                #{item.displayNo},
                #{item.diagnoseCode},
                #{item.isFertility},
                #{item.isSurgical},
                #{item.surgicalName},
                #{item.surgicalType},
                #{item.surgicalCode},
                'Y',
                #{item.diagnosticTypologyCode},
                #{item.userId},
                NOW(),
                #{item.userId},
                NOW(),
                #{item.idClmsBillInfo},
                #{item.isMain}
            )
        </foreach>
    </insert>

    <select id="getAllDiagnoseHospitalBillAssociation" resultType="com.paic.ncbs.claim.model.dto.duty.DiagnoseHospitalBillAssociationDTO">
        select
            T1.id_clms_diagnose_hospital_bill_association,
            T1.display_no,
            T1.diagnose_code,
            (SELECT t2.diagnose_name
             FROM CLMS_diagnose_define T2
             WHERE T2.diagnose_code = T1.diagnose_code limit 1) diagnose_name,
        T1.is_fertility,
        T1.is_surgical,
        T1.surgical_name,
        T1.surgical_type,
        T1.surgical_code,
        T1.is_effective,
        T1.diagnostic_typology_code,
        T1.created_by,
        T1.created_date,
        T1.updated_by,
        T1.updated_date,
        T1.id_clms_bill_info,
        (case when T1.is_main = 'Y' then '是'
            when T1.is_main = 'N' then '否'
        else '' end) as is_main,
        T1.is_effective
        from clms_diagnose_hospital_bill_association T1
        where T1.id_clms_bill_info = #{idAhcsBillInfo}
    </select>
</mapper>