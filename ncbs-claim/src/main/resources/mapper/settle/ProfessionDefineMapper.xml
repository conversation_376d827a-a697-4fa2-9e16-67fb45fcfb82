<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.ProfessionDefineMapper">
	
	<resultMap type="com.paic.ncbs.claim.model.dto.settle.SubProfessionDefineDTO" id="result1">
		<result column="PROFESSION_CODE" property="professionCode"/>
		<result column="PROFESSION_GRADE_CODE" property="professionGradeCode"/>
		<result column="PROFESSION_CHN_NAME" property="professionChnName"/>
		<result column="PROFESSION_CHN_ABBR_NAME" property="professionChnAbbrName"/>
		<result column="PROFESSION_GRADE_CHN_NAME" property="professionGradeChnName"/>
		<result column="PROFESSION_GRADE_CHN_ABBR_NAME" property="professionGradeChnAbbrName"/>
		<result column="DISPLAY_NO" property="displayNo"/>
		<result column="PARENT_CODE" property="parentCode"/>
		<collection property="subProfessionDefines" ofType="com.paic.ncbs.claim.model.dto.settle.SubProfessionDefineDTO" column="PROFESSION_CODE" select="getSubProfessionDefines">
		</collection>
	</resultMap>

	<resultMap type="com.paic.ncbs.claim.model.dto.settle.SubProfessionDefineDTO" id="result4">
		<result column="PROFESSION_CODE" property="professionCode"/>
		<result column="PROFESSION_GRADE_CODE" property="professionGradeCode"/>
		<result column="PROFESSION_CHN_NAME" property="professionChnName"/>
		<result column="PROFESSION_CHN_ABBR_NAME" property="professionChnAbbrName"/>
		<result column="PROFESSION_GRADE_CHN_NAME" property="professionGradeChnName"/>
		<result column="PROFESSION_GRADE_CHN_ABBR_NAME" property="professionGradeChnAbbrName"/>
		<result column="DISPLAY_NO" property="displayNo"/>
		<result column="PARENT_CODE" property="parentCode"/>
	</resultMap>
	
	<resultMap type="com.paic.ncbs.claim.model.dto.settle.SubProfessionDefineDTO" id="result3">
		<result column="PROFESSION_CODE" property="professionCode"/>
		<result column="PROFESSION_GRADE_CODE" property="professionGradeCode"/>
		<result column="PROFESSION_CHN_NAME" property="professionChnName"/>
		<result column="PROFESSION_CHN_ABBR_NAME" property="professionChnAbbrName"/>
		<result column="DISPLAY_NO" property="displayNo"/>
		<result column="PARENT_CODE" property="parentCode"/>
		<result column="MIGRATE_FROM" property="migrateFrom"/>
	</resultMap>
	
	<select id="getProfessionDefines" resultMap="result3">
		select t.PROFESSION_CODE,
		       t.PROFESSION_GRADE_CODE,
		       t.PROFESSION_CHN_NAME,
		       t.PROFESSION_CHN_ABBR_NAME,
		       t.DISPLAY_NO,
		       t.PARENT_CODE,
		       t.MIGRATE_FROM
		  from clms_profession_define t
		 where t.PARENT_CODE = '0'
	</select>
	
	<select id="getSubProfessionDefines" parameterType="string" resultMap="result1">
		select t1.PROFESSION_CODE,
		       t1.PROFESSION_GRADE_CODE,
		       t1.PROFESSION_CHN_NAME,
		       t1.PROFESSION_CHN_ABBR_NAME,
		       t1.DISPLAY_NO,
		       t1.PARENT_CODE,
		       t2.PROFESSION_GRADE_CHN_NAME,
		       t2.PROFESSION_GRADE_CHN_ABBR_NAME
		  from clms_profession_define t1, CLMS_PROFESSION_GRADE_DEF t2
		 where t1.PARENT_CODE = #{professionCode}
		   and t1.PROFESSION_GRADE_CODE = t2.PROFESSION_GRADE_CODE
	</select>
	
	<!-- 获取所有职业名称 -->
	<select id="getSubProfessionDefinesNameAll" resultType="String">
		select  distinct  PROFESSION_CHN_NAME from CLMS_PROFESSION_DEFINE
	</select>
	
	<select id="getProfessionLevel" resultType="String">
		select 
				t.profession_grade_code 
		from 
				clms_profession_define t
		where 
				t.profession_code=#{professionCode}
				<if test="parentCode != null and parentCode != '' ">
					and t.parent_code=#{parentCode}
		 	 	</if>
	</select>
	
	<select id="getPolicyProfession" parameterType="string" resultMap="result3">
	   select
			 distinct t1.profession_code ,
			 t3.parent_code  ,
			 t3.profession_grade_code
	   from  CLMS_insured_person t1,
			 CLMS_policy_info t2  ,
			 CLMS_profession_define t3
	   where  t1.id_ahcs_policy_info=t2.id_ahcs_policy_info
			  and t1.profession_code=t3.profession_code
			  and t2.report_no=#{reportNo}
	</select>
	
	<select id="getOldProfessionByOther" parameterType="string" resultMap="result3">
		select a.* from
			( select (select t1.profession_code from CLMS_profession_define t1  where   t.profession_chn_name=t1.profession_chn_name and t.parent_code=t1.parent_code and t1.migrate_from='o') profession_code,
					(select t1.parent_code from CLMS_profession_define t1  where   t.profession_chn_name=t1.profession_chn_name and t.parent_code=t1.parent_code and t1.migrate_from='o') parent_code,
					(select t1.profession_grade_code from CLMS_profession_define t1  where   t.profession_chn_name=t1.profession_chn_name and t.parent_code=t1.parent_code and t1.migrate_from='o') profession_grade_code
			from CLMS_profession_define t where t.profession_code=#{professionCode}  and t.migrate_from='p') a
		where profession_code is not null limit 1

	</select>

	<select id="getAllSubProfessionDefines" parameterType="string" resultMap="result4">
		select t1.PROFESSION_CODE,
			   t1.PROFESSION_GRADE_CODE,
			   t1.PROFESSION_CHN_NAME,
			   t1.PROFESSION_CHN_ABBR_NAME,
			   t1.DISPLAY_NO,
			   t1.PARENT_CODE,
			   t2.PROFESSION_GRADE_CHN_NAME,
			   t2.PROFESSION_GRADE_CHN_ABBR_NAME
		from clms_profession_define t1, CLMS_PROFESSION_GRADE_DEF t2
		where  t1.PROFESSION_GRADE_CODE = t2.PROFESSION_GRADE_CODE
	</select>

	<select id="getParentSub" parameterType="string" resultMap="result4">
		select t1.PROFESSION_CODE,
			   t1.PROFESSION_GRADE_CODE,
			   t1.PROFESSION_CHN_NAME,
			   t1.PROFESSION_CHN_ABBR_NAME,
			   t1.DISPLAY_NO,
			   t1.PARENT_CODE,
			   t2.PROFESSION_GRADE_CHN_NAME,
			   t2.PROFESSION_GRADE_CHN_ABBR_NAME
		from clms_profession_define t1, CLMS_PROFESSION_GRADE_DEF t2
		where  t1.PROFESSION_GRADE_CODE = t2.PROFESSION_GRADE_CODE
        and t1.PROFESSION_CODE= #{professionCode}  limit 1
	</select>

	<select id="getProfessiondefine" parameterType="string" resultMap="result4">
		WITH RECURSIVE clmsprofessiondefine AS (
		SELECT PARENT_CODE,PROFESSION_CODE,PROFESSION_GRADE_CODE, PROFESSION_CHN_NAME
		FROM clms_profession_define p
		WHERE p.PROFESSION_CODE =  #{professionCode}
		UNION ALL
		select e.PARENT_CODE,e.PROFESSION_CODE, e.PROFESSION_GRADE_CODE, e.PROFESSION_CHN_NAME
		FROM clms_profession_define e
		INNER JOIN clmsprofessiondefine cpd ON e.PROFESSION_CODE = cpd.PARENT_CODE
		)
		SELECT * FROM clmsprofessiondefine clm order by clm.profession_code;
	</select>

</mapper>