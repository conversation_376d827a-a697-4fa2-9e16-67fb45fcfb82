<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.PolicyClaimCaseMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.PolicyClaimCaseDTO" id="result">
        <id property="idAhcsPolicyClaimCase" column="ID_AHCS_POLICY_CLAIM_CASE"/>
        <result property="createdBy" column="CREATED_BY"/>
        <result property="createdDate" column="CREATED_DATE"/>
        <result property="updatedBy" column="UPDATED_BY"/>
        <result property="updatedDate" column="UPDATED_DATE"/>
        <result property="reportNo" column="REPORT_NO"/>
        <result property="caseNo" column="CASE_NO"/>
        <result property="policyNo" column="POLICY_NO"/>
        <result property="subpolicyNo" column="SUBPOLICY_NO"/>
        <result property="departmentCode" column="DEPARTMENT_CODE"/>
        <result property="departmentName" column="DEPARTMENT_NAME"/>
        <result property="partyNo" column="PARTY_NO"/>
        <result property="insuredCode" column="INSURED_CODE"/>
    </resultMap>

    <sql id="select">
        select
        pcc.ID_AHCS_POLICY_CLAIM_CASE ,
        pcc.POLICY_NO ,
        pcc.CASE_NO ,
        pcc.REPORT_NO ,
        pcc.SUBPOLICY_NO ,
        pcc.DEPARTMENT_CODE ,
        pcc.DEPARTMENT_NAME ,
        pcc.PARTY_NO ,
        pcc.NAME ,
        pcc.INSURED_CODE
        from CLMS_policy_claim_case pcc
    </sql>

    <insert id="insert" parameterType="com.paic.ncbs.claim.model.dto.settle.PolicyClaimCaseDTO">
        INSERT INTO CLMS_POLICY_CLAIM_CASE (
            CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_AHCS_POLICY_CLAIM_CASE,
            REPORT_NO,
            CASE_NO,
            POLICY_NO,
            SUBPOLICY_NO,
            DEPARTMENT_CODE,
            DEPARTMENT_NAME,
            PARTY_NO,
            INSURED_CODE,
            ARCHIVE_TIME
        ) VALUES (
                     #{createdBy },
                     SYSDATE(),
                     #{updatedBy },
                     SYSDATE(),
                     left(hex(uuid()),32),
                     #{reportNo },
                     #{caseNo },
                     #{policyNo },
                     #{subpolicyNo },
                     #{departmentCode },
                     #{departmentName },
                     #{partyNo },
                     #{insuredCode },
                     SYSDATE()
                 )
    </insert>

    <insert id="insertDutyPayInfo" parameterType="com.paic.ncbs.claim.model.dto.settle.PolicyClaimCaseDTO">
        INSERT INTO CLMS_POLICY_CLAIM_CASE (
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_POLICY_CLAIM_CASE,
        REPORT_NO,
        CASE_NO,
        POLICY_NO,
        SUBPOLICY_NO,
        DEPARTMENT_CODE,
        DEPARTMENT_NAME,
        PARTY_NO,
        INSURED_CODE,
        ARCHIVE_TIME
        ) VALUES (
        #{createdBy },
        SYSDATE(),
        #{updatedBy },
        SYSDATE(),
        #{idAhcsPolicyClaimCase },
        #{reportNo },
        #{caseNo },
        #{policyNo },
        #{subpolicyNo },
        #{departmentCode },
        #{departmentName },
        #{partyNo },
        #{insuredCode },
        SYSDATE()
        )
    </insert>

    <select id="selectByReportNo" resultMap="result">
        select CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        ID_AHCS_POLICY_CLAIM_CASE,
        REPORT_NO,
        CASE_NO,
        POLICY_NO,
        SUBPOLICY_NO,
        DEPARTMENT_CODE,
        DEPARTMENT_NAME,
        PARTY_NO,
        INSURED_CODE
        from CLMS_POLICY_CLAIM_CASE
        where CASE_NO=#{caseNo}
    </select>

    <select id="selectPartyNoByCaseNo" resultType="java.lang.String">
        SELECT
        PCC.PARTY_NO
        FROM CLMS_POLICY_CLAIM_CASE PCC
        WHERE
        PCC.CASE_NO=#{caseNo}
    </select>

    <select id="getByReportNoAndPolicyNo" parameterType="java.lang.String" resultMap="result">
        <include refid="select"/>
        where pcc.report_no=#{reportNo}
        and pcc.policy_no=#{policyNo}
    </select>

    <select id="getPolicyClaimCaseListByReportNo" parameterType="java.lang.String" resultMap="result">
        select pcc.CASE_NO from CLMS_policy_claim_case pcc where pcc.report_no=#{reportNo}
    </select>

    <select id="getByCaseNo" parameterType="com.paic.ncbs.claim.model.dto.settle.PolicyClaimCaseDTO" resultMap="result">
        <include refid="select"/>
        where pcc.case_no=#{caseNo} limit 1
    </select>

    <insert id="addBatchPolicyClaimCase" parameterType="java.util.List">
        insert into CLMS_policy_claim_case
        (
        ID_AHCS_POLICY_CLAIM_CASE,
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY,
        UPDATED_DATE,
        REPORT_NO,
        CASE_NO,
        SUBPOLICY_NO,
        DEPARTMENT_CODE,
        DEPARTMENT_NAME,
        POLICY_NO,
        PARTY_NO,
        NAME,
        INSURED_CODE,
        ARCHIVE_TIME
        )
        values
        <foreach collection="paramList" item="item" index="index" separator=",">
               (
                    replace(uuid(),'-',''),
                    #{item.createdBy},
                      SYSDATE(),
                    #{item.updatedBy},
                      SYSDATE(),
                    #{item.reportNo},
                    #{item.caseNo},
                    #{item.subpolicyNo},
                    #{item.departmentCode},
                    #{item.departmentName},
                    #{item.policyNo},
                    #{item.partyNo},
                    #{item.name},
                    #{item.insuredCode},
                    SYSDATE()
            )
        </foreach>

    </insert>

    <select id="getReportNoByPolicyNo" parameterType="java.lang.String" resultType="int">
        select count(DISTINCT pca.REPORT_NO)
        from CLMS_POLICY_CLAIM_CASE pca
        where pca.POLICY_NO IN
        <foreach collection="policyNoList" item="policyNo" index="index" open="(" separator="," close=")">
            #{policyNo}
        </foreach>

    </select>

    <select id="getInsuredCodeByReportNo" parameterType="java.lang.String" resultType="com.paic.ncbs.claim.model.dto.settle.PolicyClaimCaseDTO">
        select  POLICY_NO policyNo,
                INSURED_CODE insuredCode
        from    CLMS_POLICY_CLAIM_CASE
        where   report_no = #{reportNo,jdbcType=VARCHAR}

    </select>
    <select id="getInsuredPolicyNo" parameterType="java.lang.String" resultType="java.lang.String" >
        select policy_no from clms_policy_claim_case
        where  REPORT_NO =#{reportNo}
        and insured_code=#{insuredNo}
    </select>

</mapper>