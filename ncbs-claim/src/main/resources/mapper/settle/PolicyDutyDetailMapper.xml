<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"   "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.PolicyDutyDetailMapper">
	
<resultMap type="com.paic.ncbs.claim.model.dto.settle.PolicyDutyDetailDTO" id="policyDutyDetailDTO">
	<id column="ID_AHCS_POLICY_DUTY_DETAIL" property="idAhcsPolicyDutyDetail"/>
	<result column="ID_AHCS_POLICY_DUTY" property="idAhcsEstimateDuty"/>
	<result column="DUTY_DETAIL_NAME" property="dutyDetailName"/>
	<result column="DUTY_DETAIL_CODE" property="dutyDetailCode"/>
	<result column="DUTY_AMOUNT" property="dutyAmount"/>
	<result column="NOCLAIM_PROPERTY" property="noclaimProperty"/>
	<result column="NOCLAIM_AMOUNT" property="noclaimAmount"/>
	<result column="NOCLAIM_DAYS" property="noclaimDays"/>
	<result column="NOCLAIM_TIMES" property="noclaimTimes"/>
</resultMap>
	
	<!-- 根据责任主键查询责任明细 -->
	<select id="getByDutyID" parameterType="java.lang.String" resultMap="policyDutyDetailDTO">
          select
				pdd.ID_AHCS_POLICY_DUTY_DETAIL  ,
				pdd.ID_AHCS_POLICY_DUTY         ,
				pdd.DUTY_DETAIL_NAME            ,
				pdd.DUTY_DETAIL_CODE            ,
				pdd.DUTY_AMOUNT                 ,
				pdd.NOCLAIM_PROPERTY 		    ,
				pdd.NOCLAIM_AMOUNT              ,
				pdd.NOCLAIM_DAYS                ,
				pdd.NOCLAIM_TIMES               
		  from CLMS_Policy_Duty_Detail pdd
		  where pdd.ID_AHCS_POLICY_DUTY=#{idAhcsEstimateDuty}
    </select>
    
    <!-- 根据报案号查是否存在指定的事故者现状 查责任特点2 -->
    <select id="getDutyDetailByReportNo" parameterType="java.lang.String" resultType="java.util.HashMap" >
		SELECT DDC.TRAFFIC_ACCIDENT_TYPE DETAIL_ELEMENT2,
		       (SELECT CP.VALUE_CHINESE_NAME
		        FROM   CLM_COMMON_PARAMETER CP
		        WHERE  CP.COLLECTION_CODE = 'AHCS_DETAIL_ELEMENT2'
		        AND    CP.VALUE_CODE = DDC.TRAFFIC_ACCIDENT_TYPE) DETAIL_ELEMENT2_NAME
		FROM   CLMS_DUTY_DETAIL_CONFIG DDC,
		       CLMS_POLICY_INFO        PI,
		       CLMS_POLICY_PLAN        PP,
		       CLMS_POLICY_DUTY        PD,
		       CLMS_POLICY_DUTY_DETAIL PDD
		WHERE  DDC.INSURED_APPLY_STATUS = #{insuredApplyStatus}
		AND    DDC.TRAFFIC_ACCIDENT_TYPE IS NOT NULL
		AND    PI.ID_AHCS_POLICY_INFO = PP.ID_AHCS_POLICY_INFO
		AND    PP.ID_AHCS_POLICY_PLAN = PD.ID_AHCS_POLICY_PLAN
		AND    PD.ID_AHCS_POLICY_DUTY = PDD.ID_AHCS_POLICY_DUTY
		AND    DDC.PLAN_CODE = PP.PLAN_CODE
		AND    DDC.DUTY_DETAIL_CODE = PDD.DUTY_DETAIL_CODE
		AND    DDC.DUTY_CODE = PD.DUTY_CODE
		AND    PI.REPORT_NO = #{reportNo}
    </select>

	<!-- 获取是否包含指定责任特点一（暂时获取自身原因，非自身原因用） -->
	<select id="getElementCode1ByReportNoAndAccidentType" resultType="java.lang.Integer">
		SELECT COUNT(1)
		         FROM CLMS_POLICY_INFO PI
		        WHERE  PI.REPORT_NO = #{reportNo}
		          AND EXISTS
		        (SELECT 1
		                 FROM CLMS_DUTY_DETAIL_CONFIG DDC,
		                      CLMS_POLICY_PLAN        PP,
		                      CLMS_POLICY_DUTY        PD,
		                      CLMS_POLICY_DUTY_DETAIL PDD
		                WHERE DDC.PLAN_CODE = PP.PLAN_CODE
		                  AND PI.ID_AHCS_POLICY_INFO = PP.ID_AHCS_POLICY_INFO
		                  AND PP.ID_AHCS_POLICY_PLAN = PD.ID_AHCS_POLICY_PLAN
		                  AND PD.ID_AHCS_POLICY_DUTY = PDD.ID_AHCS_POLICY_DUTY
		                  AND DDC.DUTY_CODE = PD.DUTY_CODE
		                  AND DDC.DUTY_DETAIL_CODE = PDD.DUTY_DETAIL_CODE
					      AND DDC.INSURED_APPLY_STATUS = #{insuredApplyStatus}
					       <if test=" element2List != null and element2List.length > 0">
					         AND DDC.TRAFFIC_ACCIDENT_TYPE IN 
					       <foreach collection="element2List" item="element2" open="(" close=")" separator=",">
							   		#{element2}
							   </foreach>       
					       </if>
					       <if test=" element1List != null and element1List.length > 0">
						       AND DDC.DETAIL_ELEMENT1_CODE IN
							   <foreach collection="element1List" item="element1" open="(" close=")" separator=",">
							   		#{element1}
							   </foreach>       
					       </if>)
	          
	</select>

</mapper>