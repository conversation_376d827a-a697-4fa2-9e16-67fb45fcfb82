<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.AutoSettleDetailMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.settle.AutoSettleDetailDTO" id="autoSettleDetail">
        <result property="createdBy" column="CREATED_BY"/>
        <result property="createdDate" column="CREATED_DATE"/>
        <result property="updatedBy" column="UPDATED_BY"/>
        <result property="updatedDate" column="UPDATED_DATE"/>
        <result property="idAhcsAutoSettleDetail" column="ID_AHCS_AUTO_SETTLE_DETAIL"/>
        <result property="reportNo" column="REPORT_NO"/>
        <result property="caseTimes" column="CASE_TIMES"/>
        <result property="policyNo" column="POLICY_NO"/>
        <result property="caseNo" column="CASE_NO"/>
        <result property="planCode" column="PLAN_CODE"/>
        <result property="dutyCode" column="DUTY_CODE"/>
        <result property="dutyDetailCode" column="DUTY_DETAIL_CODE"/>
        <result property="dutyDetailName" column="DUTY_DETAIL_NAME"/>
        <result property="autoSettleAmount" column="AUTO_SETTLE_AMOUNT"/>
        <result property="autoSettleReason" column="AUTO_SETTLE_REASON"/>
    </resultMap>

    <select id="getAutoSettleDetails" resultMap="autoSettleDetail">
        select sd.created_by,
        sd.created_date,
        sd.updated_by,
        sd.updated_date,
        sd.id_ahcs_auto_settle_detail,
        sd.report_no,
        sd.case_times,
        sd.policy_no,
        sd.case_no,
        sd.plan_code,
        sd.duty_code,
        sd.duty_detail_code,
        sd.auto_settle_amount,
        sd.auto_settle_reason
        from CLMS_auto_settle_detail sd
        where report_no = #{reportNo,jdbcType=VARCHAR}
        and case_times = #{caseTimes,jdbcType=INTEGER}
    </select>

    <delete id="removeAutoSettleDetail" parameterType="com.paic.ncbs.claim.model.dto.duty.DutyDetailPayDTO">
        delete from CLMS_auto_settle_detail sd where
        sd.report_no=#{reportNo,jdbcType=VARCHAR} and
        sd.case_times=#{caseTimes,jdbcType=INTEGER} and
        sd.policy_no=#{policyNo,jdbcType=VARCHAR} and
        sd.plan_code=#{planCode,jdbcType=VARCHAR} and
        sd.duty_code=#{dutyCode,jdbcType=VARCHAR} and
        sd.duty_detail_code=#{dutyDetailCode,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteAutoSettleDetail">
        delete from CLMS_auto_settle_detail where
        report_no = #{reportNo,jdbcType=VARCHAR} and case_times = #{caseTimes,jdbcType=INTEGER}
    </delete>

    <select id="getAutoSettleResult" resultMap="autoSettleDetail">
        SELECT T.CASE_NO ,
        T.POLICY_NO,
        T.PLAN_CODE ,
        T.DUTY_CODE ,
        T.DUTY_DETAIL_CODE ,
        (select a.duty_detail_name
        from base_duty_detail_info a
        where a.duty_detail_code = t.duty_detail_code
        limit 1) duty_detail_name,
        T.AUTO_SETTLE_AMOUNT ,
        T.AUTO_SETTLE_REASON
        from CLMS_auto_settle_detail T
        WHERE
        T.case_no = #{caseNo,jdbcType=VARCHAR}
        and T.case_times = #{caseTimes,jdbcType=INTEGER}
        and T.policy_no = #{policyNo,jdbcType=VARCHAR}
        AND T.PLAN_CODE = #{planCode,jdbcType=VARCHAR}
        AND T.DUTY_CODE = #{dutyCode,jdbcType=VARCHAR}
    </select>

    <select id="selectByParam" resultType="com.paic.ncbs.claim.model.dto.settle.AutoSettleDetailDTO">
        select *
        from CLMS_AUTO_SETTLE_DETAIL CR
        where CR.report_no = #{reportNo}
        and CR.case_times = #{caseTimes}
        AND CR.CASE_NO = #{caseNo}
        and CR.policy_no = #{policyNo}
        and CR.PLAN_CODE = #{planCode}
        and CR.DUTY_CODE = #{dutyCode}
        and CR.DUTY_DETAIL_CODE = #{dutyDetailCode}
    </select>

    <insert id="batchInsert">
        INSERT INTO CLMS_auto_settle_detail
        (CREATED_BY, CREATED_DATE, UPDATED_BY, UPDATED_DATE, ID_AHCS_AUTO_SETTLE_DETAIL,
        REPORT_NO, CASE_TIMES,
        POLICY_NO,CASE_NO,PLAN_CODE,DUTY_CODE,DUTY_DETAIL_CODE,AUTO_SETTLE_AMOUNT,AUTO_SETTLE_REASON,ARCHIVE_TIME)
        VALUES
        <foreach collection="list" separator="," index="i" item="item">
            (item.CREATED_BY, item.CREATED_DATE, item.UPDATED_BY,item.UPDATED_DATE,
            item.ID_AHCS_AUTO_SETTLE_DETAIL,item.REPORT_NO,
            item.CASE_TIMES,item.POLICY_NO,
            item.CASE_NO,item.PLAN_CODE,item.DUTY_CODE,item.DUTY_DETAIL_CODE,
            item.AUTO_SETTLE_AMOUNT,item.AUTO_SETTLE_REASON,item.ARCHIVE_TIME)
        </foreach>

    </insert>

    <update id="updateById">
        update CLMS_auto_settle_detail
        set AUTO_SETTLE_AMOUNT = #{autoSettleAmount},
        AUTO_SETTLE_REASON = #{autoSettleReason}
        updated_by = #{updatedBy}
        updated_date = #{updatedDate}
        where id_ahcs_auto_settle_detail=#{idAhcsAutoSettleDetail}
    </update>

</mapper>