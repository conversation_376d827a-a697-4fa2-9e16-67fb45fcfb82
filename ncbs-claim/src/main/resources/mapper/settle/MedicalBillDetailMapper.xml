<?xml version =  "1.0" encoding =  "UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace =  "com.paic.ncbs.claim.dao.mapper.settle.MedicalBillDetailMapper">
   <!-- 批量增加操作 -->  
  <insert id =  "addBillDetailList" parameterType =  "java.util.List">
  	insert into 
  		clms_bill_detail
  		(CREATED_BY,
  		CREATED_DATE,
  		UPDATED_BY,
  		UPDATED_DATE,
	  	ID_AHCS_BILL_DETAIL,

  		ID_AHCS_BILL_INFO,
  		DETAIL_NO,
  		INPUT_MODE,
  		COST_CODE,
  		BILL_AMOUNT,

  		DEDUCTIBLE_AMOUNT,
  		IMMODERATE_AMOUNT,
  		PARTIAL_DEDUCTIBLE,
  		COST_COMMENT,
  		FOREIGN_BILL_AMOUNT,

  		FOREIGN_IMMODERATE_AMOUNT,
  		CURRENCY,
  		CUR_RATE,
  		archive_time
  		) values
  	<foreach collection =  "list" item =  "item" index =  "index" separator =  "," >
  		(	#{item.createdBy},
  			now(),
  			#{item.updatedBy},
		    now(),
			left(hex(uuid()),32),

  			#{item.idAhcsBillInfo},
	  		#{item.detailNo},
	  		#{item.inputMode},
	  		#{item.costCode},
	  		#{item.billAmount},

	  		#{item.deductibleAmount},
	  		#{item.immoderateAmount},
	  		#{item.partialDeductible},
		  	#{item.costComment},
		  	#{item.foreignBillAmount},

		  	#{item.foreignimmoderateAmount},
		  	#{item.currency},
		  	#{item.curRate},
		  	#{item.archiveTime} )
  	</foreach> 
  </insert>

  <update id =  "removeBillDetailList">
  	update
  		clms_bill_detail
  	set IS_EFFECTIVE =  'N',
  		 UPDATED_BY  =  #{userUM}, 
		UPDATED_DATE  =  SYSDATE()
  	where  IS_EFFECTIVE =  'Y'
  		 and ID_AHCS_BILL_INFO in
  		<foreach collection =  "idAhcsBillInfoList" item =  "item" index =  "index" open =  "(" separator =  "," close =  ")"> 
	  		#{item}
	  	</foreach>
  </update> 
  
  <update id = "clearBillDetailAmonut">
		  update 
		  	clms_bill_detail
		  set 
		      UPDATED_BY  =  #{userUM},
			  UPDATED_DATE  =  SYSDATE(),
			  deductible_amount=0 , 
			  immoderate_amount=0, 
			  partial_deductible=0,
			  cost_comment='',
			  foreign_immoderate_amount=0 
		 where  IS_EFFECTIVE =  'Y'
		 		 and ID_AHCS_BILL_INFO in
		 		<foreach collection =  "idAhcsBillInfoList" item =  "item" index =  "index" open =  "(" separator =  "," close =  ")"> 
		  			#{item}
		  		</foreach>
	</update>

	<insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.BillCopyDTO">
		INSERT INTO CLMS_BILL_DETAIL (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_BILL_DETAIL,
			ID_AHCS_BILL_INFO,
			INPUT_MODE,
			COST_CODE,
			BILL_AMOUNT,
			DEDUCTIBLE_AMOUNT,
			IMMODERATE_AMOUNT,
			PARTIAL_DEDUCTIBLE,
			COST_COMMENT,
			FOREIGN_BILL_AMOUNT,
			FOREIGN_IMMODERATE_AMOUNT,
			CURRENCY,
			CUR_RATE,
			IS_EFFECTIVE,
			DETAIL_NO,
			ARCHIVE_TIME,
			RISK_LABEL
		)
		<foreach collection="paramList" item="item" open="(" separator="union all" close=")">
			SELECT
				#{item.userId},
				NOW(),
				#{item.userId},
				NOW(),
				LEFT(HEX(UUID()), 32),
				#{item.reopenIdAhcsBillInfo},
				INPUT_MODE,
				COST_CODE,
				BILL_AMOUNT,
				DEDUCTIBLE_AMOUNT,
				IMMODERATE_AMOUNT,
				PARTIAL_DEDUCTIBLE,
				COST_COMMENT,
				FOREIGN_BILL_AMOUNT,
				FOREIGN_IMMODERATE_AMOUNT,
				CURRENCY,
				CUR_RATE,
				IS_EFFECTIVE,
				DETAIL_NO,
				NOW(),
				RISK_LABEL
			FROM CLMS_BILL_DETAIL
			WHERE ID_AHCS_BILL_INFO = #{item.idAhcsBillInfo} AND IS_EFFECTIVE = 'Y'
		</foreach>
	</insert>

  <insert id="addBillDetail">
	  insert into
	  clms_bill_detail
	  (CREATED_BY,
	  CREATED_DATE,
	  UPDATED_BY,
	  UPDATED_DATE,
	  ID_AHCS_BILL_DETAIL,
	  ID_AHCS_BILL_INFO,
	  DETAIL_NO,
	  INPUT_MODE,
	  COST_CODE,
	  BILL_AMOUNT,
	  DEDUCTIBLE_AMOUNT,
	  IMMODERATE_AMOUNT,
	  PARTIAL_DEDUCTIBLE,
	  archive_time,
	  COST_COMMENT,
	  RISK_LABEL
	  ) values(
	  #{createdBy},
	  NOW(),
	  #{updatedBy},
	  NOW(),
	  #{idAhcsBillDetail},
	  #{idAhcsBillInfo},
	  #{detailNo},
	  #{inputMode},
	  #{costCode},
	  #{billAmount},
	  #{deductibleAmount},
	  #{immoderateAmount},
	  #{partialDeductible},
	  #{archiveTime},
	  #{costComment},
	  #{riskLabel}
	  )
  </insert>
	<insert id="addBillDetailAmend">
		insert into
		clms_bill_detail_AMEND
		(CREATED_BY,
		CREATED_DATE,
		UPDATED_BY,
		UPDATED_DATE,
		ID_AHCS_BILL_DETAIL_AMEND,
		ID_AHCS_BILL_INFO_AMEND,
		DETAIL_NO,
		INPUT_MODE,
		COST_CODE,
		BILL_AMOUNT,
		DEDUCTIBLE_AMOUNT,
		IMMODERATE_AMOUNT,
		PARTIAL_DEDUCTIBLE,
		archive_time,
		COST_COMMENT,
		RISK_LABEL
		) values(
		#{createdBy},
		NOW(),
		#{updatedBy},
		NOW(),
		#{idAhcsBillDetail},
		#{idAhcsBillInfo},
		#{detailNo},
		#{inputMode},
		#{costCode},
		#{billAmount},
		#{deductibleAmount},
		#{immoderateAmount},
		#{partialDeductible},
		#{archiveTime},
		#{costComment},
		#{riskLabel}
		)
	</insert>
</mapper>