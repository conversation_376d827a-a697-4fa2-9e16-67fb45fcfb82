<?xml version =  "1.0" encoding =  "UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace =  "com.paic.ncbs.claim.dao.mapper.settle.MedicalBillReduceDetailMapper">

<resultMap type = "com.paic.ncbs.claim.model.dto.settle.MedicalBillReduceDetailDTO" id = "medicalBillReduceMap">
		<id column = "ID_AHCS_BILL_REDUCE_DETAIL" property = "idAhcsBillReduceDetail"/>
	  	<result column = "ID_AHCS_BILL_INFO" property = "idAhcsBillInfo"/>
	  	<result column = "COST_CODE" property = "costCode"/>  	
	  	<result column = "FEE_CODE" property = "feeCode"/>
	  	<result column = "FEE_NAME" property = "feeName"/>
	    <result column = "FEE_AMOUNT" property = "feeAmount"/>
	    <result column = "INSURANCE_TYPE_CODE" property = "insuranceTypeCode"/>
	  	<result column = "BILL_AMOUNT" property = "billAmount"/>  	
	  	<result column = "DEDUCTIBLE" property = "deductible"/>  	
	  	<result column = "REMARK" property = "remark"/>
	  	<result column = "DEDUCTION_RATE" property = "deductionRate"/>
	    <result column = "UNIT_PRICE" property = "unitPrice"/>
	    <result column = "AMOUNT" property = "amount"/>
	    <result column = "UNITS" property = "units"/>
	  	<result column = "IS_EFFECTIVE" property = "isEffective"/>
	    <result column = "SOCIAL_SECURITY_TYPE" property = "socialSecurityType"/>
	   <result column = "BILL_NO" property = "billNo"/>
	</resultMap>
	
   <!-- 批量增加操作 -->  
  	<insert id =  "addBillReduceDetailList" parameterType =  "java.util.List">
  	insert into 
  		CLMS_BILL_REDUCE_DETAIL
  		(CREATED_BY,
  		CREATED_DATE,
  		UPDATED_BY,
  		UPDATED_DATE,
  		ID_AHCS_BILL_INFO,
  		FEE_CODE,
  		COST_CODE,
  		FEE_NAME,
		FEE_AMOUNT,
		INSURANCE_TYPE_CODE,
  		DEDUCTION_RATE,
  		BILL_AMOUNT,
  		DEDUCTIBLE,
		UNIT_PRICE,
		AMOUNT,
		UNITS,
  		REMARK,
  		IS_EFFECTIVE,
		ARCHIVE_TIME,
	  	ID_AHCS_BILL_REDUCE_DETAIL,
		SOCIAL_SECURITY_TYPE,
		BILL_NO
  		)
  	<foreach collection =  "list" item =  "item" index =  "index" separator =  "union all"> 
  		select 
  			#{item.createdBy},
  			SYSDATE(),
  			#{item.updatedBy},
  			SYSDATE(),
  			#{item.idAhcsBillInfo},
	  		#{item.feeCode},
	  		#{item.costCode},
	  		#{item.feeName},
		    #{item.feeAmount},
	  		#{item.insuranceTypeCode},
	  		#{item.deductionRate},
	  		#{item.billAmount},
	  		#{item.deductible},
		    #{item.unitPrice},
		    #{item.amount},
		    #{item.units},
	  		#{item.remark},
  		       'Y',
	  		#{item.archiveTime},
			left(hex(uuid()),32),
		    #{item.socialSecurityType},
		    #{item.billNo}
  	</foreach>
  </insert>

	<insert id =  "addBillReduceDetail" parameterType =  "com.paic.ncbs.claim.model.dto.settle.MedicalBillReduceDetailDTO">
  	insert into
		CLMS_BILL_REDUCE_DETAIL
  		(CREATED_BY,
  		CREATED_DATE,
  		UPDATED_BY,
  		UPDATED_DATE,
		ID_AHCS_BILL_REDUCE_DETAIL,
  		ID_AHCS_BILL_INFO,
  		FEE_CODE,
  		COST_CODE,
  		FEE_NAME,
		FEE_AMOUNT,
		INSURANCE_TYPE_CODE,
  		DEDUCTION_RATE,
  		BILL_AMOUNT,
  		DEDUCTIBLE,
		UNIT_PRICE,
		AMOUNT,
		UNITS,
  		REMARK,
		ARCHIVE_TIME,
		SELF_SUFFICIENCY_RATE,
		SOCIAL_SECURITY_TYPE,
		UNITS_CODE,
		BILL_NO
  		)values(
  			#{createdBy},
  			SYSDATE(),
  			#{updatedBy},
  			SYSDATE(),
			left(hex(uuid()),32),
  			#{idAhcsBillInfo},
	  		#{feeCode},
	  		#{costCode},
	  		#{feeName},
			#{feeAmount},
	  		#{insuranceTypeCode},
	  		#{deductionRate},
	  		#{billAmount},
	  		#{deductible},
			#{unitPrice},
			#{amount},
			#{units},
	  		#{remark},
	  		#{archiveTime},
	  		#{selfSufficiencyRate},
	  		#{socialSecurityType},
	  		#{unitsCode},
		    #{billNo}
	  	 )
  </insert>
	<insert id =  "addBillReduceDetailAmend" parameterType =  "com.paic.ncbs.claim.model.dto.settle.MedicalBillReduceDetailDTO">
		insert into
		CLMS_BILL_REDUCE_DETAIL_AMEND
		(CREATED_BY,
		CREATED_DATE,
		UPDATED_BY,
		UPDATED_DATE,
		ID_AHCS_BILL_REDUCE_DETAIL_AMEND,
		ID_AHCS_BILL_INFO_AMEND,
		ID_AHCS_BILL_DETAIL_AMEND,
		FEE_CODE,
		COST_CODE,
		FEE_NAME,
		FEE_AMOUNT,
		INSURANCE_TYPE_CODE,
		DEDUCTION_RATE,
		BILL_AMOUNT,
		DEDUCTIBLE,
		UNIT_PRICE,
		AMOUNT,
		UNITS,
		REMARK,
		ARCHIVE_TIME,
		SELF_SUFFICIENCY_RATE,
		SOCIAL_SECURITY_TYPE,
		UNITS_CODE
		)values(
		#{createdBy},
		SYSDATE(),
		#{updatedBy},
		SYSDATE(),
		left(hex(uuid()),32),
		#{idAhcsBillInfo},
		#{idAhcsBillDetail},
		#{feeCode},
		#{costCode},
		#{feeName},
		#{feeAmount},
		#{insuranceTypeCode},
		#{deductionRate},
		#{billAmount},
		#{deductible},
		#{unitPrice},
		#{amount},
		#{units},
		#{remark},
		#{archiveTime},
		#{selfSufficiencyRate},
		#{socialSecurityType},
		#{unitsCode}
		)
	</insert>

	<update id =  "modifyBillReduceDetail" parameterType =  "com.paic.ncbs.claim.model.dto.settle.MedicalBillReduceDetailDTO">
	  	update
			CLMS_BILL_REDUCE_DETAIL
	  	set
	  			FEE_CODE = #{feeCode},
		  		COST_CODE = #{costCode},
		  		FEE_NAME = #{feeName},
				FEE_AMOUNT = #{feeAmount},
		  		insurance_type_code = #{insuranceTypeCode},
		  		DEDUCTION_RATE = #{deductionRate},
		  		BILL_AMOUNT = #{billAmount},
		  		DEDUCTIBLE = #{deductible},
				UNIT_PRICE = #{unitPrice},
				AMOUNT = #{amount},
				UNITS = #{units},
		  		REMARK =#{remark},
	  			IS_EFFECTIVE = #{isEffective},
			    SOCIAL_SECURITY_TYPE = #{socialSecurityType},
	  	        UPDATED_DATE = SYSDATE()
	  	where  ID_AHCS_BILL_REDUCE_DETAIL = #{idAhcsBillReduceDetail}
  </update> 
  
  	<select id = "getBillReduceDetailList" resultMap = "medicalBillReduceMap">
	  	SELECT CREATED_BY,
		  		CREATED_DATE,
		  		UPDATED_BY,
		  		UPDATED_DATE,
		  		ID_AHCS_BILL_REDUCE_DETAIL,
		  		ID_AHCS_BILL_INFO,
		  		FEE_CODE,
		  		COST_CODE,
		  		FEE_NAME,
			   FEE_AMOUNT,
			   INSURANCE_TYPE_CODE,
			   DEDUCTION_RATE,
			   BILL_AMOUNT,
			   DEDUCTIBLE,
			   UNIT_PRICE,
			   AMOUNT,
			   UNITS,
		  		REMARK ,
		  		IS_EFFECTIVE,
		SELF_SUFFICIENCY_RATE,
		SOCIAL_SECURITY_TYPE,
		UNITS_CODE
		  from 
	      		clms_bill_REDUCE_DETAIL
	     where    ID_AHCS_BILL_INFO = #{idAhcsBillInfo}  and IS_EFFECTIVE = 'Y'
  </select>

  	<update id =  "removeBillReduceDetailList">
  	update
  		clms_bill_REDUCE_DETAIL
  	set IS_EFFECTIVE =  'N',
  		 UPDATED_BY  =  #{userUM}, 
		 UPDATED_DATE  =  SYSDATE()
  	where  ID_AHCS_BILL_INFO in
  		<foreach collection =  "idAhcsBillInfoList" item =  "item" index =  "index" open =  "(" separator =  "," close =  ")"> 
	  		#{item}
	  	</foreach>
  </update>

	<insert id="copyForCaseReopen" parameterType="com.paic.ncbs.claim.model.dto.restartcase.BillCopyDTO">
		INSERT INTO CLMS_BILL_REDUCE_DETAIL (
			CREATED_BY,
			CREATED_DATE,
			UPDATED_BY,
			UPDATED_DATE,
			ID_AHCS_BILL_REDUCE_DETAIL,
			ID_AHCS_BILL_INFO,
			COST_CODE,
			FEE_CODE,
			FEE_NAME,
			FEE_AMOUNT,
			INSURANCE_TYPE_CODE,
			BILL_AMOUNT,
			DEDUCTION_RATE,
			DEDUCTIBLE,
			UNIT_PRICE,
			AMOUNT,
			UNITS,
			REMARK,
			IS_EFFECTIVE,
			ARCHIVE_TIME,
		SELF_SUFFICIENCY_RATE,
		SOCIAL_SECURITY_TYPE,
		UNITS_CODE
		)
		<foreach collection="paramList" item="item" open="(" separator="union all" close=")">
			SELECT
				#{item.userId},
				NOW(),
				#{item.userId},
				NOW(),
				LEFT(HEX(UUID()), 32),
				#{item.reopenIdAhcsBillInfo},
				COST_CODE,
				FEE_CODE,
				FEE_NAME,
				FEE_AMOUNT,
				INSURANCE_TYPE_CODE,
				BILL_AMOUNT,
				DEDUCTION_RATE,
				DEDUCTIBLE,
				UNIT_PRICE,
				AMOUNT,
				UNITS,
				REMARK,
				IS_EFFECTIVE,
				NOW(),
			SELF_SUFFICIENCY_RATE,
			SOCIAL_SECURITY_TYPE,
			UNITS_CODE
			FROM CLMS_BILL_REDUCE_DETAIL
			WHERE ID_AHCS_BILL_INFO = #{item.idAhcsBillInfo} AND IS_EFFECTIVE = 'Y'
		</foreach>
	</insert>
</mapper>