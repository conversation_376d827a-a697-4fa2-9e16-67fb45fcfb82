<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.settle.AutoSettleCheckLossValueMapper">

    <!-- 获取核责-航班延误/取消/备降、迫降信息-的赔付信息 -->
    <select id="getCheckLossPayPattern" resultType="String">
        select PAY_PATTERN
        from CLMS_flight_delay
        WHERE REPORT_NO = #{reportNo, jdbcType = VARCHAR}
        AND CASE_TIMES = #{caseTimes, jdbcType = INTEGER}
        and task_code='checkDuty'
    </select>

    <!-- 获取被保险人年龄 -->
    <select id="getCustomerAge" resultType="java.lang.String">
        SELECT T.AGE FROM CLMS_REPORT_CUSTOMER T WHERE T.REPORT_NO = #{reportNo} limit 1
    </select>

    <!-- 获取死亡证明类型 -->
    <select id="getPersonDeathPaperType" resultType="java.lang.String">
        SELECT T.DEATH_PAPER_TYPE
        FROM CLMS_PERSON_DEATH T
        WHERE T.REPORT_NO = #{reportNo}
        AND T.CASE_TIMES = #{caseTimes}
        AND T.IS_EFFECTIVE = 'Y'
        limit 1
        <if test="taskId != null and taskId != '' ">
            AND t.task_id = #{taskId,jdbcType=VARCHAR}
        </if>
    </select>

    <!-- 获取三方赔付金额 -->
    <select id="getPersonDeathThirdPayAmount" resultType="java.lang.String">
        select t.amount from CLMS_PERSON_DEATH t where t.task_id='checkDuty' and t.status='1' and t.is_third_pay='Y' and
        t.report_no=#{reportNo} and t.case_times=#{caseTimes} AND t.IS_EFFECTIVE = 'Y'
    </select>

    <!-- 获取三方赔付金额,环节为入参 -->
    <select id="getPersonDeathThirdPayAmountByTaskId" resultType="java.lang.String">
        select t.amount from CLMS_PERSON_DEATH t where t.status='1' and t.is_third_pay='Y' and t.report_no=#{reportNo}
        and t.case_times=#{caseTimes} AND t.IS_EFFECTIVE = 'Y'
        <if test="taskId != null and taskId != '' ">
            AND t.task_id = #{taskId,jdbcType=VARCHAR}
        </if>
    </select>

    <!-- 获取整案赔付次数 -->
    <select id="getCaseTimesWhole" resultType="int">
        SELECT count(1)
        FROM clm_policy_pay A
        where Exists (
        select 1
        from CLM_CASE_BASE C
        where A.CASE_TIMES = C.CASE_TIMES
        AND A.CASE_NO = C.CASE_NO
        and C.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
        )
        and Exists (select 1
        from CLMS_POLICY_CLAIM_CASE B
        where A.CASE_NO = B.CASE_NO
        AND B.SUBPOLICY_NO = (select subpolicy_no from CLMS_POLICY_CLAIM_CASE where
        report_no=#{reportNo, jdbcType = VARCHAR} and policy_no=#{policyNo,jdbcType=VARCHAR} limit 1)
        AND B.INSURED_CODE = (select insured_code from CLMS_POLICY_CLAIM_CASE where
        report_no=#{reportNo, jdbcType = VARCHAR} and policy_no=#{policyNo,jdbcType=VARCHAR} limit 1)
        and B.POLICY_NO = #{policyNo,jdbcType=VARCHAR})
        and A.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
        AND a.policy_pay > = 0
    </select>

    <!-- 获取责任赔付次数 -->
    <select id="getCaseTimesDuty" resultType="int">
        SELECT count(1)
        FROM CLM_PLAN_DUTY_PAY A
        where Exists (
        select 1
        from CLM_CASE_BASE C
        where A.CASE_TIMES = C.CASE_TIMES
        AND A.CASE_NO = C.CASE_NO
        and C.POLICY_NO = #{policyNo,jdbcType=VARCHAR}
        )
        and Exists (select 1
        from CLMS_POLICY_CLAIM_CASE B
        where A.CASE_NO = B.CASE_NO
        AND B.SUBPOLICY_NO = (select subpolicy_no from CLMS_POLICY_CLAIM_CASE where
        report_no=#{reportNo, jdbcType = VARCHAR} and policy_no=#{policyNo,jdbcType=VARCHAR} limit 1)
        AND B.INSURED_CODE = (select insured_code from CLMS_POLICY_CLAIM_CASE where
        report_no=#{reportNo, jdbcType = VARCHAR} and policy_no=#{policyNo,jdbcType=VARCHAR} limit 1)
        and B.POLICY_NO = #{policyNo,jdbcType=VARCHAR})
        and a.DUTY_PAY_AMOUNT > 0
        and a.duty_code = #{dutyCode, jdbcType=VARCHAR}
        and a.plan_code = #{planCode, jdbcType=VARCHAR}
    </select>

</mapper>