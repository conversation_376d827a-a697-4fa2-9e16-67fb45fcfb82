<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.mistake.MistakeDefineMapper">
    <resultMap type="com.paic.ncbs.claim.model.dto.mistake.MistakeDefineDTO" id="mistakeDefineDTO">
    	<id     property="idAhcsMistakeDefine" column="ID_AHCS_MISTAKE_DEFINE"/>
        <result property="mistakeType" column="MISTAKE_TYPE"/>
        <result property="isEffective" column="IS_EFFECTIVE"/>
        <result property="mistakeCode" column="MISTAKE_CODE"/>
        <result property="mistakeContent" column="MISTAKE_CONTENT"/>
        <result property="isChecked" column="IS_CHECKED"/>
    </resultMap>


    <select id="getMistakeDefineList" resultMap="mistakeDefineDTO" >

        SELECT md. ID_AHCS_MISTAKE_DEFINE,
        md. MISTAKE_TYPE,
        md. MISTAKE_CODE,
        md. IS_EFFECTIVE,
        md. MISTAKE_CONTENT
        FROM CLMS_MISTAKE_DEFINE md
        WHERE md.IS_EFFECTIVE = 'Y'
        <if test=" mistakeType != null and mistakeType != '' ">
            and md.MISTAKE_TYPE = #{mistakeType}
        </if>
        ORDER BY md. MISTAKE_CODE
    </select>


    <select id="getDefinesByCodeList" resultMap="mistakeDefineDTO" >
       SELECT
       		ID_AHCS_MISTAKE_DEFINE ,
       		MISTAKE_TYPE ,
       		MISTAKE_CODE ,
       		IS_EFFECTIVE ,
       		MISTAKE_CONTENT
       FROM CLMS_MISTAKE_DEFINE
       WHERE MISTAKE_CODE IN 
        <foreach collection="mistakeCodeList" index="index" item="mistakeCode" open="(" separator="," close=")">#{mistakeCode}</foreach>
        and IS_EFFECTIVE = 'Y'
    </select>

    <select id="getMistakeName" resultType="string" >
       SELECT
       		MISTAKE_CONTENT
       FROM CLMS_MISTAKE_DEFINE
       WHERE MISTAKE_CODE = #{mistakeCode}
    </select>

    <select id="getMistakeReasons" resultType="java.lang.String">
        select GROUP_CONCAT(MISTAKE_CODE)  from CLMS_MISTAKE_DEFINE d where d.MISTAKE_CODE in
        <foreach collection="mistakeCodeList" index="index" item="mistakeCode" open="(" separator="," close=")">#{mistakeCode}</foreach>
        and IS_EFFECTIVE = 'Y'
    </select>

</mapper>