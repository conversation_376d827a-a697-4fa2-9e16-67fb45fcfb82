<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.paic.ncbs.claim.dao.mapper.mistake.MistakeRecordMapper">

    <resultMap type="com.paic.ncbs.claim.model.dto.mistake.MistakeRecordDTO" id="mistakeRecord">
        <id property="idAhcsMistakeRecord" column="ID_AHCS_MISTAKE_RECORD" />
        <result property="createdBy" column="CREATED_BY" />
        <result property="createdDate" column="CREATED_DATE" />
        <result property="updatedBy" column="UPDATED_BY" />
        <result property="updatedDate" column="UPDATED_DATE" />
        <result property="recordTaskId" column="RECORD_TASK_ID" />
        <result property="mistakeRemark" column="MISTAKE_REMARK" />
        <result property="submitDate" column="SUBMIT_DATE" />
        <result property="submitterUm" column="SUBMITTER_UM" />
        <result property="userInCharge" column="USER_IN_CHARGE" />
        <result property="deptCode" column="DEPT_CODE" />
        <result property="reportNo" column="REPORT_NO" />
        <result property="caseTimes" column="CASE_TIMES" />
        <result property="status" column="STATUS" />
        <result property="mistakeCode" column="MISTAKE_CODE" />
    </resultMap>



    <insert id="addMistakeRecords" parameterType="com.paic.ncbs.claim.model.dto.mistake.MistakeRecordDTO">
            INSERT INTO CLMS_MISTAKE_RECORD
            (CREATED_BY,
            CREATED_DATE,
            UPDATED_BY,
            UPDATED_DATE,
            ID_AHCS_MISTAKE_RECORD,
            REPORT_NO,
            CASE_TIMES,
            RECORD_TASK_ID,
            MISTAKE_REMARK,
            SUBMIT_DATE,
            SUBMITTER_UM,
            USER_IN_CHARGE,
            DEPT_CODE,
            STATUS,
            MISTAKE_CODE,
            ARCHIVE_TIME
            )VALUES(
            #{mistakeRecord.createdBy,jdbcType=VARCHAR},
            sysdate(),
            #{mistakeRecord.updatedBy,jdbcType=VARCHAR},
            sysdate(),
            #{mistakeRecord.idAhcsMistakeRecord,jdbcType=VARCHAR},
            #{mistakeRecord.reportNo,jdbcType=VARCHAR},
            #{mistakeRecord.caseTimes,jdbcType=NUMERIC},
            #{mistakeRecord.recordTaskId,jdbcType=VARCHAR},
            #{mistakeRecord.mistakeRemark,jdbcType=VARCHAR},
            sysdate(),
            upper(#{mistakeRecord.submitterUm,jdbcType=VARCHAR} ),
            #{mistakeRecord.userInCharge,jdbcType=VARCHAR} ,
            #{mistakeRecord.deptCode,jdbcType=VARCHAR} ,
            #{mistakeRecord.status,jdbcType=VARCHAR},
            #{mistakeRecord.mistakeCode,jdbcType=VARCHAR},
            sysdate()
        )
    </insert>

    <select id="getSimpleMistakeRecord" resultMap="mistakeRecord">
        SELECT CREATED_BY,
               CREATED_DATE,
               UPDATED_BY,
               UPDATED_DATE,
               ID_AHCS_MISTAKE_RECORD,
               REPORT_NO,
               CASE_TIMES,
               RECORD_TASK_ID,
               MISTAKE_REMARK,
               SUBMIT_DATE,
               SUBMITTER_UM,
               STATUS,
               MISTAKE_CODE,
               USER_IN_CHARGE,
               DEPT_CODE
        FROM CLMS_MISTAKE_RECORD
        WHERE REPORT_NO = #{reportNo}
          AND CASE_TIMES = #{caseTimes}
          AND RECORD_TASK_ID = #{recordTaskId}
    </select>

    <delete id="deleteByReportNoAndBpmKey">
        DELETE
        FROM CLMS_MISTAKE_RECORD
        WHERE REPORT_NO = #{reportNo}
          AND CASE_TIMES = #{caseTimes}
          AND RECORD_TASK_ID = #{recordTaskId}
    </delete>


</mapper>