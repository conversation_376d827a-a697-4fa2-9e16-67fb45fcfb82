<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.reinsurance.ReinsBillMapper">

    <resultMap type="com.paic.ncbs.claim.dao.entity.reinsurance.ReinsBillDTO" id="BaseResultMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="documentGroupItemsId" column="document_group_items_id" jdbcType="VARCHAR"/>
        <result property="reportNo" column="report_no" jdbcType="VARCHAR"/>
        <result property="caseTimes" column="case_times" jdbcType="INTEGER"/>
        <result property="caseNo" column="case_no" jdbcType="VARCHAR"/>
        <result property="policyNo" column="policy_no" jdbcType="VARCHAR"/>
        <result property="billNo" column="bill_no" jdbcType="INTEGER"/>
        <result property="billType" column="bill_type" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="claimType" column="claim_type" jdbcType="VARCHAR"/>
        <result property="submitCode" column="submit_code" jdbcType="VARCHAR"/>
        <result property="reinsCode" column="reins_code" jdbcType="VARCHAR"/>
        <result property="reinsName" column="reins_name" jdbcType="VARCHAR"/>
        <result property="currency" column="currency" jdbcType="VARCHAR"/>
        <result property="sumLoss" column="sum_loss" jdbcType="DECIMAL"/>
        <result property="paidLoss" column="paid_loss" jdbcType="DECIMAL"/>
        <result property="reinsEName" column="reins_Ename" jdbcType="VARCHAR"/>
        <result property="shareRate" column="share_rate" jdbcType="DECIMAL"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="sysCtime" column="sys_ctime"  jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="sysUtime" column="sys_utime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,document_group_items_id, report_no,case_no,policy_no, case_times, bill_no, bill_type,create_date,claim_type,submit_code, reins_code, reins_name,reins_Ename,
        currency,sum_loss,paid_loss,share_rate, send_time, status, created_by, sys_ctime, updated_by, sys_utime
    </sql>
    <select id="getBillTypeByReportNo" resultType="java.lang.String">
        select
            bill_type
        from
            clms_reins_bill crb
        where
            crb.report_no = #{reportNo} and crb.status = '1'
            group by crb.bill_type
    </select>
    <select id="getBillNoByReportNo" resultType="java.lang.Integer">
        select
            bill_no
        from
            clms_reins_bill crb
        where
            crb.report_no = #{reportNo} and crb.bill_type = #{billType} and crb.status = '1'
        group by crb.bill_no
    </select>

    <select id="getReinsByReportNo" resultMap="BaseResultMap" parameterType="com.paic.ncbs.claim.dao.entity.reinsurance.ReinsBillDTO">
        select
            crb.reins_code,crb.reins_name
        from
            clms_reins_bill crb
        where
            crb.report_no = #{reportNo} and crb.bill_type = #{billType}
            and crb.bill_no =#{billNo} and crb.status = '1'

            group by crb.reins_code,crb.reins_name
    </select>

    <select id="getBillByEntity" resultMap="BaseResultMap" parameterType="com.paic.ncbs.claim.dao.entity.reinsurance.ReinsBillDTO">
        select
        <include refid="Base_Column_List"/>
        from clms_reins_bill
        where report_no = #{reportNo}
        <if test="billType !=null and billType != ''">
            and bill_type = #{billType}
        </if>
        <if test="billNo != null">
            and bill_no =#{billNo}
        </if>
        <if test="reinsCode!=null and reinsCode != ''">
            and reins_code = #{reinsCode}
        </if>
        <if test="status!=null and status != ''">
            and status = #{status}
        </if>
    </select>
    <update id="updateBillStatusById">
        update clms_reins_bill
        set status = #{status},
        sys_utime = sysdate()
        where id = #{id}
    </update>
    <select id="getFailBill" resultType="com.paic.ncbs.claim.dao.entity.reinsurance.ReinsBillDTO">
        select
        <include refid="Base_Column_List"/>
        from clms_reins_bill
        where report_no = #{reportNo}
        <if test="caseTimes!=null">
            and case_times = #{caseTimes}
        </if>
        <if test="claimType !=null and claimType != ''">
            and claim_type = #{claimType}
        </if>
        and status != '1'
    </select>
</mapper>