package com.paic.ncbs.claim.controller.duty;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.claim.model.dto.settle.MedicalBillInfoDTO;
import com.paic.ncbs.claim.common.constant.ErrorCode;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.settle.SyncBillParamDTO;
import com.paic.ncbs.claim.model.vo.settle.CompareBillNoResultVO;
import com.paic.ncbs.claim.model.vo.settle.IdAhcsBillInfoListVO;
import com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoPageVO;
import com.paic.ncbs.claim.model.vo.settle.MedicalBillInfoVO;
import com.paic.ncbs.claim.service.checkloss.PersonHospitalService;
import com.paic.ncbs.claim.service.settle.MedicalBillService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

@Api(tags = "账单发票")
@RestController
@RequestMapping("/duty/app/medicalBillAction")
public class MedicalBillController extends BaseController {

    @Autowired
    MedicalBillService medicalBillService;

    @Autowired
    PersonHospitalService personHospitalService;

    @InitBinder
    protected void initBinder(WebDataBinder binder) {
        binder.setAutoGrowCollectionLimit(2048);
    }


    @ApiOperation("定损信息-查询发票信息列表")
    @PostMapping(value = "/getBillInfoByPage")
    public ResponseResult<MedicalBillInfoPageVO> getBillInfoByPage(@RequestBody MedicalBillInfoDTO medicalBillInfoDTO) throws GlobalBusinessException {
        LogUtil.audit("#查询发票信息列表#,传入参数reportNo={},caseTimes={}",medicalBillInfoDTO.getReportNo(),medicalBillInfoDTO.getCaseTimes());

        medicalBillInfoDTO.setCaseTimes(medicalBillInfoDTO.getCaseTimes()==null ? 1 : medicalBillInfoDTO.getCaseTimes());

        MedicalBillInfoPageVO vo =  medicalBillService.getBillInfoByPage(medicalBillInfoDTO);
        return ResponseResult.success(vo);

    }

    @ApiOperation("定损信息-查询发票详细信息")
    @GetMapping(value = "/getMedicalBillDetail/{idAhcsBillInfo}")
    public ResponseResult<MedicalBillInfoVO> getMedicalBillDetail(@ApiParam("账单信息表主键") @PathVariable("idAhcsBillInfo") String idAhcsBillInfo) {
        LogUtil.audit("#查询发票详细信息#,传入参数idAhcsBillInfo={" + idAhcsBillInfo + "}");
        return ResponseResult.success(medicalBillService.getMedicalBill(idAhcsBillInfo));
    }


    @ApiOperation("定损信息录入-新增账单")
    @PostMapping(value = "/addMedicalBill")
    public ResponseResult<Object> addMedicalBill(@RequestBody MedicalBillInfoVO medicalBillInfoVO) throws Exception {
        LogUtil.audit("定损信息录入-新增账单addMedicalBill报案号{}，请求入参{}",medicalBillInfoVO.getReportNo(),JSON.toJSONString(medicalBillInfoVO));
        CompareBillNoResultVO compareBillNoResultVO = medicalBillService.validAndAddMedicalBill(medicalBillInfoVO, WebServletContext.getUserId());
        return ResponseResult.success(compareBillNoResultVO);
    }

    @ApiOperation("修改账单")
    @PostMapping(value = "/modifyMedicalBill")
    public ResponseResult<CompareBillNoResultVO> modifyMedicalBill(@RequestBody MedicalBillInfoVO medicalBillInfoVO) throws Exception {

        CompareBillNoResultVO compareBillNoResultVO = medicalBillService.modifyMedicalBill(medicalBillInfoVO, WebServletContext.getUserId());
        return ResponseResult.success(compareBillNoResultVO);
    }


    @ApiOperation("批量删除发票信息")
    @PostMapping(value = "/removeMedicalBillList")
    public ResponseResult removeMedicalBillList(@RequestBody IdAhcsBillInfoListVO idAhcsBillInfoListVO) throws Exception {
        LogUtil.audit("#批量删除发票信息#,传入参数idAhcsBillInfoList={"
                + JSON.toJSONString(idAhcsBillInfoListVO.getIdAhcsBillInfoList()) + "}");
        medicalBillService.removeMedicalBill(idAhcsBillInfoListVO, WebServletContext.getUserId());
        return ResponseResult.success();
    }

    @ApiOperation("判断发票号是否在其他案件下保存过")
    @PostMapping(value = "/getSameBillNoReportNo")
    public ResponseResult<CompareBillNoResultVO> getSameBillNoReportNo(@RequestBody MedicalBillInfoVO medicalBillInfoVO) throws GlobalBusinessException {
        LogUtil.audit("#判断发票号是否在其他案件下保存过！！billNo=%s,idAhcsBillInfo=%s,reportNo=%s", medicalBillInfoVO.getBillNo(), medicalBillInfoVO.getIdAhcsBillInfo(), medicalBillInfoVO.getReportNo());
        CompareBillNoResultVO resultVO =  medicalBillService.getSameBillNoReportNo(medicalBillInfoVO);
        return ResponseResult.success(resultVO);
    }

    @ApiOperation("获取默认保险总赔付额")
    @PostMapping(value = "/getDefaultInsuranceTotalPayment")
    public ResponseResult<Object> getDefaultInsuranceTotalPayment(@RequestBody MedicalBillInfoVO medicalBillInfoVO) throws GlobalBusinessException {
        this.validFileParams(medicalBillInfoVO);
        BigDecimal defaultInsuranceTotalPayment = medicalBillService.getDefaultInsuranceTotalPayment(medicalBillInfoVO);
        return ResponseResult.success(defaultInsuranceTotalPayment);
    }

    @ApiOperation("获取所有订单所需信息")
    @GetMapping(value = "/checkAllBillRequired/{reportNo}/{caseTimes}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号" ,dataType = "String",dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int" ,dataTypeClass=Integer.class)
    })    public ResponseResult<Object> checkAllBillRequired(@PathVariable("reportNo") String reportNo,
                                                     @PathVariable("caseTimes") Integer caseTimes) {
        String str = medicalBillService.checkAllBillRequired(reportNo, caseTimes);
        return ResponseResult.success(str);
    }


    private void validFileParams(MedicalBillInfoVO medicalBillInfoVO) throws GlobalBusinessException {
        if (StringUtils.isEmptyStr(medicalBillInfoVO.getReportNo())) {
            LogUtil.audit("参数reportNo(报案号)不能为空!");
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "报案号");
        } else if (medicalBillInfoVO.getStartDate() == null && medicalBillInfoVO.getEndDate() == null) {
            LogUtil.audit("参数startDate,endDate(就诊时间)不能为空!");
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "就诊时间");
        } else if (medicalBillInfoVO.getTherapyType() == null) {
            LogUtil.audit("参数therapyType(治疗类型)不能为空!");
            throw new GlobalBusinessException(ErrorCode.Core.CAN_NOT_NULL, "治疗类型");
        }
    }

    @ApiOperation("诊断信息查询")
    @GetMapping(value = "/getPersonDiagnoseList/{reportNo}/{caseTimes}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号" ,dataType = "String",dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int" ,dataTypeClass=Integer.class)
    })    public ResponseResult<Object> getPersonDiagnoseList(@PathVariable("reportNo") String reportNo,
                                                             @PathVariable("caseTimes") Integer caseTimes) {
        LogUtil.audit("#诊断信息查询#入参#reprotNo ={},caseTimes={}",reportNo,caseTimes);

        return ResponseResult.success(medicalBillService.getPersonDiagnoseList(reportNo,caseTimes));
    }

    @ApiOperation("查询医院信息")
    @GetMapping(value = "/getPersonHospital/{reportNo}/{caseTimes}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportNo", value = "报案号" ,dataType = "String",dataTypeClass=String.class),
            @ApiImplicitParam(name = "caseTimes", value = "赔付次数", dataType = "Int" ,dataTypeClass=Integer.class)
    })    public ResponseResult<Object> getPersonHospital(@PathVariable("reportNo") String reportNo,
                                                              @PathVariable("caseTimes") Integer caseTimes) {
        LogUtil.audit("#查询医院信息#入参#reprotNo ={},caseTimes={}",reportNo,caseTimes);

        return ResponseResult.success(personHospitalService.getPersonHospital(reportNo,caseTimes,null));
    }

    @PostMapping("/syncBillInfo")
    public ResponseResult syncBillInfo(@RequestBody SyncBillParamDTO syncBillParamDTO){
        if(StringUtils.isEmptyStr(syncBillParamDTO.getReportNo())){
            throw new GlobalBusinessException("报案号不能为空");
        }
        if(null == syncBillParamDTO.getCaseTimes()){
            throw new GlobalBusinessException("赔付次数不能为空");
        }
        medicalBillService.syncBillInfo(syncBillParamDTO);
        return ResponseResult.success();
    }

    /**
     * 查询保单历史报案发票信息合理费用总和
     * @param reportNo
     * @return
     */
    @GetMapping(value = "/getPolicyBillHistory/{reportNo}")
    public ResponseResult<Object> getPolicyBillHistory(@PathVariable("reportNo") String reportNo) {
        LogUtil.audit("#getPolicyBillHistory#入参#reprotNo ={},caseTimes={}",reportNo);

        return ResponseResult.success(medicalBillService.getPolicyHistoryBillInfo(reportNo));
    }

}
